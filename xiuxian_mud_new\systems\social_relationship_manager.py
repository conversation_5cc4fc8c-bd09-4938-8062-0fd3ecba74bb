"""
仙侠社交关系管理系统

基于TagProperty和Attributes实现的社交关系管理，包括：
- 好友关系管理
- 仇敌关系管理
- 联盟关系管理
- 声望和影响力系统

完全基于Evennia原生组件，与现有系统完美集成。
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from evennia.utils import logger
from evennia import search


class RelationshipType(Enum):
    """关系类型枚举"""
    FRIEND = "friend"           # 好友
    ENEMY = "enemy"             # 仇敌
    ALLY = "ally"               # 盟友
    NEUTRAL = "neutral"         # 中立
    SPOUSE = "spouse"           # 配偶
    RIVAL = "rival"             # 竞争对手
    BENEFACTOR = "benefactor"   # 恩人
    DEBTOR = "debtor"           # 债务人


class RelationshipStrength(Enum):
    """关系强度枚举"""
    WEAK = 1        # 微弱
    MODERATE = 2    # 一般
    STRONG = 3      # 强烈
    INTENSE = 4     # 激烈
    EXTREME = 5     # 极端


class SocialRelationshipManager:
    """
    社交关系管理器
    
    提供完整的社交关系管理功能：
    - 基于Tags的关系标记（高性能查询）
    - 基于Attributes的详细关系数据
    - 关系强度和历史记录
    - 声望和影响力计算
    """
    
    @staticmethod
    def establish_relationship(character1, character2, relationship_type: RelationshipType,
                             strength: RelationshipStrength = RelationshipStrength.MODERATE,
                             **kwargs) -> bool:
        """
        建立角色间关系
        
        Args:
            character1: 角色1
            character2: 角色2
            relationship_type: 关系类型
            strength: 关系强度
            **kwargs: 额外的关系数据
            
        Returns:
            bool: 建立是否成功
        """
        try:
            if character1.id == character2.id:
                logger.log_warn("不能与自己建立关系")
                return False
            
            # 检查是否已存在关系
            existing = SocialRelationshipManager.get_relationship(character1, character2)
            if existing:
                logger.log_warn(f"{character1.key} 和 {character2.key} 已存在关系: {existing['type']}")
                return False
            
            # 使用Tags标记关系（用于高性能查询）
            tag_name = f"{relationship_type.value}_{character2.id}"
            character1.tags.add(tag_name, category="relationships")
            
            # 对于双向关系，也为character2添加标签
            if relationship_type in [RelationshipType.FRIEND, RelationshipType.ALLY, RelationshipType.SPOUSE]:
                reverse_tag = f"{relationship_type.value}_{character1.id}"
                character2.tags.add(reverse_tag, category="relationships")
            
            # 使用Attributes存储详细关系数据
            SocialRelationshipManager._store_relationship_data(
                character1, character2, relationship_type, strength, **kwargs
            )
            
            # 发布关系建立事件
            from systems.social_events import publish_relationship_event, SocialEventType
            event_type = SocialEventType.FRIENDSHIP_ESTABLISHED.value if relationship_type == RelationshipType.FRIEND else "relationship_established"
            
            publish_relationship_event(
                character1,
                character2,
                event_type,
                relationship_type.value,
                strength=strength.value,
                establishment_time=time.time(),
                **kwargs
            )
            
            logger.log_info(f"关系建立: {character1.key} -> {character2.key} ({relationship_type.value})")
            return True
            
        except Exception as e:
            logger.log_err(f"建立关系失败: {e}")
            return False
    
    @staticmethod
    def _store_relationship_data(character1, character2, relationship_type: RelationshipType,
                               strength: RelationshipStrength, **kwargs):
        """存储关系详细数据"""
        # 获取现有关系列表
        relationships = character1.attributes.get("relationships", {}, category="social")
        
        # 创建关系数据
        relationship_data = {
            "target_id": character2.id,
            "target_name": character2.key,
            "type": relationship_type.value,
            "strength": strength.value,
            "start_time": time.time(),
            "last_interaction": time.time(),
            "interaction_count": 0,
            "history": [],
            "notes": kwargs.get("notes", ""),
            "metadata": kwargs
        }
        
        relationships[str(character2.id)] = relationship_data
        character1.attributes.add("relationships", relationships, category="social")
    
    @staticmethod
    def remove_relationship(character1, character2, reason="关系解除") -> bool:
        """
        移除角色间关系
        
        Args:
            character1: 角色1
            character2: 角色2
            reason: 移除原因
            
        Returns:
            bool: 移除是否成功
        """
        try:
            # 获取现有关系
            relationship = SocialRelationshipManager.get_relationship(character1, character2)
            if not relationship:
                logger.log_warn(f"{character1.key} 和 {character2.key} 之间没有关系")
                return False
            
            relationship_type = relationship["type"]
            
            # 移除Tags标记
            tag_name = f"{relationship_type}_{character2.id}"
            character1.tags.remove(tag_name, category="relationships")
            
            # 对于双向关系，也移除character2的标签
            if relationship_type in ["friend", "ally", "spouse"]:
                reverse_tag = f"{relationship_type}_{character1.id}"
                character2.tags.remove(reverse_tag, category="relationships")
            
            # 移除Attributes数据
            relationships = character1.attributes.get("relationships", {}, category="social")
            if str(character2.id) in relationships:
                del relationships[str(character2.id)]
                character1.attributes.add("relationships", relationships, category="social")
            
            # 发布关系解除事件
            from systems.social_events import publish_relationship_event, SocialEventType
            event_type = SocialEventType.FRIENDSHIP_BROKEN.value if relationship_type == "friend" else "relationship_removed"
            
            publish_relationship_event(
                character1,
                character2,
                event_type,
                relationship_type,
                reason=reason,
                removal_time=time.time()
            )
            
            logger.log_info(f"关系移除: {character1.key} -> {character2.key} ({relationship_type}, 原因: {reason})")
            return True
            
        except Exception as e:
            logger.log_err(f"移除关系失败: {e}")
            return False
    
    @staticmethod
    def get_relationship(character1, character2) -> Optional[Dict[str, Any]]:
        """获取两个角色间的关系"""
        try:
            relationships = character1.attributes.get("relationships", {}, category="social")
            return relationships.get(str(character2.id))
        except Exception as e:
            logger.log_err(f"获取关系失败: {e}")
            return None
    
    @staticmethod
    def get_all_relationships(character) -> Dict[str, Dict[str, Any]]:
        """获取角色的所有关系"""
        try:
            return character.attributes.get("relationships", {}, category="social")
        except Exception as e:
            logger.log_err(f"获取关系列表失败: {e}")
            return {}
    
    @staticmethod
    def get_relationships_by_type(character, relationship_type: RelationshipType) -> List[Dict[str, Any]]:
        """获取指定类型的关系"""
        relationships = []
        try:
            all_relationships = SocialRelationshipManager.get_all_relationships(character)
            for rel_data in all_relationships.values():
                if rel_data.get("type") == relationship_type.value:
                    relationships.append(rel_data)
        except Exception as e:
            logger.log_err(f"获取指定类型关系失败: {e}")
        
        return relationships
    
    @staticmethod
    def update_relationship_strength(character1, character2, new_strength: RelationshipStrength,
                                   reason="关系变化") -> bool:
        """
        更新关系强度
        
        Args:
            character1: 角色1
            character2: 角色2
            new_strength: 新的关系强度
            reason: 变化原因
            
        Returns:
            bool: 更新是否成功
        """
        try:
            relationships = character1.attributes.get("relationships", {}, category="social")
            rel_key = str(character2.id)
            
            if rel_key not in relationships:
                logger.log_warn(f"{character1.key} 和 {character2.key} 之间没有关系")
                return False
            
            old_strength = relationships[rel_key]["strength"]
            relationships[rel_key]["strength"] = new_strength.value
            relationships[rel_key]["last_interaction"] = time.time()
            
            # 添加历史记录
            history_entry = {
                "timestamp": time.time(),
                "action": "strength_change",
                "old_strength": old_strength,
                "new_strength": new_strength.value,
                "reason": reason
            }
            relationships[rel_key]["history"].append(history_entry)
            
            character1.attributes.add("relationships", relationships, category="social")
            
            logger.log_info(f"关系强度更新: {character1.key} -> {character2.key} ({old_strength} -> {new_strength.value})")
            return True
            
        except Exception as e:
            logger.log_err(f"更新关系强度失败: {e}")
            return False
    
    @staticmethod
    def record_interaction(character1, character2, interaction_type: str, details: str = "") -> bool:
        """
        记录角色间互动
        
        Args:
            character1: 角色1
            character2: 角色2
            interaction_type: 互动类型
            details: 互动详情
            
        Returns:
            bool: 记录是否成功
        """
        try:
            relationships = character1.attributes.get("relationships", {}, category="social")
            rel_key = str(character2.id)
            
            if rel_key not in relationships:
                # 如果没有关系，创建一个中立关系
                SocialRelationshipManager.establish_relationship(
                    character1, character2, RelationshipType.NEUTRAL
                )
                relationships = character1.attributes.get("relationships", {}, category="social")
            
            # 更新互动数据
            relationships[rel_key]["last_interaction"] = time.time()
            relationships[rel_key]["interaction_count"] += 1
            
            # 添加互动历史
            interaction_entry = {
                "timestamp": time.time(),
                "type": interaction_type,
                "details": details
            }
            relationships[rel_key]["history"].append(interaction_entry)
            
            # 保持历史记录在合理范围内
            if len(relationships[rel_key]["history"]) > 50:
                relationships[rel_key]["history"] = relationships[rel_key]["history"][-50:]
            
            character1.attributes.add("relationships", relationships, category="social")
            
            return True
            
        except Exception as e:
            logger.log_err(f"记录互动失败: {e}")
            return False


class ReputationManager:
    """
    声望管理器
    
    管理角色的声望和影响力系统
    """
    
    @staticmethod
    def get_reputation(character) -> int:
        """获取角色声望值"""
        return character.attributes.get("reputation", 0, category="social")
    
    @staticmethod
    def modify_reputation(character, change: int, reason: str, **kwargs) -> bool:
        """
        修改角色声望
        
        Args:
            character: 角色
            change: 声望变化值
            reason: 变化原因
            **kwargs: 额外数据
            
        Returns:
            bool: 修改是否成功
        """
        try:
            current_reputation = ReputationManager.get_reputation(character)
            new_reputation = current_reputation + change
            
            # 设置声望范围限制
            new_reputation = max(-10000, min(10000, new_reputation))
            
            character.attributes.add("reputation", new_reputation, category="social")
            
            # 记录声望变化历史
            reputation_history = character.attributes.get("reputation_history", [], category="social")
            history_entry = {
                "timestamp": time.time(),
                "change": change,
                "reason": reason,
                "old_value": current_reputation,
                "new_value": new_reputation,
                "metadata": kwargs
            }
            reputation_history.append(history_entry)
            
            # 保持历史记录在合理范围内
            if len(reputation_history) > 100:
                reputation_history = reputation_history[-100:]
            
            character.attributes.add("reputation_history", reputation_history, category="social")
            
            # 发布声望变化事件
            from systems.social_events import publish_reputation_event
            publish_reputation_event(
                character,
                change,
                reason,
                current_reputation=new_reputation,
                reputation_level=ReputationManager.get_reputation_level(new_reputation),
                **kwargs
            )
            
            logger.log_info(f"声望变化: {character.key} {change:+d} ({reason}) -> {new_reputation}")
            return True
            
        except Exception as e:
            logger.log_err(f"修改声望失败: {e}")
            return False
    
    @staticmethod
    def get_reputation_level(reputation: int) -> str:
        """根据声望值获取声望等级"""
        if reputation >= 5000:
            return "德高望重"
        elif reputation >= 2000:
            return "声名远扬"
        elif reputation >= 1000:
            return "小有名气"
        elif reputation >= 500:
            return "略有声望"
        elif reputation >= 100:
            return "初露头角"
        elif reputation >= -100:
            return "无名之辈"
        elif reputation >= -500:
            return "声名狼藉"
        elif reputation >= -1000:
            return "臭名昭著"
        elif reputation >= -2000:
            return "人人喊打"
        else:
            return "万恶不赦"
    
    @staticmethod
    def get_reputation_info(character) -> Dict[str, Any]:
        """获取角色声望详细信息"""
        reputation = ReputationManager.get_reputation(character)
        level = ReputationManager.get_reputation_level(reputation)
        history = character.attributes.get("reputation_history", [], category="social")
        
        return {
            "value": reputation,
            "level": level,
            "history_count": len(history),
            "recent_changes": history[-5:] if history else []
        }
    
    @staticmethod
    def get_top_reputation_characters(limit: int = 10) -> List[Dict[str, Any]]:
        """获取声望排行榜"""
        try:
            # 这里需要遍历所有角色，在实际应用中可能需要优化
            from evennia import search
            all_characters = search.search_object_tag("character", category="typeclass")
            
            reputation_list = []
            for char in all_characters:
                if hasattr(char, 'attributes'):
                    reputation = ReputationManager.get_reputation(char)
                    reputation_list.append({
                        "character": char,
                        "name": char.key,
                        "reputation": reputation,
                        "level": ReputationManager.get_reputation_level(reputation)
                    })
            
            # 按声望排序
            reputation_list.sort(key=lambda x: x["reputation"], reverse=True)
            return reputation_list[:limit]
            
        except Exception as e:
            logger.log_err(f"获取声望排行榜失败: {e}")
            return []


# 便利函数

def add_friend(character1, character2, **kwargs) -> bool:
    """添加好友关系"""
    return SocialRelationshipManager.establish_relationship(
        character1, character2, RelationshipType.FRIEND, **kwargs
    )


def add_enemy(character1, character2, **kwargs) -> bool:
    """添加仇敌关系"""
    return SocialRelationshipManager.establish_relationship(
        character1, character2, RelationshipType.ENEMY, **kwargs
    )


def remove_friend(character1, character2, reason="友谊破裂") -> bool:
    """移除好友关系"""
    return SocialRelationshipManager.remove_relationship(character1, character2, reason)


def get_friends(character) -> List[Dict[str, Any]]:
    """获取好友列表"""
    return SocialRelationshipManager.get_relationships_by_type(character, RelationshipType.FRIEND)


def get_enemies(character) -> List[Dict[str, Any]]:
    """获取仇敌列表"""
    return SocialRelationshipManager.get_relationships_by_type(character, RelationshipType.ENEMY)
