{% comment %}
角色状态组件模板

功能：
- 显示角色基本信息
- 修仙境界和门派
- 属性数值展示
- 实时状态更新

数据来源：
- account.character (角色对象)
- character.db.* (角色属性)
- character.tags.* (角色标签)
{% endcomment %}

<div class="character-status" id="character-status">
    {% if account and account.character %}
        <!-- 角色基本信息 -->
        <div class="character-status__header">
            <div class="character-status__name" id="character-name">
                {{ account.character.name|default:"无名修士" }}
            </div>
            <div class="character-status__level" id="character-level">
                {{ account.character.db.level|default:"1" }}级
            </div>
        </div>
        
        <!-- 修仙境界 -->
        <div class="character-status__realm" id="character-realm">
            {{ account.character.db.realm|default:"凡人境界" }}
        </div>
        
        <!-- 门派信息 -->
        {% if account.character.db.sect %}
        <div class="character-status__sect" id="character-sect">
            <div class="character-status__sect-icon"></div>
            <span>{{ account.character.db.sect }}</span>
        </div>
        {% endif %}
        
        <!-- 角色属性 -->
        <div class="character-status__stats" id="character-stats">
            <!-- 生命值 -->
            <div class="character-status__stat" data-stat="hp">
                <span class="character-status__stat-label">生命</span>
                <span class="character-status__stat-value" id="stat-hp">
                    {{ account.character.db.hp|default:"100" }}/{{ account.character.db.max_hp|default:"100" }}
                </span>
            </div>
            
            <!-- 法力值 -->
            <div class="character-status__stat" data-stat="mp">
                <span class="character-status__stat-label">法力</span>
                <span class="character-status__stat-value" id="stat-mp">
                    {{ account.character.db.mp|default:"50" }}/{{ account.character.db.max_mp|default:"50" }}
                </span>
            </div>
            
            <!-- 体力值 -->
            <div class="character-status__stat" data-stat="stamina">
                <span class="character-status__stat-label">体力</span>
                <span class="character-status__stat-value" id="stat-stamina">
                    {{ account.character.db.stamina|default:"100" }}/{{ account.character.db.max_stamina|default:"100" }}
                </span>
            </div>
            
            <!-- 修为 -->
            <div class="character-status__stat" data-stat="cultivation">
                <span class="character-status__stat-label">修为</span>
                <span class="character-status__stat-value" id="stat-cultivation">
                    {{ account.character.db.cultivation|default:"0" }}
                </span>
            </div>
            
            <!-- 力量 -->
            <div class="character-status__stat" data-stat="strength">
                <span class="character-status__stat-label">力量</span>
                <span class="character-status__stat-value" id="stat-strength">
                    {{ account.character.db.strength|default:"10" }}
                </span>
            </div>
            
            <!-- 敏捷 -->
            <div class="character-status__stat" data-stat="agility">
                <span class="character-status__stat-label">敏捷</span>
                <span class="character-status__stat-value" id="stat-agility">
                    {{ account.character.db.agility|default:"10" }}
                </span>
            </div>
            
            <!-- 智力 -->
            <div class="character-status__stat" data-stat="intelligence">
                <span class="character-status__stat-label">智力</span>
                <span class="character-status__stat-value" id="stat-intelligence">
                    {{ account.character.db.intelligence|default:"10" }}
                </span>
            </div>
            
            <!-- 体质 -->
            <div class="character-status__stat" data-stat="constitution">
                <span class="character-status__stat-label">体质</span>
                <span class="character-status__stat-value" id="stat-constitution">
                    {{ account.character.db.constitution|default:"10" }}
                </span>
            </div>
        </div>
        
        <!-- 状态效果 -->
        {% if account.character.db.status_effects %}
        <div class="character-status__effects" id="character-effects">
            {% for effect in account.character.db.status_effects %}
            <div class="character-status__effect" data-effect="{{ effect.type }}">
                <span class="character-status__effect-name">{{ effect.name }}</span>
                {% if effect.duration %}
                <span class="character-status__effect-duration">{{ effect.duration }}s</span>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
    {% else %}
        <!-- 未登录状态 -->
        <div class="character-status character-status--guest">
            <div class="character-status__header">
                <div class="character-status__name">访客</div>
                <div class="character-status__level">-</div>
            </div>
            
            <div class="character-status__realm">未入门</div>
            
            <div class="character-status__message">
                <p>请登录以查看角色状态</p>
                <div class="character-status__login-hint">
                    <small>输入 <code>connect 用户名 密码</code> 登录</small>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- 角色状态更新脚本 -->
<script>
(function() {
    'use strict';
    
    // 角色状态管理器
    window.CharacterStatus = {
        // 初始化
        init: function() {
            this.bindEvents();
            this.startUpdateTimer();
        },
        
        // 绑定事件
        bindEvents: function() {
            // 监听WebSocket消息
            if (window.Evennia && window.Evennia.msg) {
                const originalMsg = window.Evennia.msg;
                window.Evennia.msg = function(data) {
                    originalMsg.call(this, data);
                    CharacterStatus.handleMessage(data);
                };
            }
        },
        
        // 处理消息更新
        handleMessage: function(data) {
            if (data.type === 'character_update' || data.type === 'stats_update') {
                this.updateStats(data.stats || data.character);
            }
        },
        
        // 更新角色属性
        updateStats: function(stats) {
            if (!stats) return;
            
            // 更新各项属性
            Object.keys(stats).forEach(function(key) {
                const element = document.getElementById('stat-' + key);
                if (element) {
                    if (typeof stats[key] === 'object' && stats[key].current !== undefined) {
                        // 当前值/最大值格式
                        element.textContent = stats[key].current + '/' + stats[key].max;
                        
                        // 更新进度条（如果存在）
                        const progressBar = element.parentElement.querySelector('.stat-progress');
                        if (progressBar) {
                            const percentage = (stats[key].current / stats[key].max) * 100;
                            progressBar.style.width = percentage + '%';
                        }
                    } else {
                        // 简单数值
                        element.textContent = stats[key];
                    }
                    
                    // 添加更新动画
                    element.parentElement.classList.add('stat-updated');
                    setTimeout(function() {
                        element.parentElement.classList.remove('stat-updated');
                    }, 1000);
                }
            });
            
            // 更新境界
            if (stats.realm) {
                const realmElement = document.getElementById('character-realm');
                if (realmElement) {
                    realmElement.textContent = stats.realm;
                    realmElement.classList.add('realm-updated');
                    setTimeout(function() {
                        realmElement.classList.remove('realm-updated');
                    }, 2000);
                }
            }
            
            // 更新等级
            if (stats.level) {
                const levelElement = document.getElementById('character-level');
                if (levelElement) {
                    levelElement.textContent = stats.level + '级';
                }
            }
        },
        
        // 定时更新
        startUpdateTimer: function() {
            // 每30秒请求一次状态更新
            setInterval(function() {
                if (window.Evennia && window.Evennia.msg) {
                    window.Evennia.msg({
                        type: 'request_stats_update'
                    });
                }
            }, 30000);
        },
        
        // 手动刷新状态
        refresh: function() {
            if (window.Evennia && window.Evennia.msg) {
                window.Evennia.msg({
                    type: 'request_stats_update'
                });
            }
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            CharacterStatus.init();
        });
    } else {
        CharacterStatus.init();
    }
})();
</script>

<style>
/* 角色状态组件特定样式 */
.character-status--guest {
    text-align: center;
    opacity: 0.7;
}

.character-status__message {
    padding: 20px 10px;
    color: var(--xiuxian-text-secondary);
}

.character-status__login-hint {
    margin-top: 10px;
    padding: 8px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 6px;
    border: 1px solid var(--xiuxian-border-light);
}

.character-status__login-hint code {
    background: var(--xiuxian-bg-tertiary);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    color: var(--xiuxian-primary);
}

/* 状态更新动画 */
.stat-updated {
    animation: statUpdate 1s ease;
}

@keyframes statUpdate {
    0% { background: rgba(212, 175, 55, 0.3); }
    100% { background: transparent; }
}

.realm-updated {
    animation: realmUpdate 2s ease;
}

@keyframes realmUpdate {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: var(--xiuxian-shadow-glow); }
}

/* 状态效果 */
.character-status__effects {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.character-status__effect {
    background: var(--xiuxian-gradient-accent);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7em;
    display: flex;
    align-items: center;
    gap: 4px;
}

.character-status__effect-duration {
    background: rgba(255, 255, 255, 0.2);
    padding: 1px 4px;
    border-radius: 6px;
    font-size: 0.9em;
}

/* 属性进度条 */
.character-status__stat {
    position: relative;
    overflow: hidden;
}

.stat-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: var(--xiuxian-gradient-gold);
    transition: width 0.5s ease;
    border-radius: 1px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .character-status__stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .character-status__stat {
        padding: 6px;
    }
    
    .character-status__stat-label {
        font-size: 0.7em;
    }
    
    .character-status__stat-value {
        font-size: 1em;
    }
}
</style>
