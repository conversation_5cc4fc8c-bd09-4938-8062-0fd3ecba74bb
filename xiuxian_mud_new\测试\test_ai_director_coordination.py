"""
AI导演协调性测试

测试三层AI导演系统的协调机制：
- 天道导演(5分钟周期)与地灵导演(1分钟周期)协调
- 地灵导演与器灵导演(10秒周期)协调
- 跨层级指令传递和执行
- 决策冲突解决机制
- 负载均衡和优先级处理
"""

import time
import threading
import unittest
from unittest.mock import Mock, patch

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import (
    XianxiaIntegrationTest, create_test_character, create_test_room, 
    create_test_npc, cleanup_test_objects
)
from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..scripts.ai_directors.director_initialization import initialize_director_system


class AIDirectorHierarchyTest(XianxiaIntegrationTest):
    """AI导演层级协调测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试环境
        self.test_room = create_test_room("director_test_room")
        self.test_char = create_test_character("director_test_char", self.test_room)
        self.test_npc = create_test_npc("director_test_npc", self.test_room)
        
        # 初始化AI导演系统
        self.directors = initialize_director_system()
        
        # 等待系统初始化
        time.sleep(3)
        
        # 获取各层导演引用
        self.tiandao = search_script("tiandao_director_script")[0] if search_script("tiandao_director_script") else None
        self.diling = search_script("diling_director_script")[0] if search_script("diling_director_script") else None
        self.qiling = search_script("qiling_director_script")[0] if search_script("qiling_director_script") else None
        
        self.assertTrue(self.tiandao, "天道导演未找到")
        self.assertTrue(self.diling, "地灵导演未找到")
        self.assertTrue(self.qiling, "器灵导演未找到")
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_tiandao_to_diling_coordination(self):
        """测试天道导演到地灵导演的协调"""
        log_info("开始测试天道-地灵导演协调")
        
        # 1. 天道导演发出世界级指令
        world_directive = {
            "directive_type": "spiritual_energy_adjustment",
            "target_regions": ["test_region"],
            "adjustment_type": "increase",
            "intensity": "moderate",
            "duration": 3600,  # 1小时
            "reason": "seasonal_change"
        }
        
        # 模拟天道导演决策
        if hasattr(self.tiandao, 'issue_world_directive'):
            self.tiandao.issue_world_directive(world_directive)
        else:
            # 通过事件系统发送指令
            directive_event = BaseEvent(
                event_type="tiandao_directive",
                event_data=world_directive,
                priority="HIGH"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(directive_event)
        
        # 2. 等待地灵导演响应
        time.sleep(3)
        
        # 3. 验证地灵导演是否接收并执行指令
        if hasattr(self.diling, 'received_directives'):
            received = self.diling.received_directives
            tiandao_directives = [d for d in received if d.get('source') == 'tiandao']
            self.assertTrue(tiandao_directives, "地灵导演未接收到天道指令")
            
            # 验证指令内容
            latest_directive = tiandao_directives[-1]
            self.assertEqual(latest_directive.get('directive_type'), 'spiritual_energy_adjustment')
        
        # 4. 验证地灵导演的执行结果
        if hasattr(self.diling, 'execution_results'):
            results = self.diling.execution_results
            energy_adjustments = [r for r in results if r.get('action') == 'spiritual_energy_adjustment']
            self.assertTrue(energy_adjustments, "地灵导演未执行灵气调整")
        
        log_info("天道-地灵导演协调测试完成")
    
    def test_diling_to_qiling_coordination(self):
        """测试地灵导演到器灵导演的协调"""
        log_info("开始测试地灵-器灵导演协调")
        
        # 1. 地灵导演发出区域级指令
        regional_directive = {
            "directive_type": "npc_behavior_adjustment",
            "target_npcs": [self.test_npc.id],
            "behavior_change": "increased_friendliness",
            "trigger_conditions": ["player_approach"],
            "duration": 1800,  # 30分钟
            "reason": "positive_regional_energy"
        }
        
        # 模拟地灵导演决策
        if hasattr(self.diling, 'issue_regional_directive'):
            self.diling.issue_regional_directive(regional_directive)
        else:
            # 通过事件系统发送指令
            directive_event = BaseEvent(
                event_type="diling_directive",
                event_data=regional_directive,
                priority="MEDIUM"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(directive_event)
        
        # 2. 等待器灵导演响应
        time.sleep(2)
        
        # 3. 验证器灵导演是否接收并执行指令
        if hasattr(self.qiling, 'received_directives'):
            received = self.qiling.received_directives
            diling_directives = [d for d in received if d.get('source') == 'diling']
            self.assertTrue(diling_directives, "器灵导演未接收到地灵指令")
            
            # 验证指令内容
            latest_directive = diling_directives[-1]
            self.assertEqual(latest_directive.get('directive_type'), 'npc_behavior_adjustment')
        
        # 4. 验证NPC行为是否实际改变
        if hasattr(self.test_npc, 'current_behavior_modifiers'):
            modifiers = self.test_npc.current_behavior_modifiers
            friendliness_modifiers = [m for m in modifiers if m.get('type') == 'increased_friendliness']
            self.assertTrue(friendliness_modifiers, "NPC行为未按指令调整")
        
        log_info("地灵-器灵导演协调测试完成")
    
    def test_cross_layer_communication(self):
        """测试跨层级通信"""
        log_info("开始测试跨层级通信")
        
        # 1. 器灵导演向上级报告紧急情况
        emergency_report = {
            "report_type": "emergency_situation",
            "situation": "player_in_danger",
            "character_id": self.test_char.id,
            "danger_level": "high",
            "location": self.test_room.id,
            "immediate_action_needed": True
        }
        
        # 器灵导演上报紧急情况
        if hasattr(self.qiling, 'report_to_higher_level'):
            self.qiling.report_to_higher_level(emergency_report)
        else:
            # 通过事件系统上报
            report_event = BaseEvent(
                event_type="qiling_emergency_report",
                event_data=emergency_report,
                priority="CRITICAL"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(report_event)
        
        # 2. 等待上级响应
        time.sleep(3)
        
        # 3. 验证地灵导演是否接收到报告
        if hasattr(self.diling, 'received_reports'):
            reports = self.diling.received_reports
            emergency_reports = [r for r in reports if r.get('report_type') == 'emergency_situation']
            self.assertTrue(emergency_reports, "地灵导演未接收到紧急报告")
        
        # 4. 验证天道导演是否被通知（如果情况足够严重）
        if hasattr(self.tiandao, 'emergency_notifications'):
            notifications = self.tiandao.emergency_notifications
            high_level_emergencies = [n for n in notifications if n.get('danger_level') == 'high']
            # 高危情况应该通知到天道导演
            
        log_info("跨层级通信测试完成")
    
    def test_decision_conflict_resolution(self):
        """测试决策冲突解决"""
        log_info("开始测试决策冲突解决")
        
        # 1. 创建冲突场景：不同层级的导演对同一事件有不同决策
        
        # 天道导演决策：降低区域灵气（因为整体平衡）
        tiandao_decision = {
            "decision_type": "spiritual_energy_adjustment",
            "target": self.test_room.id,
            "action": "decrease",
            "intensity": "moderate",
            "priority": "world_balance"
        }
        
        # 地灵导演决策：提高区域灵气（因为本地需求）
        diling_decision = {
            "decision_type": "spiritual_energy_adjustment", 
            "target": self.test_room.id,
            "action": "increase",
            "intensity": "moderate",
            "priority": "local_optimization"
        }
        
        # 2. 同时发出冲突决策
        if hasattr(self.tiandao, 'make_decision'):
            self.tiandao.make_decision(tiandao_decision)
        
        if hasattr(self.diling, 'make_decision'):
            self.diling.make_decision(diling_decision)
        
        # 3. 等待冲突解决
        time.sleep(4)
        
        # 4. 验证冲突解决机制
        if hasattr(self.diling, 'conflict_resolutions'):
            resolutions = self.diling.conflict_resolutions
            energy_conflicts = [r for r in resolutions if r.get('conflict_type') == 'spiritual_energy_adjustment']
            self.assertTrue(energy_conflicts, "未检测到决策冲突")
            
            # 验证解决方案（应该优先天道导演的决策）
            latest_resolution = energy_conflicts[-1]
            self.assertEqual(latest_resolution.get('final_decision'), 'tiandao_priority')
        
        log_info("决策冲突解决测试完成")


class AIDirectorLoadBalancingTest(XianxiaIntegrationTest):
    """AI导演负载均衡测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建多个测试对象以产生负载
        self.test_rooms = []
        self.test_chars = []
        self.test_npcs = []
        
        for i in range(10):
            room = create_test_room(f"load_test_room_{i}")
            char = create_test_character(f"load_test_char_{i}", room)
            npc = create_test_npc(f"load_test_npc_{i}", room)
            
            self.test_rooms.append(room)
            self.test_chars.append(char)
            self.test_npcs.append(npc)
        
        # 初始化AI导演系统
        self.directors = initialize_director_system()
        time.sleep(3)
        
        # 获取导演引用
        self.tiandao = search_script("tiandao_director_script")[0] if search_script("tiandao_director_script") else None
        self.diling = search_script("diling_director_script")[0] if search_script("diling_director_script") else None
        self.qiling = search_script("qiling_director_script")[0] if search_script("qiling_director_script") else None
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_high_load_decision_making(self):
        """测试高负载下的决策制定"""
        log_info("开始测试高负载决策制定")
        
        # 1. 生成大量并发事件
        events = []
        for i in range(50):
            event = BaseEvent(
                event_type="high_load_test_event",
                event_data={
                    "event_id": i,
                    "character_id": self.test_chars[i % len(self.test_chars)].id,
                    "npc_id": self.test_npcs[i % len(self.test_npcs)].id,
                    "location": self.test_rooms[i % len(self.test_rooms)].id,
                    "complexity": "high"
                },
                priority="MEDIUM"
            )
            events.append(event)
        
        # 2. 快速发布所有事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            start_time = time.time()
            for event in events:
                event_bus.publish_event(event)
                time.sleep(0.01)  # 很短的间隔
            
            # 3. 等待处理完成
            time.sleep(10)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # 4. 验证处理性能
            self.assertLess(processing_time, 15, f"高负载处理时间过长: {processing_time}秒")
            
            # 5. 验证决策质量
            if hasattr(self.qiling, 'decision_quality_metrics'):
                metrics = self.qiling.decision_quality_metrics
                avg_quality = sum(metrics) / len(metrics) if metrics else 0
                self.assertGreater(avg_quality, 0.7, f"高负载下决策质量下降: {avg_quality}")
        
        log_info("高负载决策制定测试完成")
    
    def test_priority_based_processing(self):
        """测试基于优先级的处理"""
        log_info("开始测试优先级处理")
        
        # 1. 创建不同优先级的事件
        high_priority_event = BaseEvent(
            event_type="critical_emergency",
            event_data={"emergency_type": "player_death_risk"},
            priority="CRITICAL"
        )
        
        medium_priority_event = BaseEvent(
            event_type="normal_interaction",
            event_data={"interaction_type": "npc_dialogue"},
            priority="MEDIUM"
        )
        
        low_priority_event = BaseEvent(
            event_type="background_maintenance",
            event_data={"maintenance_type": "cleanup"},
            priority="LOW"
        )
        
        # 2. 按相反顺序发布（低优先级先发布）
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            # 记录发布时间
            publish_times = {}
            
            event_bus.publish_event(low_priority_event)
            publish_times["low"] = time.time()
            
            time.sleep(0.1)
            
            event_bus.publish_event(medium_priority_event)
            publish_times["medium"] = time.time()
            
            time.sleep(0.1)
            
            event_bus.publish_event(high_priority_event)
            publish_times["high"] = time.time()
            
            # 3. 等待处理
            time.sleep(5)
            
            # 4. 验证处理顺序
            if hasattr(event_bus, 'processing_log'):
                log = event_bus.processing_log
                
                # 查找各优先级事件的处理时间
                high_processed = None
                medium_processed = None
                low_processed = None
                
                for entry in log:
                    if entry.get('event_type') == 'critical_emergency':
                        high_processed = entry.get('processed_time')
                    elif entry.get('event_type') == 'normal_interaction':
                        medium_processed = entry.get('processed_time')
                    elif entry.get('event_type') == 'background_maintenance':
                        low_processed = entry.get('processed_time')
                
                # 验证高优先级事件被优先处理
                if high_processed and medium_processed:
                    self.assertLess(high_processed, medium_processed, "高优先级事件未被优先处理")
                
                if medium_processed and low_processed:
                    self.assertLess(medium_processed, low_processed, "中优先级事件未被优先处理")
        
        log_info("优先级处理测试完成")
    
    def test_director_workload_distribution(self):
        """测试导演工作负载分配"""
        log_info("开始测试工作负载分配")
        
        # 1. 生成不同类型的工作负载
        world_events = []  # 天道导演处理
        regional_events = []  # 地灵导演处理
        individual_events = []  # 器灵导演处理
        
        for i in range(20):
            # 世界级事件
            world_event = BaseEvent(
                event_type="world_level_event",
                event_data={"scope": "global", "event_id": f"world_{i}"},
                priority="HIGH"
            )
            world_events.append(world_event)
            
            # 区域级事件
            regional_event = BaseEvent(
                event_type="regional_level_event", 
                event_data={"scope": "regional", "event_id": f"regional_{i}"},
                priority="MEDIUM"
            )
            regional_events.append(regional_event)
            
            # 个体级事件
            individual_event = BaseEvent(
                event_type="individual_level_event",
                event_data={"scope": "individual", "event_id": f"individual_{i}"},
                priority="LOW"
            )
            individual_events.append(individual_event)
        
        # 2. 发布所有事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            all_events = world_events + regional_events + individual_events
            for event in all_events:
                event_bus.publish_event(event)
                time.sleep(0.05)
            
            # 3. 等待处理完成
            time.sleep(15)
            
            # 4. 验证负载分配
            tiandao_workload = 0
            diling_workload = 0
            qiling_workload = 0
            
            if hasattr(self.tiandao, 'processed_events_count'):
                tiandao_workload = self.tiandao.processed_events_count
            
            if hasattr(self.diling, 'processed_events_count'):
                diling_workload = self.diling.processed_events_count
            
            if hasattr(self.qiling, 'processed_events_count'):
                qiling_workload = self.qiling.processed_events_count
            
            # 验证工作负载合理分配
            total_workload = tiandao_workload + diling_workload + qiling_workload
            if total_workload > 0:
                # 器灵导演应该处理最多事件（个体级事件频繁）
                self.assertGreaterEqual(qiling_workload, diling_workload, "器灵导演工作负载分配不当")
                
                # 天道导演应该处理最少事件（世界级事件稀少但重要）
                self.assertLessEqual(tiandao_workload, diling_workload, "天道导演工作负载分配不当")
        
        log_info("工作负载分配测试完成")


if __name__ == '__main__':
    # 运行AI导演协调性测试
    unittest.main(verbosity=2)
