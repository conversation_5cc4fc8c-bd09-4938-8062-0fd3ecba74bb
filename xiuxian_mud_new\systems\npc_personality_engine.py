"""
NPC个性引擎系统

负责动态生成和管理NPC个性：
- 基于修为境界的对话风格
- 门派文化塑造的性格特征
- 五行属性影响的表达方式
- 角色类型决定的知识领域
- 动态个性调整算法
"""

import random
from typing import Dict, Any, List, Tuple
from evennia.utils import logger


class NPCPersonalityEngine:
    """NPC个性引擎"""
    
    # 修为境界对话风格映射
    REALM_DIALOGUE_STYLES = {
        "练气": {
            "tone": "谦逊、好学",
            "vocabulary": ["请教", "学习", "不懂", "请问"],
            "sentence_patterns": ["请问{topic}是什么意思？", "我还在学习{topic}"],
            "confidence_level": 0.3
        },
        "筑基": {
            "tone": "稍有自信但仍谦逊",
            "vocabulary": ["了解", "知道", "认为", "觉得"],
            "sentence_patterns": ["我觉得{topic}应该是...", "据我了解{topic}..."],
            "confidence_level": 0.5
        },
        "金丹": {
            "tone": "从容、自信",
            "vocabulary": ["确实", "当然", "自然", "必然"],
            "sentence_patterns": ["这{topic}确实如此", "自然是{topic}"],
            "confidence_level": 0.7
        },
        "元婴": {
            "tone": "深沉、睿智",
            "vocabulary": ["深知", "明了", "洞察", "领悟"],
            "sentence_patterns": ["老夫深知{topic}之理", "此{topic}之道..."],
            "confidence_level": 0.8
        },
        "化神": {
            "tone": "超然、淡泊",
            "vocabulary": ["悟得", "参透", "明悟", "洞悉"],
            "sentence_patterns": ["贫道已悟得{topic}真谛", "此{topic}乃天地至理"],
            "confidence_level": 0.9
        },
        "炼虚": {
            "tone": "高深莫测",
            "vocabulary": ["天机", "造化", "玄妙", "无上"],
            "sentence_patterns": ["此乃{topic}之天机", "{topic}玄妙无穷"],
            "confidence_level": 0.95
        },
        "合体": {
            "tone": "返璞归真",
            "vocabulary": ["本源", "根本", "本质", "真理"],
            "sentence_patterns": ["{topic}之本源在于...", "万法归一，{topic}亦然"],
            "confidence_level": 0.98
        },
        "大乘": {
            "tone": "如仙人般超脱",
            "vocabulary": ["大道", "至理", "无极", "太极"],
            "sentence_patterns": ["大道至简，{topic}亦如是", "{topic}乃无极之理"],
            "confidence_level": 0.99
        },
        "仙人": {
            "tone": "超凡脱俗",
            "vocabulary": ["天道", "仙机", "无量", "永恒"],
            "sentence_patterns": ["天道昭昭，{topic}自明", "{topic}乃仙机所在"],
            "confidence_level": 1.0
        }
    }
    
    # 门派文化特征
    SECT_CULTURAL_TRAITS = {
        "青云门": {
            "core_values": ["正直", "守序", "重义", "护道"],
            "speech_style": "正气凛然，言辞正直",
            "common_phrases": ["正道", "正义", "守护", "护佑"],
            "personality_modifiers": {"正直": 0.9, "守序": 0.8, "勇敢": 0.7}
        },
        "天音寺": {
            "core_values": ["慈悲", "宽容", "智慧", "度化"],
            "speech_style": "慈悲为怀，言语温和",
            "common_phrases": ["慈悲", "度化", "佛法", "善缘"],
            "personality_modifiers": {"慈悲": 0.9, "宽容": 0.8, "智慧": 0.7}
        },
        "鬼王宗": {
            "core_values": ["神秘", "强势", "独立", "自由"],
            "speech_style": "神秘莫测，言语犀利",
            "common_phrases": ["鬼道", "阴阳", "生死", "轮回"],
            "personality_modifiers": {"神秘": 0.9, "强势": 0.8, "独立": 0.7}
        },
        "焚香谷": {
            "core_values": ["优雅", "高贵", "细致", "精致"],
            "speech_style": "优雅高贵，言辞精致",
            "common_phrases": ["优雅", "精致", "品味", "格调"],
            "personality_modifiers": {"优雅": 0.9, "高贵": 0.8, "细致": 0.7}
        },
        "合欢派": {
            "core_values": ["魅惑", "灵活", "善变", "机智"],
            "speech_style": "魅惑动人，言语灵活",
            "common_phrases": ["魅惑", "灵动", "变化", "机缘"],
            "personality_modifiers": {"魅惑": 0.9, "灵活": 0.8, "机智": 0.7}
        },
        "无门派": {
            "core_values": ["自由", "独立", "随性", "不拘"],
            "speech_style": "自由随性，不拘一格",
            "common_phrases": ["自由", "随心", "不拘", "洒脱"],
            "personality_modifiers": {"自由": 0.9, "独立": 0.8, "随性": 0.7}
        }
    }
    
    # 五行属性表达风格
    ELEMENT_EXPRESSION_STYLES = {
        "金": {
            "characteristics": ["刚毅", "果断", "锐利", "坚定"],
            "speech_pattern": "言语锐利直接，不喜拐弯抹角",
            "emotional_tendency": "理性大于感性，决断力强",
            "interaction_style": "直接明了，言出必行"
        },
        "木": {
            "characteristics": ["温和", "包容", "生机", "成长"],
            "speech_pattern": "语调温和包容，善于安慰他人",
            "emotional_tendency": "感性丰富，富有同情心",
            "interaction_style": "温和友善，乐于助人"
        },
        "水": {
            "characteristics": ["灵活", "智慧", "深邃", "变通"],
            "speech_pattern": "表达灵活机智，善于随机应变",
            "emotional_tendency": "情感深沉，善于观察",
            "interaction_style": "机智灵活，适应性强"
        },
        "火": {
            "characteristics": ["热情", "急躁", "活力", "激情"],
            "speech_pattern": "语速较快热情，容易激动",
            "emotional_tendency": "情感外露，热情洋溢",
            "interaction_style": "热情主动，富有感染力"
        },
        "土": {
            "characteristics": ["稳重", "可靠", "厚重", "踏实"],
            "speech_pattern": "说话稳重可靠，逻辑清晰",
            "emotional_tendency": "情感稳定，值得信赖",
            "interaction_style": "稳重可靠，值得依赖"
        }
    }
    
    # 角色类型知识领域
    ROLE_KNOWLEDGE_AREAS = {
        "长老": {
            "primary_knowledge": ["门派功法", "修炼心得", "门派历史", "人生智慧"],
            "teaching_ability": 0.9,
            "authority_level": "high",
            "specialties": ["高深功法", "境界突破", "门派管理", "弟子指导"]
        },
        "师兄": {
            "primary_knowledge": ["基础修炼", "门派规矩", "实战经验", "师弟指导"],
            "teaching_ability": 0.7,
            "authority_level": "medium",
            "specialties": ["基础功法", "修炼技巧", "门派生活", "同门关系"]
        },
        "师姐": {
            "primary_knowledge": ["修炼技巧", "生活常识", "情感指导", "细节关怀"],
            "teaching_ability": 0.7,
            "authority_level": "medium",
            "specialties": ["细致指导", "生活照顾", "情感支持", "修炼细节"]
        },
        "师弟": {
            "primary_knowledge": ["基础知识", "修炼疑问", "新鲜见闻", "同门交流"],
            "teaching_ability": 0.3,
            "authority_level": "low",
            "specialties": ["基础问题", "同龄交流", "新奇见闻", "学习心得"]
        },
        "师妹": {
            "primary_knowledge": ["入门知识", "天真疑问", "可爱见解", "纯真想法"],
            "teaching_ability": 0.3,
            "authority_level": "low",
            "specialties": ["入门指导", "天真见解", "可爱互动", "纯真交流"]
        },
        "普通弟子": {
            "primary_knowledge": ["基础修炼", "门派生活", "同门关系", "成长烦恼"],
            "teaching_ability": 0.5,
            "authority_level": "low",
            "specialties": ["基础修炼", "日常生活", "同门友谊", "成长经历"]
        },
        "门派掌门": {
            "primary_knowledge": ["门派大事", "修仙界局势", "高深功法", "领导智慧"],
            "teaching_ability": 1.0,
            "authority_level": "highest",
            "specialties": ["门派决策", "大局观", "高深修为", "领导艺术"]
        },
        "客卿长老": {
            "primary_knowledge": ["江湖见闻", "各派功法", "世事洞察", "超然智慧"],
            "teaching_ability": 0.8,
            "authority_level": "high",
            "specialties": ["江湖经验", "见多识广", "超然视角", "独特见解"]
        }
    }
    
    def __init__(self):
        """初始化个性引擎"""
        self.personality_cache = {}
        logger.log_info("NPC个性引擎初始化完成")
    
    def generate_personality_profile(self, npc_attributes: Dict[str, str]) -> Dict[str, Any]:
        """生成完整的个性档案"""
        realm = npc_attributes.get("修为境界", "练气")
        sect = npc_attributes.get("门派归属", "无门派")
        element = npc_attributes.get("五行属性", "土")
        role = npc_attributes.get("角色类型", "普通弟子")
        
        # 生成缓存键
        cache_key = f"{realm}_{sect}_{element}_{role}"
        
        # 检查缓存
        if cache_key in self.personality_cache:
            return self.personality_cache[cache_key].copy()
        
        # 生成新的个性档案
        profile = {
            "basic_info": {
                "realm": realm,
                "sect": sect,
                "element": element,
                "role": role
            },
            "dialogue_style": self._generate_dialogue_style(realm, element),
            "personality_traits": self._generate_personality_traits(sect, element, role),
            "knowledge_areas": self._generate_knowledge_areas(role, sect),
            "speech_patterns": self._generate_speech_patterns(realm, sect, element),
            "interaction_preferences": self._generate_interaction_preferences(sect, element, role),
            "emotional_tendencies": self._generate_emotional_tendencies(element, role),
            "authority_level": self._get_authority_level(role),
            "teaching_ability": self._get_teaching_ability(role)
        }
        
        # 缓存结果
        self.personality_cache[cache_key] = profile.copy()
        
        return profile
    
    def _generate_dialogue_style(self, realm: str, element: str) -> Dict[str, Any]:
        """生成对话风格"""
        realm_style = self.REALM_DIALOGUE_STYLES.get(realm, self.REALM_DIALOGUE_STYLES["练气"])
        element_style = self.ELEMENT_EXPRESSION_STYLES.get(element, self.ELEMENT_EXPRESSION_STYLES["土"])
        
        return {
            "tone": realm_style["tone"],
            "confidence_level": realm_style["confidence_level"],
            "vocabulary_preference": realm_style["vocabulary"],
            "sentence_patterns": realm_style["sentence_patterns"],
            "expression_characteristics": element_style["characteristics"],
            "speech_pattern": element_style["speech_pattern"],
            "emotional_tendency": element_style["emotional_tendency"]
        }
    
    def _generate_personality_traits(self, sect: str, element: str, role: str) -> List[str]:
        """生成个性特征列表"""
        traits = []
        
        # 门派特征
        sect_traits = self.SECT_CULTURAL_TRAITS.get(sect, self.SECT_CULTURAL_TRAITS["无门派"])
        traits.extend(sect_traits["core_values"])
        
        # 五行特征
        element_traits = self.ELEMENT_EXPRESSION_STYLES.get(element, self.ELEMENT_EXPRESSION_STYLES["土"])
        traits.extend(element_traits["characteristics"])
        
        # 角色特征
        role_info = self.ROLE_KNOWLEDGE_AREAS.get(role, self.ROLE_KNOWLEDGE_AREAS["普通弟子"])
        if "specialties" in role_info:
            traits.extend([trait.split("、")[0] for trait in role_info["specialties"]])
        
        # 去重并返回
        return list(set(traits))
    
    def _generate_knowledge_areas(self, role: str, sect: str) -> List[str]:
        """生成知识领域"""
        knowledge = []
        
        # 角色知识
        role_info = self.ROLE_KNOWLEDGE_AREAS.get(role, self.ROLE_KNOWLEDGE_AREAS["普通弟子"])
        knowledge.extend(role_info["primary_knowledge"])
        
        # 门派知识
        sect_info = self.SECT_CULTURAL_TRAITS.get(sect, self.SECT_CULTURAL_TRAITS["无门派"])
        knowledge.extend([f"{sect}文化", f"{sect}传统"])
        
        return knowledge
    
    def _generate_speech_patterns(self, realm: str, sect: str, element: str) -> List[str]:
        """生成语言模式"""
        patterns = []
        
        # 修为境界模式
        realm_info = self.REALM_DIALOGUE_STYLES.get(realm, self.REALM_DIALOGUE_STYLES["练气"])
        patterns.extend(realm_info["sentence_patterns"])
        
        # 门派用词
        sect_info = self.SECT_CULTURAL_TRAITS.get(sect, self.SECT_CULTURAL_TRAITS["无门派"])
        patterns.extend([f"常用词汇：{', '.join(sect_info['common_phrases'])}"])
        
        return patterns
    
    def _generate_interaction_preferences(self, sect: str, element: str, role: str) -> Dict[str, Any]:
        """生成交互偏好"""
        sect_info = self.SECT_CULTURAL_TRAITS.get(sect, self.SECT_CULTURAL_TRAITS["无门派"])
        element_info = self.ELEMENT_EXPRESSION_STYLES.get(element, self.ELEMENT_EXPRESSION_STYLES["土"])
        role_info = self.ROLE_KNOWLEDGE_AREAS.get(role, self.ROLE_KNOWLEDGE_AREAS["普通弟子"])
        
        return {
            "preferred_topics": role_info["primary_knowledge"],
            "interaction_style": element_info["interaction_style"],
            "authority_approach": sect_info["speech_style"],
            "teaching_willingness": role_info["teaching_ability"]
        }
    
    def _generate_emotional_tendencies(self, element: str, role: str) -> Dict[str, float]:
        """生成情感倾向"""
        element_info = self.ELEMENT_EXPRESSION_STYLES.get(element, self.ELEMENT_EXPRESSION_STYLES["土"])
        
        # 基础情感倾向
        tendencies = {
            "热情度": 0.5,
            "耐心度": 0.5,
            "严肃度": 0.5,
            "友善度": 0.5,
            "权威感": 0.5
        }
        
        # 五行影响
        if element == "火":
            tendencies["热情度"] += 0.3
            tendencies["耐心度"] -= 0.2
        elif element == "水":
            tendencies["耐心度"] += 0.3
            tendencies["友善度"] += 0.2
        elif element == "金":
            tendencies["严肃度"] += 0.3
            tendencies["权威感"] += 0.2
        elif element == "木":
            tendencies["友善度"] += 0.3
            tendencies["耐心度"] += 0.2
        elif element == "土":
            tendencies["耐心度"] += 0.2
            tendencies["严肃度"] += 0.1
        
        # 角色影响
        if role in ["长老", "门派掌门"]:
            tendencies["权威感"] += 0.3
            tendencies["严肃度"] += 0.2
        elif role in ["师弟", "师妹"]:
            tendencies["热情度"] += 0.2
            tendencies["友善度"] += 0.2
            tendencies["权威感"] -= 0.3
        
        # 确保值在0-1范围内
        for key in tendencies:
            tendencies[key] = max(0.0, min(1.0, tendencies[key]))
        
        return tendencies
    
    def _get_authority_level(self, role: str) -> str:
        """获取权威等级"""
        role_info = self.ROLE_KNOWLEDGE_AREAS.get(role, self.ROLE_KNOWLEDGE_AREAS["普通弟子"])
        return role_info["authority_level"]
    
    def _get_teaching_ability(self, role: str) -> float:
        """获取教学能力"""
        role_info = self.ROLE_KNOWLEDGE_AREAS.get(role, self.ROLE_KNOWLEDGE_AREAS["普通弟子"])
        return role_info["teaching_ability"]
    
    def generate_dynamic_prompt(self, personality_profile: Dict[str, Any], context: Dict[str, Any]) -> str:
        """生成动态LLM提示"""
        basic_info = personality_profile["basic_info"]
        dialogue_style = personality_profile["dialogue_style"]
        traits = personality_profile["personality_traits"]
        knowledge = personality_profile["knowledge_areas"]
        
        prompt = f"""你是{context.get('npc_name', '某位修士')}，一位{basic_info['role']}。

基本信息：
- 修为境界：{basic_info['realm']}
- 门派归属：{basic_info['sect']}
- 五行属性：{basic_info['element']}
- 角色定位：{basic_info['role']}

个性特征：{', '.join(traits[:5])}

对话风格：
- 语调：{dialogue_style['tone']}
- 自信程度：{dialogue_style['confidence_level']:.1f}
- 表达特点：{dialogue_style['speech_pattern']}

知识领域：{', '.join(knowledge[:3])}

当前情况：
{context.get('current_situation', '正在与访客对话')}

请以{context.get('npc_name', '某位修士')}的身份回应，保持角色一致性和仙侠世界氛围。
回答要简洁明了，体现修为境界和个性特征。
"""
        return prompt
    
    def update_personality_based_on_interaction(self, npc_key: str, interaction_data: Dict[str, Any]):
        """基于交互更新个性（学习机制）"""
        # 这里可以实现基于玩家反馈的个性微调
        # 目前保持简单实现
        logger.log_info(f"NPC {npc_key} 个性更新：{interaction_data}")
    
    def get_personality_summary(self, personality_profile: Dict[str, Any]) -> str:
        """获取个性摘要"""
        basic_info = personality_profile["basic_info"]
        traits = personality_profile["personality_traits"][:3]
        
        return f"{basic_info['sect']}{basic_info['role']}，{basic_info['realm']}修为，性格{', '.join(traits)}"
