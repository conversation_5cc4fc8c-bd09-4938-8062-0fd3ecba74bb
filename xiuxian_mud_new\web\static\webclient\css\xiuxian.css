/*
仙侠MUD主题样式 - 基于Evennia Web客户端的仙侠主题扩展

设计理念：
- 继承Evennia原生webclient.css
- 仙侠风格色彩和视觉效果
- 现代化布局和交互
- 桌面端优先设计（1920x1080）

技术特点：
- CSS变量主题系统
- Grid + Flexbox布局
- 渐变和阴影效果
- 平滑动画过渡
*/

/* 导入Evennia原生样式作为基础 */
@import url('/static/webclient/css/webclient.css');

/* ===== 仙侠主题变量定义 ===== */
:root {
    /* 主色调 - 金色系 */
    --xiuxian-primary: #d4af37;        /* 金色主色 */
    --xiuxian-secondary: #ffd700;      /* 亮金色 */
    --xiuxian-accent: #b8860b;         /* 深金色强调 */
    
    /* 背景色 - 深蓝夜空系 */
    --xiuxian-bg-primary: #1a1a2e;     /* 主背景 */
    --xiuxian-bg-secondary: #16213e;   /* 次背景 */
    --xiuxian-bg-tertiary: #0f1419;    /* 深背景 */
    
    /* 文字色 */
    --xiuxian-text-primary: #e8e8e8;   /* 主文字 */
    --xiuxian-text-secondary: #c0c0c0; /* 次文字 */
    --xiuxian-text-muted: #888888;     /* 弱化文字 */
    
    /* 功能色 */
    --xiuxian-success: #4caf50;        /* 成功绿 */
    --xiuxian-warning: #ff9800;        /* 警告橙 */
    --xiuxian-error: #f44336;          /* 错误红 */
    --xiuxian-info: #2196f3;           /* 信息蓝 */
    
    /* 边框和分割线 */
    --xiuxian-border: #3a3a5c;         /* 边框色 */
    --xiuxian-border-light: #4a4a6c;   /* 浅边框 */
    --xiuxian-divider: #2a2a4c;        /* 分割线 */
    
    /* 阴影效果 */
    --xiuxian-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --xiuxian-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --xiuxian-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --xiuxian-shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
    
    /* 渐变效果 */
    --xiuxian-gradient-primary: linear-gradient(135deg, var(--xiuxian-bg-primary), var(--xiuxian-bg-secondary));
    --xiuxian-gradient-gold: linear-gradient(90deg, var(--xiuxian-primary), var(--xiuxian-secondary));
    --xiuxian-gradient-accent: linear-gradient(45deg, var(--xiuxian-accent), var(--xiuxian-primary));
    
    /* 动画时长 */
    --xiuxian-transition-fast: 0.2s ease;
    --xiuxian-transition-normal: 0.3s ease;
    --xiuxian-transition-slow: 0.5s ease;
}

/* ===== 全局样式覆盖 ===== */

/* 基础元素重置 */
html, body {
    background: var(--xiuxian-gradient-primary);
    color: var(--xiuxian-text-primary);
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--xiuxian-bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--xiuxian-gradient-gold);
    border-radius: 4px;
    transition: var(--xiuxian-transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--xiuxian-secondary);
    box-shadow: var(--xiuxian-shadow-glow);
}

/* 链接样式 */
a {
    color: var(--xiuxian-secondary);
    text-decoration: none;
    transition: var(--xiuxian-transition-fast);
}

a:hover {
    color: var(--xiuxian-primary);
    text-shadow: 0 0 8px var(--xiuxian-primary);
}

/* ===== 主容器布局 ===== */

/* Web客户端主容器 */
.webclient-main,
#webclient {
    background: var(--xiuxian-gradient-primary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 12px;
    box-shadow: var(--xiuxian-shadow-lg);
    overflow: hidden;
    min-height: 100vh;
}

/* 顶部标题栏 */
.webclient-header {
    background: var(--xiuxian-gradient-gold);
    color: var(--xiuxian-bg-primary);
    padding: 12px 20px;
    font-weight: bold;
    font-size: 1.2em;
    text-align: center;
    box-shadow: var(--xiuxian-shadow-md);
    position: relative;
}

.webclient-header::before {
    content: '🎭';
    margin-right: 8px;
}

.webclient-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--xiuxian-gradient-accent);
}

/* ===== 消息显示区域 ===== */

/* 消息容器 */
.msg-container,
#messagewindow {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid var(--xiuxian-border);
    border-radius: 8px;
    margin: 10px;
    padding: 15px;
    box-shadow: var(--xiuxian-shadow-md);
    backdrop-filter: blur(5px);
}

/* 消息行 */
.msg,
.msg-line {
    padding: 4px 8px;
    margin: 2px 0;
    border-radius: 4px;
    transition: var(--xiuxian-transition-fast);
    word-wrap: break-word;
}

.msg:hover {
    background: rgba(212, 175, 55, 0.1);
    box-shadow: var(--xiuxian-shadow-sm);
}

/* 不同类型消息样式 */
.msg-channel {
    border-left: 3px solid var(--xiuxian-secondary);
    background: rgba(212, 175, 55, 0.05);
    padding-left: 12px;
}

.msg-system {
    color: var(--xiuxian-info);
    font-style: italic;
    border-left: 3px solid var(--xiuxian-info);
    padding-left: 12px;
}

.msg-error {
    color: var(--xiuxian-error);
    background: rgba(244, 67, 54, 0.1);
    border-left: 3px solid var(--xiuxian-error);
    padding-left: 12px;
}

.msg-success {
    color: var(--xiuxian-success);
    background: rgba(76, 175, 80, 0.1);
    border-left: 3px solid var(--xiuxian-success);
    padding-left: 12px;
}

/* AI导演消息特殊样式 */
.msg-ai-director {
    background: var(--xiuxian-gradient-accent);
    color: white;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 8px 0;
    box-shadow: var(--xiuxian-shadow-glow);
    position: relative;
}

.msg-ai-director::before {
    content: '🎭 AI导演：';
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

/* ===== 输入区域 ===== */

/* 输入容器 */
.input-container,
#inputcontainer {
    background: var(--xiuxian-bg-secondary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 8px;
    margin: 10px;
    padding: 10px;
    box-shadow: var(--xiuxian-shadow-md);
}

/* 输入框 */
.input-field,
#inputfield {
    background: var(--xiuxian-bg-tertiary);
    border: 2px solid var(--xiuxian-border);
    border-radius: 6px;
    color: var(--xiuxian-text-primary);
    padding: 10px 15px;
    font-size: 14px;
    width: 100%;
    transition: var(--xiuxian-transition-normal);
}

.input-field:focus,
#inputfield:focus {
    border-color: var(--xiuxian-primary);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
    outline: none;
    background: var(--xiuxian-bg-primary);
}

/* 输入框占位符 */
.input-field::placeholder,
#inputfield::placeholder {
    color: var(--xiuxian-text-muted);
    font-style: italic;
}

/* ===== 按钮样式 ===== */

/* 基础按钮 */
.btn,
button {
    background: var(--xiuxian-gradient-gold);
    border: none;
    border-radius: 6px;
    color: var(--xiuxian-bg-primary);
    cursor: pointer;
    font-weight: bold;
    padding: 8px 16px;
    transition: var(--xiuxian-transition-normal);
    box-shadow: var(--xiuxian-shadow-sm);
}

.btn:hover,
button:hover {
    transform: translateY(-2px);
    box-shadow: var(--xiuxian-shadow-md), var(--xiuxian-shadow-glow);
}

.btn:active,
button:active {
    transform: translateY(0);
    box-shadow: var(--xiuxian-shadow-sm);
}

/* 次要按钮 */
.btn-secondary {
    background: var(--xiuxian-bg-secondary);
    color: var(--xiuxian-text-primary);
    border: 1px solid var(--xiuxian-border);
}

.btn-secondary:hover {
    background: var(--xiuxian-border);
    border-color: var(--xiuxian-primary);
}

/* 危险按钮 */
.btn-danger {
    background: linear-gradient(135deg, var(--xiuxian-error), #d32f2f);
    color: white;
}

/* ===== 状态指示器 ===== */

/* 在线状态 */
.status-online {
    color: var(--xiuxian-success);
}

.status-offline {
    color: var(--xiuxian-text-muted);
}

.status-away {
    color: var(--xiuxian-warning);
}

/* 连接状态指示器 */
.connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1000;
    transition: var(--xiuxian-transition-fast);
}

.connection-status.connected {
    background: var(--xiuxian-success);
    color: white;
}

.connection-status.disconnected {
    background: var(--xiuxian-error);
    color: white;
}

.connection-status.connecting {
    background: var(--xiuxian-warning);
    color: white;
}

/* ===== 动画效果 ===== */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 金光闪烁动画 */
@keyframes goldGlow {
    0%, 100% {
        box-shadow: 0 0 5px var(--xiuxian-primary);
    }
    50% {
        box-shadow: 0 0 20px var(--xiuxian-secondary), 0 0 30px var(--xiuxian-primary);
    }
}

/* 应用动画 */
.fade-in {
    animation: fadeIn var(--xiuxian-transition-normal);
}

.gold-glow {
    animation: goldGlow 2s ease-in-out infinite;
}

/* ===== 工具提示 ===== */

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--xiuxian-bg-tertiary);
    color: var(--xiuxian-text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--xiuxian-transition-fast);
    box-shadow: var(--xiuxian-shadow-md);
    border: 1px solid var(--xiuxian-border);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ===== 响应式基础 ===== */

/* 大屏幕优化 */
@media (min-width: 1920px) {
    .webclient-main {
        max-width: 1800px;
        margin: 0 auto;
    }
}

/* 中等屏幕适配 */
@media (max-width: 1200px) {
    .webclient-main {
        margin: 5px;
        border-radius: 8px;
    }
    
    .msg-container {
        margin: 5px;
        padding: 10px;
    }
}

/* 小屏幕基础适配 */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }
    
    .webclient-header {
        padding: 8px 15px;
        font-size: 1em;
    }
    
    .input-field {
        padding: 8px 12px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 16px;
    }
}
