"""
TagProperty系统集成测试

测试TagProperty系统与现有Evennia系统的集成兼容性：
- 与Traits系统的协同工作
- 与Components系统的兼容性
- 与AI导演系统的集成
- 数据一致性验证
"""

import unittest
from unittest.mock import Mock, patch
from evennia.utils.test_resources import BaseEvenniaTest
from evennia.utils.create import create_object

from ..systems.tag_property_system import TagPropertyQueryManager
from ..systems.query_interfaces import AIDirectorQueryInterface
from ..typeclasses.characters import XianxiaCharacter
from ..typeclasses.rooms import XianxiaRoom
from ..typeclasses.objects import XianxiaObject


class TagPropertyIntegrationTest(BaseEvenniaTest):
    """TagProperty系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        
        # 创建测试角色
        self.test_char = create_object(
            XianxiaCharacter,
            key="test_integration_char",
            location=None
        )
        
        # 创建测试房间
        self.test_room = create_object(
            XianxiaRoom,
            key="test_integration_room",
            location=None
        )
        
        # 创建测试物品
        self.test_obj = create_object(
            XianxiaObject,
            key="test_integration_obj",
            location=None
        )
    
    def tearDown(self):
        """测试后清理"""
        if self.test_char:
            self.test_char.delete()
        if self.test_room:
            self.test_room.delete()
        if self.test_obj:
            self.test_obj.delete()
        super().tearDown()
    
    def test_character_traits_tagproperty_sync(self):
        """测试角色Traits与TagProperty同步"""
        print("\n=== 角色Traits与TagProperty同步测试 ===")
        
        # 设置TagProperty
        self.test_char.修为境界 = "金丹"
        self.test_char.门派归属 = "青云门"
        self.test_char.五行属性 = "火"
        
        # 验证TagProperty设置成功
        self.assertEqual(self.test_char.修为境界, "金丹")
        self.assertEqual(self.test_char.门派归属, "青云门")
        self.assertEqual(self.test_char.五行属性, "火")
        
        # 测试同步方法
        self.test_char.sync_realm_with_traits()
        
        # 验证境界等级计算
        realm_level = self.test_char.get_realm_level()
        self.assertEqual(realm_level, 2)  # 金丹对应等级2
        
        # 验证五行亲和度计算
        fire_affinity = self.test_char.get_elemental_affinity("火")
        self.assertEqual(fire_affinity, 1.5)  # 同属性高亲和
        
        water_affinity = self.test_char.get_elemental_affinity("水")
        self.assertEqual(water_affinity, 1.2)  # 火生土，相生关系
        
        print("角色Traits与TagProperty同步测试通过")
    
    def test_room_spiritual_energy_calculation(self):
        """测试房间灵气浓度计算"""
        print("\n=== 房间灵气浓度计算测试 ===")
        
        # 设置房间属性
        self.test_room.灵气浓度 = "浓郁"
        self.test_room.危险等级 = "中等"
        self.test_room.地点类型 = "洞府"
        self.test_room.五行属性 = "木"
        
        # 验证属性设置
        self.assertEqual(self.test_room.灵气浓度, "浓郁")
        self.assertEqual(self.test_room.危险等级, "中等")
        
        # 测试灵气加成计算
        spiritual_bonus = self.test_room.get_spiritual_bonus()
        self.assertGreater(spiritual_bonus, 1.0)  # 浓郁灵气应该有加成
        
        # 测试危险修正
        danger_modifier = self.test_room.get_danger_modifier()
        self.assertIsInstance(danger_modifier, float)
        
        # 测试可用功能
        available_functions = self.test_room.get_available_functions()
        self.assertIsInstance(available_functions, list)
        
        print("房间灵气浓度计算测试通过")
    
    def test_object_quality_system(self):
        """测试物品品质系统"""
        print("\n=== 物品品质系统测试 ===")
        
        # 设置物品属性
        self.test_obj.物品类型 = "法器"
        self.test_obj.品质等级 = "灵品"
        self.test_obj.五行属性 = "金"
        self.test_obj.炼制等级 = 3
        
        # 验证属性设置
        self.assertEqual(self.test_obj.物品类型, "法器")
        self.assertEqual(self.test_obj.品质等级, "灵品")
        
        # 测试品质倍数计算
        quality_multiplier = self.test_obj.get_quality_multiplier()
        self.assertGreater(quality_multiplier, 1.0)  # 灵品应该有倍数加成
        
        # 测试五行加成
        elemental_bonus = self.test_obj.get_elemental_bonus("金")
        self.assertEqual(elemental_bonus, 1.5)  # 同属性高加成
        
        # 测试物品价值计算
        item_value = self.test_obj.calculate_item_value()
        self.assertGreater(item_value, 0)
        
        print("物品品质系统测试通过")
    
    def test_query_manager_functionality(self):
        """测试查询管理器功能"""
        print("\n=== 查询管理器功能测试 ===")
        
        # 测试角色查询
        jindan_chars = TagPropertyQueryManager.find_characters_by_realm("金丹")
        self.assertIsInstance(jindan_chars, list)
        
        # 测试房间查询
        rich_rooms = TagPropertyQueryManager.find_rooms_by_spiritual_energy("浓郁")
        self.assertIsInstance(rich_rooms, list)
        
        # 测试物品查询
        spirit_items = TagPropertyQueryManager.find_objects_by_quality("灵品")
        self.assertIsInstance(spirit_items, list)
        
        # 测试复合查询
        complex_result = TagPropertyQueryManager.complex_query({
            "境界等级": "金丹",
            "sect_territory": "青云门"
        })
        self.assertIsInstance(complex_result, list)
        
        # 测试统计功能
        stats = TagPropertyQueryManager.get_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn("total_characters", stats)
        self.assertIn("total_rooms", stats)
        self.assertIn("total_objects", stats)
        
        print("查询管理器功能测试通过")
    
    def test_ai_director_integration(self):
        """测试AI导演系统集成"""
        print("\n=== AI导演系统集成测试 ===")
        
        # 测试天道导演查询
        world_summary = AIDirectorQueryInterface.get_world_state_summary()
        self.assertIsInstance(world_summary, dict)
        self.assertIn("realm_distribution", world_summary)
        self.assertIn("sect_power", world_summary)
        
        # 测试地灵导演查询
        regional_activity = AIDirectorQueryInterface.get_regional_activity()
        self.assertIsInstance(regional_activity, dict)
        
        # 测试器灵导演查询
        realtime_events = AIDirectorQueryInterface.get_realtime_events()
        self.assertIsInstance(realtime_events, dict)
        self.assertIn("active_characters", realtime_events)
        self.assertIn("high_risk_activity", realtime_events)
        
        print("AI导演系统集成测试通过")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n=== 数据一致性测试 ===")
        
        # 设置角色属性
        self.test_char.修为境界 = "元婴"
        self.test_char.门派归属 = "天音寺"
        
        # 验证通过不同方式查询的一致性
        # 方式1：直接属性访问
        direct_realm = self.test_char.修为境界
        
        # 方式2：通过Tags查询
        tag_realm = self.test_char.tags.get(category="境界等级")
        
        # 验证一致性
        self.assertEqual(direct_realm, tag_realm)
        
        # 测试查询结果一致性
        char_by_realm = TagPropertyQueryManager.find_characters_by_realm("元婴")
        char_by_sect = TagPropertyQueryManager.find_characters_by_sect("天音寺")
        
        # 验证角色在查询结果中
        if char_by_realm:
            char_keys = [char.key for char in char_by_realm]
            self.assertIn(self.test_char.key, char_keys)
        
        if char_by_sect:
            char_keys = [char.key for char in char_by_sect]
            self.assertIn(self.test_char.key, char_keys)
        
        print("数据一致性测试通过")
    
    def test_performance_under_load(self):
        """测试负载下的性能"""
        print("\n=== 负载性能测试 ===")
        
        import time
        
        # 创建多个测试对象
        test_objects = []
        for i in range(20):
            char = create_object(
                XianxiaCharacter,
                key=f"load_test_char_{i}",
                location=None
            )
            char.修为境界 = "筑基"
            char.门派归属 = "青云门"
            test_objects.append(char)
        
        try:
            # 测试批量查询性能
            start_time = time.perf_counter()
            
            for _ in range(10):
                TagPropertyQueryManager.find_characters_by_realm("筑基")
                TagPropertyQueryManager.find_characters_by_sect("青云门")
                TagPropertyQueryManager.complex_query({
                    "境界等级": "筑基",
                    "sect_territory": "青云门"
                })
            
            end_time = time.perf_counter()
            total_time = end_time - start_time
            
            print(f"批量查询总时间: {total_time:.4f}秒")
            print(f"平均每次查询: {total_time/30:.6f}秒")
            
            # 验证性能要求
            self.assertLess(total_time, 1.0, "批量查询应在1秒内完成")
            
        finally:
            # 清理测试对象
            for obj in test_objects:
                obj.delete()
        
        print("负载性能测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 错误处理测试 ===")
        
        # 测试无效境界设置
        try:
            self.test_char.修为境界 = "无效境界"
            # 应该被验证器拒绝或使用默认值
        except Exception as e:
            print(f"预期的验证错误: {e}")
        
        # 测试无效查询
        invalid_result = TagPropertyQueryManager.find_characters_by_realm("不存在的境界")
        self.assertIsInstance(invalid_result, list)
        self.assertEqual(len(invalid_result), 0)
        
        # 测试空查询条件
        empty_result = TagPropertyQueryManager.complex_query({})
        self.assertIsInstance(empty_result, list)
        
        print("错误处理测试通过")


if __name__ == "__main__":
    unittest.main()
