"""
小说生成系统验证脚本

验证小说生成系统的完整性和功能正确性。
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def verify_imports():
    """验证所有必要的模块导入"""
    print("=== 验证模块导入 ===")
    
    try:
        # 验证核心脚本
        from xiuxian_mud_new.scripts.novel_generator_script import NovelGeneratorScript, NovelEventHandler
        print("✓ NovelGeneratorScript 导入成功")
        
        # 验证事件收集器
        from xiuxian_mud_new.systems.novel_event_collector import NovelEventCollector, EventRecord, EventSignificance
        print("✓ NovelEventCollector 导入成功")
        
        # 验证上下文管理器
        from xiuxian_mud_new.systems.narrative_context_manager import NarrativeContextManager
        print("✓ NarrativeContextManager 导入成功")
        
        # 验证内容生成器
        from xiuxian_mud_new.systems.novel_content_generator import NovelContentGenerator
        print("✓ NovelContentGenerator 导入成功")
        
        # 验证管理命令
        from xiuxian_mud_new.commands.novel_commands import (
            CmdNovelStatus, CmdNovelGenerate, CmdNovelConfig, 
            CmdNovelChapters, CmdNovelRead
        )
        print("✓ Novel Commands 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def verify_class_structure():
    """验证类结构和方法"""
    print("\n=== 验证类结构 ===")
    
    try:
        from xiuxian_mud_new.scripts.novel_generator_script import NovelGeneratorScript
        from xiuxian_mud_new.systems.novel_event_collector import NovelEventCollector
        from xiuxian_mud_new.systems.narrative_context_manager import NarrativeContextManager
        from xiuxian_mud_new.systems.novel_content_generator import NovelContentGenerator
        
        # 验证NovelGeneratorScript方法
        required_methods = [
            'at_script_creation', 'at_repeat', 'at_script_delete',
            'generate_novel_chapter', 'on_event_trigger', 'should_generate_content',
            'manual_generate', 'get_status', 'update_config'
        ]
        
        for method in required_methods:
            if hasattr(NovelGeneratorScript, method):
                print(f"✓ NovelGeneratorScript.{method} 存在")
            else:
                print(f"✗ NovelGeneratorScript.{method} 缺失")
                return False
                
        # 验证NovelEventCollector方法
        collector_methods = ['collect_event', 'evaluate_significance', 'get_recent_events']
        for method in collector_methods:
            if hasattr(NovelEventCollector, method):
                print(f"✓ NovelEventCollector.{method} 存在")
            else:
                print(f"✗ NovelEventCollector.{method} 缺失")
                return False
                
        # 验证NarrativeContextManager方法
        context_methods = ['build_narrative_context', '_analyze_character_arcs', '_identify_current_themes']
        for method in context_methods:
            if hasattr(NarrativeContextManager, method):
                print(f"✓ NarrativeContextManager.{method} 存在")
            else:
                print(f"✗ NarrativeContextManager.{method} 缺失")
                return False
                
        # 验证NovelContentGenerator方法
        generator_methods = ['generate_chapter', 'generate_event_narrative', 'generate_dialogue']
        for method in generator_methods:
            if hasattr(NovelContentGenerator, method):
                print(f"✓ NovelContentGenerator.{method} 存在")
            else:
                print(f"✗ NovelContentGenerator.{method} 缺失")
                return False
                
        return True
        
    except Exception as e:
        print(f"✗ 类结构验证失败: {e}")
        return False


def verify_data_structures():
    """验证数据结构"""
    print("\n=== 验证数据结构 ===")
    
    try:
        from xiuxian_mud_new.systems.novel_event_collector import EventRecord, EventSignificance
        
        # 验证EventRecord结构
        test_record = EventRecord(
            event_id="test_123",
            event_type="TestEvent",
            timestamp=time.time(),
            participants=["张三"],
            location="测试地点",
            description="测试事件",
            significance=EventSignificance.MEDIUM,
            narrative_tags=["测试"]
        )
        
        print("✓ EventRecord 结构正确")
        
        # 验证EventSignificance枚举
        significance_values = [
            EventSignificance.VERY_LOW,
            EventSignificance.LOW,
            EventSignificance.MEDIUM,
            EventSignificance.HIGH,
            EventSignificance.VERY_HIGH
        ]
        
        print("✓ EventSignificance 枚举完整")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据结构验证失败: {e}")
        return False


def verify_configuration():
    """验证配置结构"""
    print("\n=== 验证配置结构 ===")
    
    try:
        # 模拟配置验证
        default_config = {
            "generation_interval": 3600,
            "auto_generation": True,
            "max_records": 1000,
            "max_chapters": 50,
            "significance_threshold": 0.7,
            "cache_ttl": {
                "narrative_context": 300,
                "world_state": 600,
                "character_arcs": 900,
                "recent_events": 180
            }
        }
        
        # 验证配置项
        required_keys = [
            "generation_interval", "auto_generation", "max_records",
            "max_chapters", "significance_threshold"
        ]
        
        for key in required_keys:
            if key in default_config:
                print(f"✓ 配置项 {key} 存在")
            else:
                print(f"✗ 配置项 {key} 缺失")
                return False
                
        return True
        
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        return False


def verify_integration():
    """验证系统集成"""
    print("\n=== 验证系统集成 ===")
    
    try:
        # 验证事件总线集成
        from xiuxian_mud_new.systems.event_system import XianxiaEventBus, BaseEventHandler, EventFilter
        print("✓ 事件总线集成正确")
        
        # 验证LLM集成
        try:
            from evennia.contrib.rpg.llm import llm_request
            print("✓ LLM系统可用")
        except ImportError:
            print("⚠ LLM系统不可用（将使用备用方案）")
            
        # 验证命令系统集成
        from evennia import Command
        print("✓ 命令系统集成正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 系统集成验证失败: {e}")
        return False


def verify_file_structure():
    """验证文件结构"""
    print("\n=== 验证文件结构 ===")
    
    required_files = [
        "xiuxian_mud_new/scripts/novel_generator_script.py",
        "xiuxian_mud_new/systems/novel_event_collector.py",
        "xiuxian_mud_new/systems/narrative_context_manager.py",
        "xiuxian_mud_new/systems/novel_content_generator.py",
        "xiuxian_mud_new/commands/novel_commands.py",
        "测试/test_novel_generation_system.py"
    ]
    
    base_path = os.path.join(os.path.dirname(__file__), '..')
    
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 缺失")
            return False
            
    return True


def verify_functionality():
    """验证基本功能"""
    print("\n=== 验证基本功能 ===")
    
    try:
        # 模拟Evennia环境
        class MockScript:
            def __init__(self):
                self.attributes = MockAttributes()
                
        class MockAttributes:
            def __init__(self):
                self._data = {}
                
            def add(self, key, value, category=None):
                self._data[key] = value
                
            def get(self, key, default=None):
                return self._data.get(key, default)
                
            def set(self, key, value):
                self._data[key] = value
        
        # 测试事件收集器
        from xiuxian_mud_new.systems.novel_event_collector import NovelEventCollector
        
        mock_script = MockScript()
        collector = NovelEventCollector(mock_script)
        
        test_event = {
            "type": "CultivationBreakthroughEvent",
            "description": "测试突破事件",
            "participants": ["张三"],
            "location": "测试地点",
            "timestamp": time.time()
        }
        
        result = collector.collect_event(test_event)
        if result:
            print("✓ 事件收集功能正常")
        else:
            print("✗ 事件收集功能异常")
            return False
            
        # 测试内容生成器
        from xiuxian_mud_new.systems.novel_content_generator import NovelContentGenerator
        
        generator = NovelContentGenerator(mock_script)
        mock_script.attributes.add("generated_chapters", [])
        
        test_context = {
            "recent_events": [test_event],
            "world_mood": "宁静祥和",
            "current_themes": ["修炼成长"]
        }
        
        chapter = generator._generate_chapter_fallback(test_context)
        if chapter and len(chapter) > 100:
            print("✓ 内容生成功能正常")
        else:
            print("✗ 内容生成功能异常")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ 功能验证失败: {e}")
        return False


def generate_verification_report():
    """生成验证报告"""
    print("\n=== 生成验证报告 ===")
    
    report = {
        "verification_time": datetime.now().isoformat(),
        "system_name": "小说生成系统",
        "version": "1.0.0",
        "tests": []
    }
    
    # 运行所有验证
    tests = [
        ("模块导入", verify_imports),
        ("类结构", verify_class_structure),
        ("数据结构", verify_data_structures),
        ("配置结构", verify_configuration),
        ("系统集成", verify_integration),
        ("文件结构", verify_file_structure),
        ("基本功能", verify_functionality)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            report["tests"].append({
                "name": test_name,
                "status": "PASS" if result else "FAIL",
                "timestamp": datetime.now().isoformat()
            })
            if not result:
                all_passed = False
        except Exception as e:
            report["tests"].append({
                "name": test_name,
                "status": "ERROR",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            all_passed = False
    
    report["overall_status"] = "PASS" if all_passed else "FAIL"
    
    # 保存报告
    report_path = os.path.join(os.path.dirname(__file__), "novel_system_verification_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n验证报告已保存到: {report_path}")
    
    # 显示总结
    print(f"\n=== 验证总结 ===")
    print(f"总体状态: {'✓ 通过' if all_passed else '✗ 失败'}")
    
    passed_count = sum(1 for test in report["tests"] if test["status"] == "PASS")
    total_count = len(report["tests"])
    print(f"通过测试: {passed_count}/{total_count}")
    
    if not all_passed:
        print("\n失败的测试:")
        for test in report["tests"]:
            if test["status"] != "PASS":
                print(f"  - {test['name']}: {test['status']}")
                if "error" in test:
                    print(f"    错误: {test['error']}")
    
    return all_passed


if __name__ == "__main__":
    print("小说生成系统验证脚本")
    print("=" * 50)
    
    success = generate_verification_report()
    
    if success:
        print("\n🎉 所有验证通过！小说生成系统已准备就绪。")
        sys.exit(0)
    else:
        print("\n❌ 验证失败，请检查上述错误并修复。")
        sys.exit(1)
