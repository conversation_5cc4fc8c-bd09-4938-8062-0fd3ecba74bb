"""
境界计算器

处理仙侠修炼境界的各种计算，包括境界差异、战斗加成、突破条件等。
"""

from typing import Dict, Tuple, Optional
from dataclasses import dataclass
from evennia.utils import logger
import math


@dataclass
class RealmInfo:
    """境界信息数据类"""
    name: str
    level: int
    base_power: int
    breakthrough_requirement: int
    description: str
    special_abilities: list


class RealmCalculator:
    """
    境界计算器
    
    功能：
    - 境界等级转换
    - 境界差异计算
    - 战斗力加成计算
    - 突破条件判断
    - 境界压制效果
    """
    
    # 境界配置
    REALM_CONFIG = {
        "练气": RealmInfo("练气", 0, 100, 1000, "修炼入门，感知天地灵气", []),
        "筑基": RealmInfo("筑基", 1, 300, 3000, "筑建修炼根基，灵力凝实", ["灵力护体"]),
        "金丹": RealmInfo("金丹", 2, 800, 8000, "凝结金丹，寿元大增", ["金丹护体", "神识外放"]),
        "元婴": RealmInfo("元婴", 3, 2000, 20000, "元婴出窍，神魂强大", ["元婴出窍", "瞬移"]),
        "化神": RealmInfo("化神", 4, 5000, 50000, "化神通玄，掌控法则", ["法则感悟", "空间操控"]),
        "炼虚": RealmInfo("炼虚", 5, 12000, 120000, "炼虚合道，与天地共鸣", ["虚空行走", "时间感知"]),
        "合体": RealmInfo("合体", 6, 30000, 300000, "合体大能，移山填海", ["天地合一", "造化之力"]),
        "大乘": RealmInfo("大乘", 7, 75000, 750000, "大乘至尊，俯瞰众生", ["大道感悟", "因果操控"]),
        "渡劫": RealmInfo("渡劫", 8, 180000, 1800000, "渡劫飞升，超脱凡俗", ["雷劫抗性", "飞升准备"]),
        "仙人": RealmInfo("仙人", 9, 500000, float('inf'), "得道成仙，永生不灭", ["仙力", "不死不灭"])
    }
    
    # 境界名称到等级的映射
    REALM_LEVELS = {info.name: info.level for info in REALM_CONFIG.values()}
    
    # 等级到境界名称的映射
    LEVEL_REALMS = {info.level: info.name for info in REALM_CONFIG.values()}
    
    def __init__(self):
        """初始化境界计算器"""
        logger.log_info("RealmCalculator initialized")
    
    def get_realm_level(self, realm_name: str) -> int:
        """
        获取境界等级
        
        Args:
            realm_name: 境界名称
            
        Returns:
            int: 境界等级
        """
        return self.REALM_LEVELS.get(realm_name, 0)
    
    def get_realm_name(self, level: int) -> str:
        """
        根据等级获取境界名称
        
        Args:
            level: 境界等级
            
        Returns:
            str: 境界名称
        """
        return self.LEVEL_REALMS.get(level, "练气")
    
    def get_realm_info(self, realm_name: str) -> Optional[RealmInfo]:
        """
        获取境界详细信息
        
        Args:
            realm_name: 境界名称
            
        Returns:
            Optional[RealmInfo]: 境界信息，不存在则返回None
        """
        return self.REALM_CONFIG.get(realm_name)
    
    def calculate_realm_difference(self, higher_realm: str, lower_realm: str) -> Dict:
        """
        计算境界差异
        
        Args:
            higher_realm: 高境界名称
            lower_realm: 低境界名称
            
        Returns:
            Dict: 境界差异信息
        """
        higher_level = self.get_realm_level(higher_realm)
        lower_level = self.get_realm_level(lower_realm)
        
        level_diff = higher_level - lower_level
        
        # 计算战斗力差异
        higher_info = self.get_realm_info(higher_realm)
        lower_info = self.get_realm_info(lower_realm)
        
        if not higher_info or not lower_info:
            return {"error": "无效的境界"}
        
        power_ratio = higher_info.base_power / lower_info.base_power if lower_info.base_power > 0 else 1.0
        
        return {
            "level_difference": level_diff,
            "power_ratio": power_ratio,
            "suppression_level": self._calculate_suppression_level(level_diff),
            "combat_bonus": self._calculate_combat_bonus(level_diff),
            "description": self._get_difference_description(level_diff)
        }
    
    def _calculate_suppression_level(self, level_diff: int) -> str:
        """
        计算境界压制等级
        
        Args:
            level_diff: 境界等级差异
            
        Returns:
            str: 压制等级描述
        """
        if level_diff >= 3:
            return "绝对压制"
        elif level_diff >= 2:
            return "强力压制"
        elif level_diff >= 1:
            return "轻微压制"
        else:
            return "无压制"
    
    def _calculate_combat_bonus(self, level_diff: int) -> float:
        """
        计算战斗加成
        
        Args:
            level_diff: 境界等级差异
            
        Returns:
            float: 战斗加成倍数
        """
        if level_diff <= 0:
            return 1.0
        
        # 每级差异提供递增的加成
        # 1级差异: 1.3x, 2级差异: 1.7x, 3级差异: 2.2x, 以此类推
        base_bonus = 1.0
        for i in range(level_diff):
            base_bonus += 0.3 + (i * 0.1)
        
        # 限制最大加成为5倍
        return min(base_bonus, 5.0)
    
    def _get_difference_description(self, level_diff: int) -> str:
        """
        获取境界差异描述
        
        Args:
            level_diff: 境界等级差异
            
        Returns:
            str: 差异描述
        """
        descriptions = {
            0: "境界相当，势均力敌",
            1: "境界略高，小有优势",
            2: "境界明显高出，占据上风",
            3: "境界远超对手，压制明显",
            4: "境界天差地别，如山压卵",
            5: "境界差距巨大，不可同日而语"
        }
        
        if level_diff >= 5:
            return descriptions[5]
        else:
            return descriptions.get(level_diff, "境界差异未知")
    
    def calculate_breakthrough_progress(self, current_exp: int, realm_name: str) -> Dict:
        """
        计算突破进度
        
        Args:
            current_exp: 当前经验值
            realm_name: 当前境界
            
        Returns:
            Dict: 突破进度信息
        """
        realm_info = self.get_realm_info(realm_name)
        if not realm_info:
            return {"error": "无效的境界"}
        
        if realm_info.breakthrough_requirement == float('inf'):
            return {
                "can_breakthrough": False,
                "progress": 1.0,
                "required_exp": float('inf'),
                "description": "已达最高境界"
            }
        
        progress = min(current_exp / realm_info.breakthrough_requirement, 1.0)
        can_breakthrough = current_exp >= realm_info.breakthrough_requirement
        
        return {
            "can_breakthrough": can_breakthrough,
            "progress": progress,
            "current_exp": current_exp,
            "required_exp": realm_info.breakthrough_requirement,
            "remaining_exp": max(0, realm_info.breakthrough_requirement - current_exp),
            "next_realm": self.get_next_realm(realm_name),
            "description": f"突破进度: {progress*100:.1f}%"
        }
    
    def get_next_realm(self, current_realm: str) -> Optional[str]:
        """
        获取下一个境界
        
        Args:
            current_realm: 当前境界
            
        Returns:
            Optional[str]: 下一个境界名称，如果已是最高境界则返回None
        """
        current_level = self.get_realm_level(current_realm)
        next_level = current_level + 1
        
        return self.LEVEL_REALMS.get(next_level)
    
    def get_previous_realm(self, current_realm: str) -> Optional[str]:
        """
        获取上一个境界
        
        Args:
            current_realm: 当前境界
            
        Returns:
            Optional[str]: 上一个境界名称，如果已是最低境界则返回None
        """
        current_level = self.get_realm_level(current_realm)
        previous_level = current_level - 1
        
        if previous_level < 0:
            return None
        
        return self.LEVEL_REALMS.get(previous_level)
    
    def calculate_cultivation_efficiency(self, realm_name: str, talent: float = 1.0, 
                                       environment_bonus: float = 1.0) -> Dict:
        """
        计算修炼效率
        
        Args:
            realm_name: 当前境界
            talent: 天赋系数 (0.5-2.0)
            environment_bonus: 环境加成 (0.5-3.0)
            
        Returns:
            Dict: 修炼效率信息
        """
        realm_info = self.get_realm_info(realm_name)
        if not realm_info:
            return {"error": "无效的境界"}
        
        # 基础修炼效率随境界递减
        base_efficiency = 1.0 / (1 + realm_info.level * 0.2)
        
        # 综合效率
        total_efficiency = base_efficiency * talent * environment_bonus
        
        # 每小时经验获取
        exp_per_hour = int(total_efficiency * 100)
        
        # 预估突破时间
        if realm_info.breakthrough_requirement == float('inf'):
            breakthrough_time = float('inf')
        else:
            breakthrough_time = realm_info.breakthrough_requirement / exp_per_hour
        
        return {
            "base_efficiency": base_efficiency,
            "talent_modifier": talent,
            "environment_modifier": environment_bonus,
            "total_efficiency": total_efficiency,
            "exp_per_hour": exp_per_hour,
            "estimated_breakthrough_hours": breakthrough_time,
            "estimated_breakthrough_days": breakthrough_time / 24 if breakthrough_time != float('inf') else float('inf')
        }
    
    def get_realm_abilities(self, realm_name: str) -> List[str]:
        """
        获取境界特殊能力
        
        Args:
            realm_name: 境界名称
            
        Returns:
            List[str]: 特殊能力列表
        """
        realm_info = self.get_realm_info(realm_name)
        if not realm_info:
            return []
        
        return realm_info.special_abilities.copy()
    
    def compare_realms(self, realm1: str, realm2: str) -> Dict:
        """
        比较两个境界
        
        Args:
            realm1: 境界1
            realm2: 境界2
            
        Returns:
            Dict: 比较结果
        """
        level1 = self.get_realm_level(realm1)
        level2 = self.get_realm_level(realm2)
        
        info1 = self.get_realm_info(realm1)
        info2 = self.get_realm_info(realm2)
        
        if not info1 or not info2:
            return {"error": "无效的境界"}
        
        result = {
            "realm1": {
                "name": realm1,
                "level": level1,
                "power": info1.base_power
            },
            "realm2": {
                "name": realm2,
                "level": level2,
                "power": info2.base_power
            },
            "comparison": {
                "level_difference": level1 - level2,
                "power_ratio": info1.base_power / info2.base_power if info2.base_power > 0 else 1.0,
                "higher_realm": realm1 if level1 > level2 else realm2 if level2 > level1 else "相等"
            }
        }
        
        return result
    
    def get_all_realms(self) -> List[Dict]:
        """
        获取所有境界信息
        
        Returns:
            List[Dict]: 所有境界信息列表
        """
        realms = []
        
        for realm_name, realm_info in self.REALM_CONFIG.items():
            realm_dict = {
                "name": realm_info.name,
                "level": realm_info.level,
                "base_power": realm_info.base_power,
                "breakthrough_requirement": realm_info.breakthrough_requirement,
                "description": realm_info.description,
                "special_abilities": realm_info.special_abilities.copy()
            }
            realms.append(realm_dict)
        
        # 按等级排序
        realms.sort(key=lambda x: x["level"])
        
        return realms


# 全局实例
realm_calculator = RealmCalculator()


def get_realm_calculator() -> RealmCalculator:
    """获取境界计算器实例"""
    return realm_calculator
