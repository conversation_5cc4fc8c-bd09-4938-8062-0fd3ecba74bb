"""
仙侠社交命令系统

提供完整的社交互动命令，包括：
- 门派相关命令：入门、退门、门派频道等
- 师徒相关命令：收徒、逐出师门、师徒对话等
- 关系相关命令：加好友、删好友、查看关系等
- 声望相关命令：查看声望、声望排行等

基于Evennia Command系统，与社交管理器完全集成。
"""

from evennia import Command, CmdSet
from evennia.utils import logger, evtable
from evennia.utils.search import search_object


class CmdJoinSect(Command):
    """
    加入门派命令
    
    用法:
        入门 <门派名称>
        joinsect <sect_name>
    
    加入指定的门派。需要门派长老的批准。
    """
    
    key = "入门"
    aliases = ["joinsect"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 入门 <门派名称>")
            return
        
        sect_name = self.args.strip()
        
        # 检查是否已有门派
        current_sect = getattr(self.caller, '门派归属', '无门派')
        if current_sect != '无门派':
            self.caller.msg(f"你已经是{current_sect}的成员，不能加入其他门派。")
            return
        
        # 查找门派频道
        from typeclasses.sect_channels import SectChannelManager
        sect_channel = SectChannelManager.find_sect_channel(sect_name)
        
        if not sect_channel:
            self.caller.msg(f"找不到门派: {sect_name}")
            return
        
        # 添加到门派（这里简化处理，实际可能需要长老批准）
        success = sect_channel.add_sect_member(self.caller, "外门弟子")
        
        if success:
            self.caller.msg(f"恭喜！你已成功加入{sect_name}，成为外门弟子。")
        else:
            self.caller.msg(f"加入{sect_name}失败。")


class CmdLeaveSect(Command):
    """
    离开门派命令
    
    用法:
        退门 [原因]
        leavesect [reason]
    
    离开当前门派。
    """
    
    key = "退门"
    aliases = ["leavesect"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        current_sect = getattr(self.caller, '门派归属', '无门派')
        if current_sect == '无门派':
            self.caller.msg("你目前没有加入任何门派。")
            return
        
        reason = self.args.strip() if self.args else "主动退出"
        
        # 查找门派频道
        from typeclasses.sect_channels import SectChannelManager
        sect_channel = SectChannelManager.find_sect_channel(current_sect)
        
        if sect_channel:
            success = sect_channel.remove_sect_member(self.caller, reason)
            if success:
                self.caller.msg(f"你已离开{current_sect}。")
            else:
                self.caller.msg("离开门派失败。")
        else:
            self.caller.msg("找不到门派频道，请联系管理员。")


class CmdSectInfo(Command):
    """
    查看门派信息命令
    
    用法:
        门派信息 [门派名称]
        sectinfo [sect_name]
    
    查看门派的详细信息。
    """
    
    key = "门派信息"
    aliases = ["sectinfo"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if self.args:
            sect_name = self.args.strip()
        else:
            sect_name = getattr(self.caller, '门派归属', '无门派')
            if sect_name == '无门派':
                self.caller.msg("请指定门派名称，或先加入一个门派。")
                return
        
        # 查找门派频道
        from typeclasses.sect_channels import SectChannelManager
        sect_channel = SectChannelManager.find_sect_channel(sect_name)
        
        if not sect_channel:
            self.caller.msg(f"找不到门派: {sect_name}")
            return
        
        # 获取门派信息
        sect_info = sect_channel.get_sect_info()
        members = sect_channel.get_sect_members()
        
        # 创建信息表格
        table = evtable.EvTable(f"|c{sect_name} 门派信息|n", border="cells")
        table.add_row("门派等级", sect_info["level"])
        table.add_row("成员数量", sect_info["member_count"])
        table.add_row("创建时间", sect_info.get("creation_time", "未知"))
        table.add_row("最后活动", sect_info.get("last_activity", "未知"))
        
        self.caller.msg(str(table))
        
        # 显示成员列表
        if members:
            member_table = evtable.EvTable("姓名", "等级", "修为", "职业", border="cells")
            for member in members[:10]:  # 只显示前10个成员
                member_table.add_row(
                    member["name"],
                    member["rank"],
                    member["realm"],
                    member["profession"]
                )
            
            self.caller.msg(f"\n|c门派成员 (显示前10名)|n")
            self.caller.msg(str(member_table))


class CmdAcceptDisciple(Command):
    """
    收徒命令
    
    用法:
        收徒 <角色名>
        accept_disciple <character_name>
    
    收指定角色为弟子。
    """
    
    key = "收徒"
    aliases = ["accept_disciple"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 收徒 <角色名>")
            return
        
        disciple_name = self.args.strip()
        disciple = search_object(disciple_name)
        
        if not disciple:
            self.caller.msg(f"找不到角色: {disciple_name}")
            return
        
        disciple = disciple[0]
        
        # 建立师徒关系
        from systems.mentorship_manager import MentorshipManager
        success = MentorshipManager.establish_mentorship(
            self.caller, 
            disciple,
            relationship_level="新收弟子",
            notes=f"由{self.caller.key}收为弟子"
        )
        
        if success:
            self.caller.msg(f"你已成功收{disciple.key}为弟子。")
            disciple.msg(f"{self.caller.key}收你为弟子！")
        else:
            self.caller.msg(f"收徒失败。")


class CmdDismissDisciple(Command):
    """
    逐出师门命令
    
    用法:
        逐出师门 <弟子名> [原因]
        dismiss_disciple <disciple_name> [reason]
    
    将弟子逐出师门。
    """
    
    key = "逐出师门"
    aliases = ["dismiss_disciple"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 逐出师门 <弟子名> [原因]")
            return
        
        args = self.args.split(None, 1)
        disciple_name = args[0]
        reason = args[1] if len(args) > 1 else "师父逐出师门"
        
        disciple = search_object(disciple_name)
        if not disciple:
            self.caller.msg(f"找不到弟子: {disciple_name}")
            return
        
        disciple = disciple[0]
        
        # 检查师徒关系
        from systems.mentorship_manager import MentorshipManager
        if not MentorshipManager.is_master_of(self.caller, disciple):
            self.caller.msg(f"{disciple.key}不是你的弟子。")
            return
        
        # 终止师徒关系
        success = MentorshipManager.terminate_mentorship(self.caller, disciple, reason)
        
        if success:
            self.caller.msg(f"你已将{disciple.key}逐出师门。")
            disciple.msg(f"你被{self.caller.key}逐出师门！原因：{reason}")
        else:
            self.caller.msg("逐出师门失败。")


class CmdMentorshipInfo(Command):
    """
    查看师徒信息命令
    
    用法:
        师徒信息 [角色名]
        mentorship [character_name]
    
    查看自己或指定角色的师徒关系信息。
    """
    
    key = "师徒信息"
    aliases = ["mentorship"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if self.args:
            target_name = self.args.strip()
            target = search_object(target_name)
            if not target:
                self.caller.msg(f"找不到角色: {target_name}")
                return
            target = target[0]
        else:
            target = self.caller
        
        from systems.mentorship_manager import MentorshipManager
        
        # 获取师父信息
        master = MentorshipManager.get_master(target)
        if master:
            mentorship_info = MentorshipManager.get_mentorship_info(master, target)
            if mentorship_info:
                self.caller.msg(f"\n|c{target.key} 的师父信息|n")
                self.caller.msg(f"师父: {master.key} ({mentorship_info['master']['realm']})")
                self.caller.msg(f"关系等级: {mentorship_info['relationship']['level']}")
                self.caller.msg(f"师徒关系持续: {int(mentorship_info['relationship']['duration'] / 86400)} 天")
                self.caller.msg(f"传授技能: {', '.join(mentorship_info['relationship']['skills_taught'])}")
        
        # 获取弟子信息
        disciples = MentorshipManager.get_disciples(target)
        if disciples:
            self.caller.msg(f"\n|c{target.key} 的弟子信息|n")
            for disciple in disciples:
                mentorship_info = MentorshipManager.get_mentorship_info(target, disciple)
                if mentorship_info:
                    self.caller.msg(f"弟子: {disciple.key} ({mentorship_info['disciple']['realm']})")
                    self.caller.msg(f"  关系等级: {mentorship_info['relationship']['level']}")
                    self.caller.msg(f"  传授技能: {', '.join(mentorship_info['relationship']['skills_taught'])}")
        
        if not master and not disciples:
            self.caller.msg(f"{target.key} 没有师徒关系。")


class CmdAddFriend(Command):
    """
    添加好友命令
    
    用法:
        加好友 <角色名>
        addfriend <character_name>
    
    向指定角色发送好友请求。
    """
    
    key = "加好友"
    aliases = ["addfriend"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 加好友 <角色名>")
            return
        
        friend_name = self.args.strip()
        friend = search_object(friend_name)
        
        if not friend:
            self.caller.msg(f"找不到角色: {friend_name}")
            return
        
        friend = friend[0]
        
        if friend == self.caller:
            self.caller.msg("不能添加自己为好友。")
            return
        
        # 建立好友关系
        from systems.social_relationship_manager import add_friend
        success = add_friend(
            self.caller, 
            friend,
            notes=f"由{self.caller.key}主动添加"
        )
        
        if success:
            self.caller.msg(f"你已添加{friend.key}为好友。")
            friend.msg(f"{self.caller.key}添加你为好友！")
        else:
            self.caller.msg("添加好友失败。")


class CmdRemoveFriend(Command):
    """
    删除好友命令
    
    用法:
        删好友 <角色名>
        removefriend <character_name>
    
    删除指定的好友关系。
    """
    
    key = "删好友"
    aliases = ["removefriend"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 删好友 <角色名>")
            return
        
        friend_name = self.args.strip()
        friend = search_object(friend_name)
        
        if not friend:
            self.caller.msg(f"找不到角色: {friend_name}")
            return
        
        friend = friend[0]
        
        # 删除好友关系
        from systems.social_relationship_manager import remove_friend
        success = remove_friend(self.caller, friend, "主动删除")
        
        if success:
            self.caller.msg(f"你已删除与{friend.key}的好友关系。")
            friend.msg(f"{self.caller.key}删除了与你的好友关系。")
        else:
            self.caller.msg("删除好友失败。")


class CmdFriendsList(Command):
    """
    查看好友列表命令
    
    用法:
        好友列表
        friends
    
    查看自己的好友列表。
    """
    
    key = "好友列表"
    aliases = ["friends"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        from systems.social_relationship_manager import get_friends
        friends = get_friends(self.caller)
        
        if not friends:
            self.caller.msg("你还没有好友。")
            return
        
        # 创建好友列表表格
        table = evtable.EvTable("好友", "修为", "门派", "最后互动", border="cells")
        
        for friend_data in friends:
            friend_id = friend_data.get("target_id")
            if friend_id:
                friend_objs = search_object(f"#{friend_id}")
                if friend_objs:
                    friend = friend_objs[0]
                    last_interaction = friend_data.get("last_interaction", 0)
                    import time
                    time_diff = int((time.time() - last_interaction) / 86400)
                    
                    table.add_row(
                        friend.key,
                        getattr(friend, '修为境界', '未知'),
                        getattr(friend, '门派归属', '无门派'),
                        f"{time_diff}天前" if time_diff > 0 else "今天"
                    )
        
        self.caller.msg(f"|c你的好友列表|n")
        self.caller.msg(str(table))


class CmdReputation(Command):
    """
    查看声望命令
    
    用法:
        声望 [角色名]
        reputation [character_name]
    
    查看自己或指定角色的声望信息。
    """
    
    key = "声望"
    aliases = ["reputation"]
    locks = "cmd:all()"
    help_category = "社交"
    
    def func(self):
        if self.args:
            target_name = self.args.strip()
            target = search_object(target_name)
            if not target:
                self.caller.msg(f"找不到角色: {target_name}")
                return
            target = target[0]
        else:
            target = self.caller
        
        from systems.social_relationship_manager import ReputationManager
        reputation_info = ReputationManager.get_reputation_info(target)
        
        self.caller.msg(f"\n|c{target.key} 的声望信息|n")
        self.caller.msg(f"声望值: {reputation_info['value']}")
        self.caller.msg(f"声望等级: {reputation_info['level']}")
        self.caller.msg(f"历史记录: {reputation_info['history_count']} 条")
        
        # 显示最近的声望变化
        if reputation_info['recent_changes']:
            self.caller.msg("\n|c最近的声望变化|n")
            for change in reputation_info['recent_changes']:
                change_str = f"{change['change']:+d}" if change['change'] != 0 else "0"
                self.caller.msg(f"  {change_str} - {change['reason']}")


class SocialCmdSet(CmdSet):
    """社交命令集"""
    
    key = "SocialCmdSet"
    
    def at_cmdset_creation(self):
        """添加所有社交命令"""
        self.add(CmdJoinSect())
        self.add(CmdLeaveSect())
        self.add(CmdSectInfo())
        self.add(CmdAcceptDisciple())
        self.add(CmdDismissDisciple())
        self.add(CmdMentorshipInfo())
        self.add(CmdAddFriend())
        self.add(CmdRemoveFriend())
        self.add(CmdFriendsList())
        self.add(CmdReputation())
