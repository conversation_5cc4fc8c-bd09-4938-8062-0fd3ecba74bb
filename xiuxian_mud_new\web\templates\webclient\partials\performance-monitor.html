<!-- 性能监控面板组件 -->
<div class="performance-monitor" id="performance-monitor">
    <div class="monitor-header">
        <h3 class="monitor-title">
            <span class="monitor-icon">📊</span>
            性能监控
        </h3>
        <div class="monitor-controls">
            <button class="monitor-btn" id="toggle-monitor" title="开启/关闭监控">
                <span class="btn-icon">⏸️</span>
            </button>
            <button class="monitor-btn" id="clear-monitor" title="清除数据">
                <span class="btn-icon">🗑️</span>
            </button>
            <button class="monitor-btn" id="export-monitor" title="导出数据">
                <span class="btn-icon">📤</span>
            </button>
        </div>
    </div>

    <div class="monitor-content">
        <!-- 实时指标 -->
        <div class="metrics-section">
            <h4 class="section-title">实时指标</h4>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">CPU使用率</div>
                    <div class="metric-value" id="cpu-usage">0%</div>
                    <div class="metric-bar">
                        <div class="metric-fill" id="cpu-bar"></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">内存使用</div>
                    <div class="metric-value" id="memory-usage">0MB</div>
                    <div class="metric-bar">
                        <div class="metric-fill" id="memory-bar"></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">网络延迟</div>
                    <div class="metric-value" id="network-latency">0ms</div>
                    <div class="metric-bar">
                        <div class="metric-fill" id="latency-bar"></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">帧率(FPS)</div>
                    <div class="metric-value" id="fps-value">60</div>
                    <div class="metric-bar">
                        <div class="metric-fill" id="fps-bar"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="status-section">
            <h4 class="section-title">系统状态</h4>
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-label">WebSocket连接</span>
                    <span class="status-indicator" id="ws-indicator">●</span>
                    <span class="status-text" id="ws-status">连接中</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">AI导演系统</span>
                    <span class="status-indicator" id="ai-indicator">●</span>
                    <span class="status-text" id="ai-status">运行中</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">缓存系统</span>
                    <span class="status-indicator" id="cache-indicator">●</span>
                    <span class="status-text" id="cache-status">正常</span>
                </div>
                
                <div class="status-item">
                    <span class="status-label">数据库连接</span>
                    <span class="status-indicator" id="db-indicator">●</span>
                    <span class="status-text" id="db-status">正常</span>
                </div>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="charts-section">
            <h4 class="section-title">性能趋势</h4>
            <div class="chart-tabs">
                <button class="chart-tab active" data-chart="cpu">CPU</button>
                <button class="chart-tab" data-chart="memory">内存</button>
                <button class="chart-tab" data-chart="network">网络</button>
                <button class="chart-tab" data-chart="fps">帧率</button>
            </div>
            <div class="chart-container">
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 警告和建议 -->
        <div class="alerts-section">
            <h4 class="section-title">性能警告</h4>
            <div class="alerts-list" id="alerts-list">
                <!-- 警告项目将通过JavaScript动态添加 -->
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="stats-section">
            <h4 class="section-title">详细统计</h4>
            <div class="stats-table">
                <div class="stats-row">
                    <span class="stats-label">页面加载时间</span>
                    <span class="stats-value" id="page-load-time">0ms</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">DOM节点数量</span>
                    <span class="stats-value" id="dom-nodes">0</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">事件监听器</span>
                    <span class="stats-value" id="event-listeners">0</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">WebSocket消息</span>
                    <span class="stats-value" id="ws-messages">0</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">缓存命中率</span>
                    <span class="stats-value" id="cache-hit-rate">0%</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">平均响应时间</span>
                    <span class="stats-value" id="avg-response-time">0ms</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.performance-monitor {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

.monitor-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.monitor-icon {
    font-size: 1.2rem;
}

.monitor-controls {
    display: flex;
    gap: 8px;
}

.monitor-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 0.9rem;
}

.monitor-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.monitor-content {
    padding: 20px;
}

.section-title {
    margin: 0 0 15px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #8B4513;
    padding-bottom: 5px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.metric-card {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.metric-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 10px;
}

.metric-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.status-section {
    margin-bottom: 25px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    gap: 10px;
}

.status-label {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.status-indicator {
    font-size: 1.2rem;
    color: #28a745;
}

.status-indicator.warning {
    color: #ffc107;
}

.status-indicator.error {
    color: #dc3545;
}

.status-text {
    font-size: 0.9rem;
    color: #666;
    min-width: 60px;
}

.charts-section {
    margin-bottom: 25px;
}

.chart-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.chart-tab {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.chart-tab.active {
    background: #8B4513;
    color: white;
    border-color: #8B4513;
}

.chart-container {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    height: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.alerts-section {
    margin-bottom: 25px;
}

.alerts-list {
    max-height: 150px;
    overflow-y: auto;
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    gap: 10px;
}

.alert-item.warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.alert-item.error {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

.alert-icon {
    font-size: 1.1rem;
}

.alert-text {
    flex: 1;
    font-size: 0.9rem;
}

.alert-time {
    font-size: 0.8rem;
    color: #666;
}

.stats-section {
    margin-bottom: 15px;
}

.stats-table {
    background: #f8f9fa;
    border-radius: 6px;
    overflow: hidden;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
}

.stats-row:last-child {
    border-bottom: none;
}

.stats-label {
    font-weight: 500;
    color: #333;
}

.stats-value {
    font-weight: 600;
    color: #8B4513;
}

@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-tabs {
        flex-wrap: wrap;
    }
    
    .monitor-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
</style>

<script>
// 性能监控管理器
window.PerformanceMonitor = {
    // 配置
    config: {
        updateInterval: 1000, // 1秒更新一次
        maxDataPoints: 60, // 保留60个数据点
        alertThresholds: {
            cpu: 80, // CPU使用率警告阈值
            memory: 500, // 内存使用警告阈值(MB)
            latency: 1000, // 网络延迟警告阈值(ms)
            fps: 30 // 帧率警告阈值
        }
    },

    // 状态
    state: {
        isMonitoring: true,
        currentChart: 'cpu',
        data: {
            cpu: [],
            memory: [],
            network: [],
            fps: []
        },
        alerts: [],
        stats: {
            pageLoadTime: 0,
            domNodes: 0,
            eventListeners: 0,
            wsMessages: 0,
            cacheHitRate: 0,
            avgResponseTime: 0
        }
    },

    // 初始化
    init: function() {
        this.bindEvents();
        this.startMonitoring();
        this.initializeChart();
        this.calculateInitialStats();
        console.log('PerformanceMonitor initialized');
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 监控控制按钮
        document.getElementById('toggle-monitor').addEventListener('click', function() {
            self.toggleMonitoring();
        });

        document.getElementById('clear-monitor').addEventListener('click', function() {
            self.clearData();
        });

        document.getElementById('export-monitor').addEventListener('click', function() {
            self.exportData();
        });

        // 图表切换
        document.querySelectorAll('.chart-tab').forEach(function(tab) {
            tab.addEventListener('click', function() {
                const chartType = this.getAttribute('data-chart');
                self.switchChart(chartType);
            });
        });
    },

    // 开始监控
    startMonitoring: function() {
        if (this.monitoringTimer) return;

        const self = this;
        this.monitoringTimer = setInterval(function() {
            if (self.state.isMonitoring) {
                self.collectMetrics();
                self.updateDisplay();
                self.checkAlerts();
            }
        }, this.config.updateInterval);
    },

    // 停止监控
    stopMonitoring: function() {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
    },

    // 切换监控状态
    toggleMonitoring: function() {
        this.state.isMonitoring = !this.state.isMonitoring;
        const button = document.getElementById('toggle-monitor');
        const icon = button.querySelector('.btn-icon');

        if (this.state.isMonitoring) {
            icon.textContent = '⏸️';
            button.title = '暂停监控';
        } else {
            icon.textContent = '▶️';
            button.title = '开始监控';
        }
    },

    // 收集性能指标
    collectMetrics: function() {
        const metrics = {
            cpu: this.getCPUUsage(),
            memory: this.getMemoryUsage(),
            network: this.getNetworkLatency(),
            fps: this.getFPS()
        };

        // 添加到数据数组
        Object.keys(metrics).forEach(key => {
            this.state.data[key].push({
                timestamp: Date.now(),
                value: metrics[key]
            });

            // 限制数据点数量
            if (this.state.data[key].length > this.config.maxDataPoints) {
                this.state.data[key].shift();
            }
        });

        return metrics;
    },

    // 获取CPU使用率（模拟）
    getCPUUsage: function() {
        // 实际实现中可以使用Performance API
        return Math.random() * 100;
    },

    // 获取内存使用（模拟）
    getMemoryUsage: function() {
        if (performance.memory) {
            return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        }
        return Math.random() * 200 + 50;
    },

    // 获取网络延迟
    getNetworkLatency: function() {
        // 可以通过ping测试或WebSocket延迟计算
        return Math.random() * 200 + 50;
    },

    // 获取帧率
    getFPS: function() {
        // 实际实现中可以使用requestAnimationFrame计算
        return Math.floor(Math.random() * 30) + 30;
    },

    // 更新显示
    updateDisplay: function() {
        const latestData = {};
        Object.keys(this.state.data).forEach(key => {
            const data = this.state.data[key];
            latestData[key] = data.length > 0 ? data[data.length - 1].value : 0;
        });

        // 更新指标值
        document.getElementById('cpu-usage').textContent = Math.round(latestData.cpu) + '%';
        document.getElementById('memory-usage').textContent = Math.round(latestData.memory) + 'MB';
        document.getElementById('network-latency').textContent = Math.round(latestData.network) + 'ms';
        document.getElementById('fps-value').textContent = Math.round(latestData.fps);

        // 更新进度条
        this.updateProgressBar('cpu-bar', latestData.cpu, 100);
        this.updateProgressBar('memory-bar', latestData.memory, 500);
        this.updateProgressBar('latency-bar', latestData.network, 500);
        this.updateProgressBar('fps-bar', latestData.fps, 60);

        // 更新系统状态
        this.updateSystemStatus();

        // 更新图表
        this.updateChart();
    },

    // 更新进度条
    updateProgressBar: function(barId, value, max) {
        const bar = document.getElementById(barId);
        const percentage = Math.min((value / max) * 100, 100);
        bar.style.width = percentage + '%';

        // 根据值设置颜色
        if (percentage < 50) {
            bar.style.background = '#28a745';
        } else if (percentage < 80) {
            bar.style.background = '#ffc107';
        } else {
            bar.style.background = '#dc3545';
        }
    },

    // 更新系统状态
    updateSystemStatus: function() {
        const statuses = {
            ws: Math.random() > 0.1 ? 'normal' : 'error',
            ai: Math.random() > 0.05 ? 'normal' : 'warning',
            cache: Math.random() > 0.08 ? 'normal' : 'warning',
            db: Math.random() > 0.12 ? 'normal' : 'error'
        };

        Object.keys(statuses).forEach(key => {
            const indicator = document.getElementById(key + '-indicator');
            const statusText = document.getElementById(key + '-status');

            indicator.className = 'status-indicator';

            switch (statuses[key]) {
                case 'normal':
                    indicator.style.color = '#28a745';
                    statusText.textContent = '正常';
                    break;
                case 'warning':
                    indicator.style.color = '#ffc107';
                    statusText.textContent = '警告';
                    break;
                case 'error':
                    indicator.style.color = '#dc3545';
                    statusText.textContent = '错误';
                    break;
            }
        });
    },

    // 检查警告
    checkAlerts: function() {
        const latestData = {};
        Object.keys(this.state.data).forEach(key => {
            const data = this.state.data[key];
            latestData[key] = data.length > 0 ? data[data.length - 1].value : 0;
        });

        // 检查各项指标
        if (latestData.cpu > this.config.alertThresholds.cpu) {
            this.addAlert('warning', 'CPU使用率过高: ' + Math.round(latestData.cpu) + '%');
        }

        if (latestData.memory > this.config.alertThresholds.memory) {
            this.addAlert('warning', '内存使用过高: ' + Math.round(latestData.memory) + 'MB');
        }

        if (latestData.network > this.config.alertThresholds.latency) {
            this.addAlert('error', '网络延迟过高: ' + Math.round(latestData.network) + 'ms');
        }

        if (latestData.fps < this.config.alertThresholds.fps) {
            this.addAlert('warning', '帧率过低: ' + Math.round(latestData.fps) + 'FPS');
        }
    },

    // 添加警告
    addAlert: function(type, message) {
        const alert = {
            type: type,
            message: message,
            timestamp: Date.now()
        };

        // 避免重复警告
        const exists = this.state.alerts.some(a =>
            a.message === message && (Date.now() - a.timestamp) < 5000
        );

        if (!exists) {
            this.state.alerts.unshift(alert);

            // 限制警告数量
            if (this.state.alerts.length > 10) {
                this.state.alerts = this.state.alerts.slice(0, 10);
            }

            this.updateAlertsDisplay();
        }
    },

    // 更新警告显示
    updateAlertsDisplay: function() {
        const container = document.getElementById('alerts-list');
        container.innerHTML = '';

        if (this.state.alerts.length === 0) {
            container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无警告</div>';
            return;
        }

        this.state.alerts.forEach(alert => {
            const item = document.createElement('div');
            item.className = `alert-item ${alert.type}`;

            const timeAgo = this.getTimeAgo(alert.timestamp);

            item.innerHTML = `
                <span class="alert-icon">${alert.type === 'warning' ? '⚠️' : '❌'}</span>
                <span class="alert-text">${alert.message}</span>
                <span class="alert-time">${timeAgo}</span>
            `;

            container.appendChild(item);
        });
    },

    // 获取时间差
    getTimeAgo: function(timestamp) {
        const diff = Date.now() - timestamp;
        const seconds = Math.floor(diff / 1000);

        if (seconds < 60) return seconds + '秒前';
        if (seconds < 3600) return Math.floor(seconds / 60) + '分钟前';
        return Math.floor(seconds / 3600) + '小时前';
    },

    // 初始化图表
    initializeChart: function() {
        const canvas = document.getElementById('performance-chart');
        this.chartContext = canvas.getContext('2d');
        this.updateChart();
    },

    // 切换图表
    switchChart: function(chartType) {
        // 更新标签状态
        document.querySelectorAll('.chart-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

        this.state.currentChart = chartType;
        this.updateChart();
    },

    // 更新图表
    updateChart: function() {
        if (!this.chartContext) return;

        const canvas = this.chartContext.canvas;
        const width = canvas.width;
        const height = canvas.height;

        // 清除画布
        this.chartContext.clearRect(0, 0, width, height);

        const data = this.state.data[this.state.currentChart];
        if (data.length === 0) return;

        // 绘制网格
        this.drawGrid(width, height);

        // 绘制数据线
        this.drawDataLine(data, width, height);

        // 绘制标签
        this.drawLabels(data, width, height);
    },

    // 绘制网格
    drawGrid: function(width, height) {
        const ctx = this.chartContext;
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 1;

        // 垂直网格线
        for (let i = 0; i <= 10; i++) {
            const x = (width / 10) * i;
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }

        // 水平网格线
        for (let i = 0; i <= 5; i++) {
            const y = (height / 5) * i;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
    },

    // 绘制数据线
    drawDataLine: function(data, width, height) {
        if (data.length < 2) return;

        const ctx = this.chartContext;
        const maxValue = Math.max(...data.map(d => d.value));
        const minValue = Math.min(...data.map(d => d.value));
        const range = maxValue - minValue || 1;

        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 2;
        ctx.beginPath();

        data.forEach((point, index) => {
            const x = (width / (data.length - 1)) * index;
            const y = height - ((point.value - minValue) / range) * height;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // 绘制数据点
        ctx.fillStyle = '#8B4513';
        data.forEach((point, index) => {
            const x = (width / (data.length - 1)) * index;
            const y = height - ((point.value - minValue) / range) * height;

            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    },

    // 绘制标签
    drawLabels: function(data, width, height) {
        const ctx = this.chartContext;
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';

        // Y轴标签
        const maxValue = Math.max(...data.map(d => d.value));
        const minValue = Math.min(...data.map(d => d.value));

        for (let i = 0; i <= 5; i++) {
            const value = minValue + ((maxValue - minValue) / 5) * (5 - i);
            const y = (height / 5) * i;
            ctx.fillText(Math.round(value), 5, y + 4);
        }
    },

    // 计算初始统计
    calculateInitialStats: function() {
        // 页面加载时间
        if (performance.timing) {
            this.state.stats.pageLoadTime =
                performance.timing.loadEventEnd - performance.timing.navigationStart;
        }

        // DOM节点数量
        this.state.stats.domNodes = document.querySelectorAll('*').length;

        // 更新统计显示
        this.updateStatsDisplay();
    },

    // 更新统计显示
    updateStatsDisplay: function() {
        document.getElementById('page-load-time').textContent =
            this.state.stats.pageLoadTime + 'ms';
        document.getElementById('dom-nodes').textContent =
            this.state.stats.domNodes;
        document.getElementById('event-listeners').textContent =
            this.state.stats.eventListeners;
        document.getElementById('ws-messages').textContent =
            this.state.stats.wsMessages;
        document.getElementById('cache-hit-rate').textContent =
            this.state.stats.cacheHitRate + '%';
        document.getElementById('avg-response-time').textContent =
            this.state.stats.avgResponseTime + 'ms';
    },

    // 清除数据
    clearData: function() {
        Object.keys(this.state.data).forEach(key => {
            this.state.data[key] = [];
        });
        this.state.alerts = [];
        this.updateDisplay();
        this.updateAlertsDisplay();
        this.updateChart();
    },

    // 导出数据
    exportData: function() {
        const exportData = {
            timestamp: new Date().toISOString(),
            metrics: this.state.data,
            alerts: this.state.alerts,
            stats: this.state.stats,
            config: this.config
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)],
            { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    },

    // 销毁监控器
    destroy: function() {
        this.stopMonitoring();
        console.log('PerformanceMonitor destroyed');
    }
};

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.PerformanceMonitor) {
        window.PerformanceMonitor.init();
    }
});

// 导出到全局
window.performanceMonitor = window.PerformanceMonitor;
</script>
