{% extends "website/base.html" %}
{% load static %}

{% block title %}仙侠MUD - 管理仪表板{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{% static 'webclient/css/xiuxian.css' %}">
<link rel="stylesheet" href="{% static 'webclient/css/xiuxian-components.css' %}">
<style>
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(139, 69, 19, 0.3);
        text-align: center;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #8B4513;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .stat-icon {
        font-size: 2rem;
        margin-right: 15px;
        color: #8B4513;
    }

    .stat-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #8B4513;
        margin-bottom: 5px;
    }

    .stat-description {
        color: #666;
        font-size: 0.9rem;
    }

    .charts-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .chart-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }

    .chart-placeholder {
        height: 300px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 1.1rem;
    }

    .activity-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .activity-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
    }

    .activity-filter {
        display: flex;
        gap: 10px;
    }

    .filter-btn {
        padding: 8px 16px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-btn.active {
        background: #8B4513;
        color: white;
        border-color: #8B4513;
    }

    .activity-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #8B4513;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-text {
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
    }

    .activity-time {
        color: #666;
        font-size: 0.9rem;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }

    .action-btn {
        background: linear-gradient(135deg, #8B4513 0%, #DAA520 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 15px 20px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
        text-decoration: none;
        color: white;
    }

    .system-status {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .status-header {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .status-label {
        font-weight: 500;
        color: #333;
    }

    .status-value {
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .status-online {
        background: #d4edda;
        color: #155724;
    }

    .status-warning {
        background: #fff3cd;
        color: #856404;
    }

    .status-error {
        background: #f8d7da;
        color: #721c24;
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px;
        }
        
        .charts-section {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .dashboard-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- 仪表板标题 -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">仙侠MUD管理仪表板</h1>
        <p class="dashboard-subtitle">实时监控游戏状态，管理玩家数据，优化系统性能</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">👥</div>
                <div class="stat-title">在线玩家</div>
            </div>
            <div class="stat-value" id="online-players">0</div>
            <div class="stat-description">当前在线用户数量</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">📊</div>
                <div class="stat-title">总注册用户</div>
            </div>
            <div class="stat-value" id="total-users">0</div>
            <div class="stat-description">累计注册用户数量</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">🏛️</div>
                <div class="stat-title">活跃门派</div>
            </div>
            <div class="stat-value" id="active-sects">0</div>
            <div class="stat-description">当前活跃的门派数量</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">⚔️</div>
                <div class="stat-title">今日战斗</div>
            </div>
            <div class="stat-value" id="daily-battles">0</div>
            <div class="stat-description">今日发生的战斗次数</div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
        <div class="chart-container">
            <h3 class="chart-title">玩家活跃度趋势</h3>
            <div class="chart-placeholder" id="activity-chart">
                图表加载中...
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">境界分布统计</h3>
            <div class="chart-placeholder" id="realm-chart">
                图表加载中...
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
        <a href="/admin/" class="action-btn">
            <span>⚙️</span>
            系统管理
        </a>
        <a href="/webclient/" class="action-btn">
            <span>🎮</span>
            进入游戏
        </a>
        <button class="action-btn" onclick="refreshData()">
            <span>🔄</span>
            刷新数据
        </button>
        <button class="action-btn" onclick="exportData()">
            <span>📊</span>
            导出报告
        </button>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
        <div class="activity-header">
            <h3 class="activity-title">最近活动</h3>
            <div class="activity-filter">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="login">登录</button>
                <button class="filter-btn" data-filter="battle">战斗</button>
                <button class="filter-btn" data-filter="cultivation">修炼</button>
            </div>
        </div>
        <div class="activity-list" id="activity-list">
            <!-- 活动项目将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="system-status">
        <h3 class="status-header">系统状态</h3>
        <div class="status-grid">
            <div class="status-item">
                <span class="status-label">数据库连接</span>
                <span class="status-value status-online" id="db-status">正常</span>
            </div>
            <div class="status-item">
                <span class="status-label">WebSocket服务</span>
                <span class="status-value status-online" id="ws-status">正常</span>
            </div>
            <div class="status-item">
                <span class="status-label">AI导演系统</span>
                <span class="status-value status-online" id="ai-status">正常</span>
            </div>
            <div class="status-item">
                <span class="status-label">缓存系统</span>
                <span class="status-value status-online" id="cache-status">正常</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'webclient/js/xiuxian-client.js' %}"></script>
<script>
// 仙侠MUD仪表板管理器
const XiuxianDashboard = {
    // 配置
    config: {
        refreshInterval: 30000, // 30秒刷新一次
        maxActivityItems: 50,
        chartColors: {
            primary: '#8B4513',
            secondary: '#DAA520',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545'
        }
    },

    // 状态
    state: {
        isLoading: false,
        lastUpdate: null,
        currentFilter: 'all'
    },

    // 初始化
    init: function() {
        this.bindEvents();
        this.loadInitialData();
        this.startAutoRefresh();
        console.log('XiuxianDashboard initialized');
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 过滤按钮事件
        document.querySelectorAll('.filter-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                self.setActivityFilter(filter);
            });
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                self.refreshData();
            }
        });
    },

    // 加载初始数据
    loadInitialData: function() {
        this.loadStatistics();
        this.loadActivityData();
        this.loadSystemStatus();
        this.initializeCharts();
    },

    // 加载统计数据
    loadStatistics: function() {
        // 模拟数据加载
        const stats = {
            onlinePlayers: Math.floor(Math.random() * 100) + 20,
            totalUsers: Math.floor(Math.random() * 1000) + 500,
            activeSects: Math.floor(Math.random() * 10) + 5,
            dailyBattles: Math.floor(Math.random() * 200) + 50
        };

        this.updateStatistics(stats);
    },

    // 更新统计数据
    updateStatistics: function(stats) {
        this.animateNumber('online-players', stats.onlinePlayers);
        this.animateNumber('total-users', stats.totalUsers);
        this.animateNumber('active-sects', stats.activeSects);
        this.animateNumber('daily-battles', stats.dailyBattles);
    },

    // 数字动画
    animateNumber: function(elementId, targetValue) {
        const element = document.getElementById(elementId);
        const currentValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 20;
        let current = currentValue;

        const timer = setInterval(function() {
            current += increment;
            if ((increment > 0 && current >= targetValue) ||
                (increment < 0 && current <= targetValue)) {
                current = targetValue;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 50);
    },

    // 加载活动数据
    loadActivityData: function() {
        // 模拟活动数据
        const activities = [
            { type: 'login', icon: '🔐', text: '玩家"剑仙李白"登录游戏', time: '2分钟前' },
            { type: 'battle', icon: '⚔️', text: '玩家"武当张三丰"击败了"少林玄慈"', time: '5分钟前' },
            { type: 'cultivation', icon: '🧘', text: '玩家"峨眉周芷若"突破到金丹期', time: '8分钟前' },
            { type: 'login', icon: '🔐', text: '玩家"华山令狐冲"登录游戏', time: '12分钟前' },
            { type: 'battle', icon: '⚔️', text: '门派战：武当派 vs 少林寺开始', time: '15分钟前' },
            { type: 'cultivation', icon: '🧘', text: '玩家"昆仑何太冲"学会了"太极剑法"', time: '18分钟前' }
        ];

        this.updateActivityList(activities);
    },

    // 更新活动列表
    updateActivityList: function(activities) {
        const container = document.getElementById('activity-list');
        container.innerHTML = '';

        const filteredActivities = this.state.currentFilter === 'all'
            ? activities
            : activities.filter(activity => activity.type === this.state.currentFilter);

        filteredActivities.forEach(function(activity) {
            const item = document.createElement('div');
            item.className = 'activity-item';
            item.innerHTML = `
                <div class="activity-icon">${activity.icon}</div>
                <div class="activity-content">
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            `;
            container.appendChild(item);
        });
    },

    // 设置活动过滤器
    setActivityFilter: function(filter) {
        // 更新按钮状态
        document.querySelectorAll('.filter-btn').forEach(function(btn) {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        this.state.currentFilter = filter;
        this.loadActivityData(); // 重新加载数据
    },

    // 加载系统状态
    loadSystemStatus: function() {
        // 模拟系统状态检查
        const statuses = {
            database: Math.random() > 0.1 ? 'online' : 'error',
            websocket: Math.random() > 0.05 ? 'online' : 'warning',
            ai: Math.random() > 0.15 ? 'online' : 'warning',
            cache: Math.random() > 0.08 ? 'online' : 'error'
        };

        this.updateSystemStatus(statuses);
    },

    // 更新系统状态
    updateSystemStatus: function(statuses) {
        const statusMap = {
            online: { text: '正常', class: 'status-online' },
            warning: { text: '警告', class: 'status-warning' },
            error: { text: '错误', class: 'status-error' }
        };

        Object.keys(statuses).forEach(function(key) {
            const elementId = key === 'database' ? 'db-status' :
                             key === 'websocket' ? 'ws-status' :
                             key === 'ai' ? 'ai-status' : 'cache-status';

            const element = document.getElementById(elementId);
            const status = statusMap[statuses[key]];

            element.textContent = status.text;
            element.className = 'status-value ' + status.class;
        });
    },

    // 初始化图表
    initializeCharts: function() {
        // 这里可以集成Chart.js或其他图表库
        // 目前使用简单的占位符
        setTimeout(function() {
            document.getElementById('activity-chart').innerHTML =
                '<div style="text-align: center; color: #666;">活跃度图表 (需要Chart.js)</div>';
            document.getElementById('realm-chart').innerHTML =
                '<div style="text-align: center; color: #666;">境界分布图 (需要Chart.js)</div>';
        }, 1000);
    },

    // 刷新数据
    refreshData: function() {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoadingIndicator();

        // 模拟异步数据加载
        setTimeout(() => {
            this.loadStatistics();
            this.loadActivityData();
            this.loadSystemStatus();
            this.state.lastUpdate = new Date();
            this.state.isLoading = false;
            this.hideLoadingIndicator();
        }, 1000);
    },

    // 显示加载指示器
    showLoadingIndicator: function() {
        // 可以添加加载动画
        console.log('Loading data...');
    },

    // 隐藏加载指示器
    hideLoadingIndicator: function() {
        console.log('Data loaded');
    },

    // 开始自动刷新
    startAutoRefresh: function() {
        const self = this;
        setInterval(function() {
            if (!document.hidden) {
                self.refreshData();
            }
        }, this.config.refreshInterval);
    },

    // 导出数据
    exportData: function() {
        // 模拟数据导出
        const data = {
            timestamp: new Date().toISOString(),
            statistics: {
                onlinePlayers: document.getElementById('online-players').textContent,
                totalUsers: document.getElementById('total-users').textContent,
                activeSects: document.getElementById('active-sects').textContent,
                dailyBattles: document.getElementById('daily-battles').textContent
            }
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `xiuxian-dashboard-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
};

// 全局函数
function refreshData() {
    XiuxianDashboard.refreshData();
}

function exportData() {
    XiuxianDashboard.exportData();
}

// 初始化仪表板
document.addEventListener('DOMContentLoaded', function() {
    XiuxianDashboard.init();
});
</script>
{% endblock %}
