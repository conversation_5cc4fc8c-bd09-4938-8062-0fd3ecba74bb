"""
仙侠社交事件系统

定义所有社交相关的事件类型，包括：
- 门派事件：入门、退门、晋升、惩罚等
- 师徒事件：收徒、逐出师门、师徒决裂等
- 关系事件：结交、决裂、复仇、和解等
- 声望事件：声望变化、名声传播等

所有事件都基于现有的BaseEvent架构，与XianxiaEventBus完全兼容。
"""

import time
from enum import Enum
from typing import Dict, Any, Optional, List
from systems.event_system import BaseEvent, EventPriority


class SocialEventType(Enum):
    """社交事件类型枚举"""

    # 门派事件
    SECT_JOIN = "sect_join"                    # 加入门派
    SECT_LEAVE = "sect_leave"                  # 离开门派
    SECT_PROMOTION = "sect_promotion"          # 门派晋升
    SECT_DEMOTION = "sect_demotion"           # 门派降级
    SECT_PUNISHMENT = "sect_punishment"        # 门派惩罚
    SECT_REWARD = "sect_reward"               # 门派奖励
    SECT_MESSAGE = "sect_message"             # 门派消息
    SECT_MEETING = "sect_meeting"             # 门派集会
    SECT_MISSION = "sect_mission"             # 门派任务
    SECT_WAR = "sect_war"                     # 门派战争
    SECT_ALLIANCE = "sect_alliance"           # 门派结盟
    SECT_DISSOLUTION = "sect_dissolution"      # 门派解散

    # 师徒事件
    MENTORSHIP_ESTABLISHED = "mentorship_established"    # 建立师徒关系
    MENTORSHIP_TERMINATED = "mentorship_terminated"      # 终止师徒关系
    SKILL_TEACHING = "skill_teaching"                    # 技能传授
    CULTIVATION_GUIDANCE = "cultivation_guidance"        # 修炼指导
    MASTER_DISCIPLE_DIALOGUE = "master_disciple_dialogue"  # 师徒对话
    DISCIPLE_GRADUATION = "disciple_graduation"          # 弟子出师
    MASTER_PUNISHMENT = "master_punishment"              # 师父惩罚弟子
    MASTER_REWARD = "master_reward"                      # 师父奖励弟子

    # 关系事件
    FRIENDSHIP_ESTABLISHED = "friendship_established"    # 建立友谊
    FRIENDSHIP_BROKEN = "friendship_broken"              # 友谊破裂
    ENEMY_DECLARED = "enemy_declared"                    # 宣布敌对
    ENEMY_RECONCILED = "enemy_reconciled"                # 敌对和解
    ALLIANCE_FORMED = "alliance_formed"                  # 结成联盟
    ALLIANCE_BROKEN = "alliance_broken"                  # 联盟破裂
    MARRIAGE_PROPOSAL = "marriage_proposal"              # 求婚
    MARRIAGE_CEREMONY = "marriage_ceremony"              # 结婚典礼
    DIVORCE = "divorce"                                  # 离婚

    # 声望事件
    REPUTATION_INCREASE = "reputation_increase"          # 声望提升
    REPUTATION_DECREASE = "reputation_decrease"          # 声望下降
    FAME_SPREAD = "fame_spread"                         # 名声传播
    INFAMY_SPREAD = "infamy_spread"                     # 恶名传播
    TITLE_GRANTED = "title_granted"                     # 获得称号
    TITLE_REVOKED = "title_revoked"                     # 失去称号

    # 社交互动事件
    SOCIAL_GATHERING = "social_gathering"               # 社交聚会
    GIFT_EXCHANGE = "gift_exchange"                     # 礼物交换
    FAVOR_REQUEST = "favor_request"                     # 请求帮助
    FAVOR_GRANTED = "favor_granted"                     # 提供帮助
    INSULT_DELIVERED = "insult_delivered"               # 侮辱行为
    APOLOGY_MADE = "apology_made"                       # 道歉行为
    CHALLENGE_ISSUED = "challenge_issued"               # 发起挑战
    CHALLENGE_ACCEPTED = "challenge_accepted"           # 接受挑战


class SocialInteractionEvent(BaseEvent):
    """
    社交互动事件基类

    用于所有涉及角色间社交互动的事件
    """

    def __init__(self, event_type: str, character, target=None,
                 priority: EventPriority = EventPriority.NORMAL, **kwargs):
        """
        初始化社交互动事件

        Args:
            event_type: 事件类型
            character: 主要角色
            target: 目标角色（可选）
            priority: 事件优先级
            **kwargs: 额外的事件数据
        """
        super().__init__(event_type, priority)
        self.character = character
        self.target = target
        self.social_data = kwargs

        # 添加角色信息到事件数据
        self.event_data.update({
            "character_id": character.id if character else None,
            "character_name": character.key if character else None,
            "target_id": target.id if target else None,
            "target_name": target.key if target else None,
            "social_data": self.social_data
        })

    def get_character_info(self) -> Dict[str, Any]:
        """获取角色信息"""
        if not self.character:
            return {}

        return {
            "id": self.character.id,
            "name": self.character.key,
            "realm": getattr(self.character, '修为境界', '未知'),
            "sect": getattr(self.character, '门派归属', '无门派'),
            "profession": getattr(self.character, '职业类型', '散修')
        }

    def get_target_info(self) -> Dict[str, Any]:
        """获取目标信息"""
        if not self.target:
            return {}

        return {
            "id": self.target.id,
            "name": self.target.key,
            "realm": getattr(self.target, '修为境界', '未知'),
            "sect": getattr(self.target, '门派归属', '无门派'),
            "profession": getattr(self.target, '职业类型', '散修')
        }


class SectEvent(SocialInteractionEvent):
    """
    门派事件类

    处理所有与门派相关的事件
    """

    def __init__(self, event_type: str, sect_name: str, character,
                 priority: EventPriority = EventPriority.NORMAL, **kwargs):
        """
        初始化门派事件

        Args:
            event_type: 事件类型
            sect_name: 门派名称
            character: 相关角色
            priority: 事件优先级
            **kwargs: 额外的门派数据
        """
        super().__init__(event_type, character, priority=priority, **kwargs)
        self.sect_name = sect_name

        # 添加门派信息到事件数据
        self.event_data.update({
            "sect_name": sect_name,
            "sect_data": kwargs
        })

    def get_sect_info(self) -> Dict[str, Any]:
        """获取门派信息"""
        return {
            "name": self.sect_name,
            "member_count": self.social_data.get("member_count", 0),
            "sect_level": self.social_data.get("sect_level", "普通门派"),
            "sect_reputation": self.social_data.get("sect_reputation", 0)
        }


class MentorshipEvent(SocialInteractionEvent):
    """
    师徒关系事件类

    处理所有与师徒关系相关的事件
    """

    def __init__(self, event_type: str, master, disciple,
                 priority: EventPriority = EventPriority.HIGH, **kwargs):
        """
        初始化师徒事件

        Args:
            event_type: 事件类型
            master: 师父角色
            disciple: 弟子角色
            priority: 事件优先级（师徒事件通常是高优先级）
            **kwargs: 额外的师徒数据
        """
        super().__init__(event_type, master, target=disciple, priority=priority, **kwargs)
        self.master = master
        self.disciple = disciple

        # 添加师徒关系信息到事件数据
        self.event_data.update({
            "master_id": master.id if master else None,
            "master_name": master.key if master else None,
            "disciple_id": disciple.id if disciple else None,
            "disciple_name": disciple.key if disciple else None,
            "mentorship_data": kwargs
        })

    def get_mentorship_info(self) -> Dict[str, Any]:
        """获取师徒关系信息"""
        return {
            "master": self.get_character_info(),
            "disciple": self.get_target_info(),
            "relationship_duration": self.social_data.get("relationship_duration", 0),
            "skills_taught": self.social_data.get("skills_taught", []),
            "relationship_level": self.social_data.get("relationship_level", "新收弟子")
        }


class RelationshipEvent(SocialInteractionEvent):
    """
    关系事件类

    处理角色间各种关系变化的事件
    """

    def __init__(self, event_type: str, character, target, relationship_type: str,
                 priority: EventPriority = EventPriority.NORMAL, **kwargs):
        """
        初始化关系事件

        Args:
            event_type: 事件类型
            character: 主要角色
            target: 目标角色
            relationship_type: 关系类型（友谊、敌对、联盟等）
            priority: 事件优先级
            **kwargs: 额外的关系数据
        """
        super().__init__(event_type, character, target, priority, **kwargs)
        self.relationship_type = relationship_type

        # 添加关系信息到事件数据
        self.event_data.update({
            "relationship_type": relationship_type,
            "relationship_data": kwargs
        })

    def get_relationship_info(self) -> Dict[str, Any]:
        """获取关系信息"""
        return {
            "type": self.relationship_type,
            "strength": self.social_data.get("relationship_strength", 0),
            "duration": self.social_data.get("relationship_duration", 0),
            "status": self.social_data.get("relationship_status", "active"),
            "history": self.social_data.get("relationship_history", [])
        }


class ReputationEvent(SocialInteractionEvent):
    """
    声望事件类

    处理角色声望和名声相关的事件
    """

    def __init__(self, event_type: str, character, reputation_change: int,
                 reason: str, priority: EventPriority = EventPriority.NORMAL, **kwargs):
        """
        初始化声望事件

        Args:
            event_type: 事件类型
            character: 相关角色
            reputation_change: 声望变化值
            reason: 声望变化原因
            priority: 事件优先级
            **kwargs: 额外的声望数据
        """
        super().__init__(event_type, character, priority=priority, **kwargs)
        self.reputation_change = reputation_change
        self.reason = reason

        # 添加声望信息到事件数据
        self.event_data.update({
            "reputation_change": reputation_change,
            "reason": reason,
            "reputation_data": kwargs
        })

    def get_reputation_info(self) -> Dict[str, Any]:
        """获取声望信息"""
        return {
            "change": self.reputation_change,
            "reason": self.reason,
            "current_reputation": self.social_data.get("current_reputation", 0),
            "reputation_level": self.social_data.get("reputation_level", "无名"),
            "affected_regions": self.social_data.get("affected_regions", [])
        }


# 便利函数：创建各种社交事件

def create_sect_join_event(character, sect_name: str, **kwargs) -> SectEvent:
    """创建加入门派事件"""
    return SectEvent(
        SocialEventType.SECT_JOIN.value,
        sect_name,
        character,
        priority=EventPriority.HIGH,
        **kwargs
    )


def create_mentorship_event(master, disciple, **kwargs) -> MentorshipEvent:
    """创建师徒关系建立事件"""
    return MentorshipEvent(
        SocialEventType.MENTORSHIP_ESTABLISHED.value,
        master,
        disciple,
        **kwargs
    )


def create_friendship_event(character, friend, **kwargs) -> RelationshipEvent:
    """创建友谊建立事件"""
    return RelationshipEvent(
        SocialEventType.FRIENDSHIP_ESTABLISHED.value,
        character,
        friend,
        "friendship",
        **kwargs
    )


def create_reputation_event(character, change: int, reason: str, **kwargs) -> ReputationEvent:
    """创建声望变化事件"""
    event_type = SocialEventType.REPUTATION_INCREASE.value if change > 0 else SocialEventType.REPUTATION_DECREASE.value
    return ReputationEvent(
        event_type,
        character,
        change,
        reason,
        **kwargs
    )


# 事件发布便利函数

def publish_social_event(event: SocialInteractionEvent) -> bool:
    """
    发布社交事件到事件总线

    Args:
        event: 社交事件对象

    Returns:
        bool: 发布是否成功
    """
    try:
        from systems.event_system import publish_event
        return publish_event(event)
    except Exception as e:
        print(f"[ERROR] 社交事件发布失败: {e}")
        return False


def publish_sect_event(sect_name: str, character, event_type: str, **kwargs) -> bool:
    """发布门派事件"""
    event = SectEvent(event_type, sect_name, character, **kwargs)
    return publish_social_event(event)


def publish_mentorship_event(master, disciple, event_type: str, **kwargs) -> bool:
    """发布师徒事件"""
    event = MentorshipEvent(event_type, master, disciple, **kwargs)
    return publish_social_event(event)


def publish_relationship_event(character, target, event_type: str, relationship_type: str, **kwargs) -> bool:
    """发布关系事件"""
    event = RelationshipEvent(event_type, character, target, relationship_type, **kwargs)
    return publish_social_event(event)


def publish_reputation_event(character, change: int, reason: str, **kwargs) -> bool:
    """发布声望事件"""
    event = create_reputation_event(character, change, reason, **kwargs)
    return publish_social_event(event)