#!/usr/bin/env python3
"""
全面测试所有AI导演命令
"""

import os
import sys
import django
import time

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

# 添加项目路径到sys.path
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_path)

# 初始化Django
django.setup()

from evennia import AccountDB, ObjectDB
from commands.ai_director_commands import (
    CmdAIDirectorStatus, CmdAIDirectorHelp, CmdAIDirectorTest, 
    CmdAIDirectorDecision, CmdAIDirectorPerformance, CmdParseStoryOutline,
    CmdAIDirectorReload
)

def test_all_commands():
    print("=== 全面测试AI导演命令系统 ===")
    
    try:
        # 获取admin账户和角色
        admin_account = AccountDB.objects.get(username="admin")
        character = admin_account.db._last_puppet
        if not character:
            characters = ObjectDB.objects.filter(db_account=admin_account)
            if characters:
                character = characters[0]
        
        if not character:
            print("✗ 无法找到admin角色")
            return False
            
        print(f"使用角色: {character.key}")
        
        # 测试命令列表
        test_cases = [
            {
                "name": "ai帮助",
                "cmd_class": CmdAIDirectorHelp,
                "args": "",
                "description": "显示帮助信息"
            },
            {
                "name": "ai状态", 
                "cmd_class": CmdAIDirectorStatus,
                "args": "",
                "description": "查看AI系统状态"
            },
            {
                "name": "ai性能",
                "cmd_class": CmdAIDirectorPerformance,
                "args": "",
                "description": "查看性能统计"
            },
            {
                "name": "测试ai",
                "cmd_class": CmdAIDirectorTest,
                "args": "",
                "description": "运行AI功能测试"
            },
            {
                "name": "ai决策",
                "cmd_class": CmdAIDirectorDecision,
                "args": "玩家遇到了神秘的修仙洞府",
                "description": "AI决策测试"
            },
            {
                "name": "解析故事",
                "cmd_class": CmdParseStoryOutline,
                "args": "《逆天改命》主题：凡人修仙，境界提升，最终成为仙帝",
                "description": "故事大纲解析"
            },
            {
                "name": "ai重载",
                "cmd_class": CmdAIDirectorReload,
                "args": "",
                "description": "重新加载AI系统"
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i}/{total_count}] 测试命令: {test_case['name']}")
            print(f"描述: {test_case['description']}")
            if test_case['args']:
                print(f"参数: {test_case['args']}")
            
            try:
                # 创建命令实例
                cmd = test_case['cmd_class']()
                cmd.caller = character
                cmd.args = test_case['args']
                
                # 执行命令
                start_time = time.time()
                result = cmd.func()
                end_time = time.time()
                
                execution_time = (end_time - start_time) * 1000
                
                print(f"✓ 执行成功 (耗时: {execution_time:.1f}ms)")
                success_count += 1
                results.append({
                    "command": test_case['name'],
                    "status": "success",
                    "time": execution_time
                })
                
            except Exception as e:
                print(f"✗ 执行失败: {e}")
                results.append({
                    "command": test_case['name'],
                    "status": "failed",
                    "error": str(e)
                })
                
                # 打印详细错误信息
                import traceback
                print("详细错误:")
                traceback.print_exc()
        
        # 显示测试总结
        print(f"\n{'='*50}")
        print(f"测试总结:")
        print(f"总命令数: {total_count}")
        print(f"成功执行: {success_count}")
        print(f"失败执行: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 显示详细结果
        print(f"\n详细结果:")
        for result in results:
            status_icon = "✓" if result['status'] == 'success' else "✗"
            if result['status'] == 'success':
                print(f"{status_icon} {result['command']} - {result['time']:.1f}ms")
            else:
                print(f"{status_icon} {result['command']} - {result['error']}")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"测试框架失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_all_commands()
    print(f"\n{'='*50}")
    if success:
        print("🎉 所有AI导演命令测试通过！")
    else:
        print("❌ 部分命令测试失败，请检查上述错误信息。")
    exit(0 if success else 1)