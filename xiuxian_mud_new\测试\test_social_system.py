"""
仙侠社交系统测试套件

全面测试社交系统的各个组件：
- 社交事件系统
- 门派频道系统
- 师徒关系管理
- 社交关系管理
- 社交查询系统
- 社交命令系统
- 门派管理系统
- AI导演社交集成

使用Evennia测试框架进行单元测试和集成测试。
"""

import time
import unittest
from unittest.mock import Mock, patch, MagicMock
from evennia.utils.test_resources import EvenniaTest
from evennia.utils import create
from evennia import DefaultCharacter, DefaultChannel


class SocialSystemTestCase(EvenniaTest):
    """社交系统测试基类"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        
        # 创建测试角色
        self.char1 = create.create_object(
            "typeclasses.characters.XianxiaCharacter",
            key="测试角色1"
        )
        self.char2 = create.create_object(
            "typeclasses.characters.XianxiaCharacter", 
            key="测试角色2"
        )
        self.char3 = create.create_object(
            "typeclasses.characters.XianxiaCharacter",
            key="测试角色3"
        )
        
        # 设置角色属性
        self.char1.修为境界 = "筑基"
        self.char1.门派归属 = "无门派"
        
        self.char2.修为境界 = "金丹"
        self.char2.门派归属 = "无门派"
        
        self.char3.修为境界 = "练气"
        self.char3.门派归属 = "无门派"


class TestSocialEvents(SocialSystemTestCase):
    """测试社交事件系统"""
    
    def test_social_event_creation(self):
        """测试社交事件创建"""
        from systems.social_events import SocialInteractionEvent, SocialEventType
        
        # 创建门派事件
        event = SocialInteractionEvent(
            event_type=SocialEventType.SECT_MEMBER_JOINED.value,
            character=self.char1,
            target_character=None,
            sect_name="青云门",
            additional_data={"join_time": time.time()}
        )
        
        self.assertEqual(event.event_type, SocialEventType.SECT_MEMBER_JOINED.value)
        self.assertEqual(event.character, self.char1)
        self.assertEqual(event.sect_name, "青云门")
        self.assertIsNotNone(event.additional_data.get("join_time"))
    
    def test_event_publishing(self):
        """测试事件发布"""
        from systems.social_events import publish_sect_event, SocialEventType
        
        # 模拟事件总线
        with patch('systems.social_events.publish_event') as mock_publish:
            mock_publish.return_value = True
            
            result = publish_sect_event(
                self.char1,
                "青云门",
                SocialEventType.SECT_FOUNDED.value,
                founder_name=self.char1.key
            )
            
            self.assertTrue(result)
            mock_publish.assert_called_once()


class TestSectChannels(SocialSystemTestCase):
    """测试门派频道系统"""
    
    def test_sect_channel_creation(self):
        """测试门派频道创建"""
        from typeclasses.sect_channels import SectChannelManager
        
        # 创建门派频道
        channel = SectChannelManager.create_sect_channel(
            "青云门",
            self.char1,
            description="青云门门派频道"
        )
        
        self.assertIsNotNone(channel)
        self.assertEqual(channel.key, "青云门")
        self.assertTrue(channel.tags.get("sect_channel", category="channel_type"))
    
    def test_sect_member_management(self):
        """测试门派成员管理"""
        from typeclasses.sect_channels import SectChannelManager
        
        # 创建门派频道
        channel = SectChannelManager.create_sect_channel("青云门", self.char1)
        
        # 添加成员
        result = channel.add_sect_member(self.char2, "外门弟子")
        self.assertTrue(result)
        
        # 检查成员列表
        members = channel.get_sect_members()
        member_names = [m["name"] for m in members]
        self.assertIn(self.char1.key, member_names)
        self.assertIn(self.char2.key, member_names)
        
        # 移除成员
        result = channel.remove_sect_member(self.char2)
        self.assertTrue(result)
        
        # 再次检查成员列表
        members = channel.get_sect_members()
        member_names = [m["name"] for m in members]
        self.assertNotIn(self.char2.key, member_names)


class TestMentorshipManager(SocialSystemTestCase):
    """测试师徒关系管理"""
    
    def test_establish_mentorship(self):
        """测试建立师徒关系"""
        from systems.mentorship_manager import MentorshipManager
        
        # 建立师徒关系
        result = MentorshipManager.establish_mentorship(self.char2, self.char3)
        self.assertTrue(result)
        
        # 验证关系
        self.assertTrue(MentorshipManager.is_master_of(self.char2, self.char3))
        
        # 获取师父
        master = MentorshipManager.get_master(self.char3)
        self.assertEqual(master, self.char2)
        
        # 获取弟子列表
        disciples = MentorshipManager.get_disciples(self.char2)
        self.assertIn(self.char3, disciples)
    
    def test_terminate_mentorship(self):
        """测试终止师徒关系"""
        from systems.mentorship_manager import MentorshipManager
        
        # 先建立关系
        MentorshipManager.establish_mentorship(self.char2, self.char3)
        
        # 终止关系
        result = MentorshipManager.terminate_mentorship(self.char2, self.char3)
        self.assertTrue(result)
        
        # 验证关系已终止
        self.assertFalse(MentorshipManager.is_master_of(self.char2, self.char3))
        master = MentorshipManager.get_master(self.char3)
        self.assertIsNone(master)


class TestSocialRelationshipManager(SocialSystemTestCase):
    """测试社交关系管理"""
    
    def test_establish_friendship(self):
        """测试建立友谊关系"""
        from systems.social_relationship_manager import SocialRelationshipManager
        
        # 建立友谊
        result = SocialRelationshipManager.establish_relationship(
            self.char1, self.char2, "friend", "strong"
        )
        self.assertTrue(result)
        
        # 验证关系
        relationship = SocialRelationshipManager.get_relationship(self.char1, self.char2)
        self.assertIsNotNone(relationship)
        self.assertEqual(relationship["type"], "friend")
        self.assertEqual(relationship["strength"], "strong")
    
    def test_reputation_management(self):
        """测试声望管理"""
        from systems.social_relationship_manager import ReputationManager
        
        # 初始声望
        initial_rep = ReputationManager.get_reputation(self.char1)
        self.assertEqual(initial_rep, 0)
        
        # 增加声望
        ReputationManager.add_reputation(self.char1, 100, "测试增加")
        new_rep = ReputationManager.get_reputation(self.char1)
        self.assertEqual(new_rep, 100)
        
        # 获取声望等级
        level = ReputationManager.get_reputation_level(new_rep)
        self.assertEqual(level, "小有名气")


class TestSocialQueryManager(SocialSystemTestCase):
    """测试社交查询系统"""
    
    def test_sect_member_queries(self):
        """测试门派成员查询"""
        from systems.social_query_manager import SocialQueryManager
        from typeclasses.sect_channels import SectChannelManager
        
        # 创建门派并添加成员
        channel = SectChannelManager.create_sect_channel("青云门", self.char1)
        channel.add_sect_member(self.char2, "外门弟子")
        
        # 设置角色门派归属
        self.char1.门派归属 = "青云门"
        self.char2.门派归属 = "青云门"
        
        # 查询门派成员
        members = SocialQueryManager.get_sect_members("青云门")
        self.assertGreaterEqual(len(members), 2)
        
        member_names = [m.key for m in members]
        self.assertIn(self.char1.key, member_names)
        self.assertIn(self.char2.key, member_names)
    
    def test_relationship_queries(self):
        """测试关系查询"""
        from systems.social_query_manager import SocialQueryManager
        from systems.social_relationship_manager import SocialRelationshipManager
        
        # 建立关系
        SocialRelationshipManager.establish_relationship(
            self.char1, self.char2, "friend", "strong"
        )
        
        # 查询好友
        friends = SocialQueryManager.get_friends(self.char1)
        self.assertIn(self.char2, friends)


class TestSocialCommands(SocialSystemTestCase):
    """测试社交命令系统"""
    
    def test_sect_join_command(self):
        """测试入门命令"""
        from commands.social_commands import CmdJoinSect
        from typeclasses.sect_channels import SectChannelManager
        
        # 创建门派
        channel = SectChannelManager.create_sect_channel("青云门", self.char1)
        
        # 创建命令实例
        cmd = CmdJoinSect()
        cmd.caller = self.char2
        cmd.args = "青云门"
        
        # 模拟命令执行
        with patch.object(cmd, 'msg') as mock_msg:
            cmd.func()
            # 验证消息发送
            mock_msg.assert_called()
    
    def test_mentorship_command(self):
        """测试收徒命令"""
        from commands.social_commands import CmdTakeDisciple
        
        # 创建命令实例
        cmd = CmdTakeDisciple()
        cmd.caller = self.char2  # 师父
        cmd.args = self.char3.key  # 弟子
        
        # 模拟命令执行
        with patch.object(cmd, 'msg') as mock_msg:
            cmd.func()
            # 验证消息发送
            mock_msg.assert_called()


class TestSectManagement(SocialSystemTestCase):
    """测试门派管理系统"""
    
    def test_sect_creation(self):
        """测试门派创建"""
        from systems.sect_management import SectManager
        
        # 创建门派
        result = SectManager.create_sect(
            self.char1,
            "测试门派",
            "修炼门派",
            description="测试用门派"
        )
        self.assertTrue(result)
        
        # 验证门派信息
        sect_info = SectManager.get_sect_info("测试门派")
        self.assertIsNotNone(sect_info)
        self.assertEqual(sect_info["name"], "测试门派")
        self.assertEqual(sect_info["founder"]["name"], self.char1.key)
    
    def test_sect_resource_management(self):
        """测试门派资源管理"""
        from systems.sect_management import SectManager
        
        # 创建门派
        SectManager.create_sect(self.char1, "测试门派")
        
        # 添加资源
        result = SectManager.manage_sect_resources(
            "测试门派", self.char1, "spirit_stones", 500, "add"
        )
        self.assertTrue(result)
        
        # 验证资源变化
        sect_info = SectManager.get_sect_info("测试门派")
        self.assertGreaterEqual(sect_info["resources"]["spirit_stones"], 1500)


class TestAIDirectorIntegration(SocialSystemTestCase):
    """测试AI导演社交集成"""
    
    def test_diling_social_event_processing(self):
        """测试地灵导演社交事件处理"""
        from scripts.ai_directors.diling_director import DilingDirector
        
        # 创建地灵导演实例
        director = DilingDirector()
        
        # 模拟社交事件数据收集
        with patch.object(director, '_get_recent_social_events') as mock_events:
            mock_events.return_value = {
                "recent_events": [
                    {
                        "event_type": "sect_founded",
                        "timestamp": time.time(),
                        "data": {"sect_name": "测试门派"}
                    }
                ],
                "sect_events": {"测试门派": []},
                "social_trends": {"sect_activity": {"测试门派": 1}},
                "event_count": 1
            }
            
            # 收集上下文数据
            context = director.collect_context_data()
            
            # 验证社交事件被包含
            self.assertIn("social_events", context)
            self.assertEqual(context["social_events"]["event_count"], 1)
    
    def test_qiling_social_analysis(self):
        """测试器灵导演社交分析"""
        from scripts.ai_directors.qiling_director import QilingDirector
        
        # 创建器灵导演实例
        director = QilingDirector()
        
        # 模拟社交网络分析
        with patch.object(director, '_get_recent_individual_social_events') as mock_events:
            mock_events.return_value = {
                "recent_events": [],
                "player_events": {},
                "event_count": 0
            }
            
            # 执行社交网络分析
            social_analysis = director._analyze_social_networks()
            
            # 验证分析结果
            self.assertIn("active_relationships", social_analysis)
            self.assertIn("recent_social_events", social_analysis)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
