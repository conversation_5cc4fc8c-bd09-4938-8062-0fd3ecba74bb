"""
微集成测试套件

测试两个系统间的直接集成：
- 事件总线与AI导演系统集成测试
- TagProperty查询与世界系统集成测试
- AI导演与NPC系统集成测试
- 世界系统与战斗系统集成测试
- 社交系统与事件总线集成测试
"""

import time
import unittest
from unittest.mock import Mock, patch

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import (
    XianxiaIntegrationTest, create_test_character, create_test_room, 
    create_test_npc, cleanup_test_objects
)
from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..systems.tag_property_system import TagPropertyQueryManager
from ..systems.query_interfaces import AIDirectorQueryInterface
from ..typeclasses.characters import XianxiaCharacter
from ..typeclasses.rooms import XianxiaRoom
from ..typeclasses.npcs import XianxiaNPC


class EventBusAIDirectorIntegrationTest(XianxiaIntegrationTest):
    """事件总线与AI导演系统集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 初始化AI导演系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        # 等待系统初始化
        time.sleep(2)
    
    def test_event_bus_to_ai_director_communication(self):
        """测试事件总线到AI导演的通信"""
        log_info("开始测试事件总线到AI导演的通信")
        
        # 创建测试事件
        test_event = BaseEvent(
            event_type="cultivation_breakthrough",
            event_data={
                "character_id": "test_char_123",
                "old_realm": "练气一层",
                "new_realm": "练气二层",
                "location": "test_location"
            },
            priority="HIGH"
        )
        
        # 开始事件追踪
        trace_id = self.event_tracker.start_trace(test_event)
        
        # 发布事件到事件总线
        event_bus_scripts = search_script("xianxia_event_bus")
        self.assertTrue(event_bus_scripts, "事件总线脚本未找到")
        
        event_bus = event_bus_scripts[0]
        event_bus.publish_event(test_event)
        
        # 等待事件处理
        time.sleep(3)
        
        # 验证AI导演是否接收到事件
        tiandao_scripts = search_script("tiandao_director_script")
        if tiandao_scripts:
            tiandao = tiandao_scripts[0]
            # 检查AI导演是否处理了事件
            self.assertTrue(hasattr(tiandao, 'recent_events'), "AI导演缺少事件记录")
        
        # 完成事件追踪
        self.event_tracker.complete_trace(trace_id, True)
        
        # 验证事件链成功
        self.assert_event_chain_success(trace_id)
        
        log_info("事件总线到AI导演通信测试完成")
    
    def test_ai_director_event_generation(self):
        """测试AI导演生成事件"""
        log_info("开始测试AI导演事件生成")
        
        # 获取天道导演
        tiandao_scripts = search_script("tiandao_director_script")
        self.assertTrue(tiandao_scripts, "天道导演脚本未找到")
        
        tiandao = tiandao_scripts[0]
        
        # 触发AI导演决策
        if hasattr(tiandao, 'make_decision'):
            decision = tiandao.make_decision()
            
            # 验证决策结果
            if decision:
                self.assertIn('event_type', decision, "AI导演决策缺少事件类型")
                self.assertIn('event_data', decision, "AI导演决策缺少事件数据")
        
        log_info("AI导演事件生成测试完成")
    
    def test_director_coordination(self):
        """测试AI导演间协调"""
        log_info("开始测试AI导演间协调")
        
        # 获取所有导演
        tiandao_scripts = search_script("tiandao_director_script")
        diling_scripts = search_script("diling_director_script")
        qiling_scripts = search_script("qiling_director_script")
        
        self.assertTrue(tiandao_scripts, "天道导演未找到")
        self.assertTrue(diling_scripts, "地灵导演未找到")
        self.assertTrue(qiling_scripts, "器灵导演未找到")
        
        # 测试导演间通信
        tiandao = tiandao_scripts[0]
        diling = diling_scripts[0]
        
        # 模拟天道导演发送指令给地灵导演
        if hasattr(tiandao, 'send_directive') and hasattr(diling, 'receive_directive'):
            directive = {
                "type": "world_event",
                "data": {"event": "spiritual_energy_surge", "intensity": "high"}
            }
            
            tiandao.send_directive("diling", directive)
            time.sleep(1)
            
            # 验证地灵导演是否接收到指令
            received_directives = getattr(diling, 'received_directives', [])
            self.assertTrue(received_directives, "地灵导演未接收到天道指令")
        
        log_info("AI导演间协调测试完成")


class TagPropertyWorldSystemIntegrationTest(XianxiaIntegrationTest):
    """TagProperty查询与世界系统集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试房间
        self.test_room = create_test_room("integration_test_room")
        
        # 设置房间属性
        self.test_room.tags.add("location_type", category="world")
        self.test_room.tags.add("spiritual_energy", category="environment")
        self.test_room.tags.add("high", category="spiritual_level")
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_tagproperty_world_query_integration(self):
        """测试TagProperty与世界查询的集成"""
        log_info("开始测试TagProperty与世界查询集成")
        
        # 测试世界系统查询
        from ..systems.world_query_manager import world_query_manager
        
        # 查询高灵气地点
        high_energy_locations = world_query_manager.find_locations_by_spiritual_energy("high")
        
        # 验证查询结果包含测试房间
        room_ids = [loc.id for loc in high_energy_locations] if high_energy_locations else []
        self.assertIn(self.test_room.id, room_ids, "世界查询未找到测试房间")
        
        # 测试查询性能
        start_time = time.time()
        for _ in range(10):
            world_query_manager.find_locations_by_spiritual_energy("high")
        end_time = time.time()
        
        avg_query_time = (end_time - start_time) / 10 * 1000  # 毫秒
        self.assertLess(avg_query_time, 50, f"世界查询性能不达标: {avg_query_time}ms")
        
        log_info("TagProperty与世界查询集成测试完成")
    
    def test_world_system_tagproperty_consistency(self):
        """测试世界系统与TagProperty的数据一致性"""
        log_info("开始测试世界系统与TagProperty数据一致性")
        
        # 通过世界系统修改环境属性
        from ..systems.world_location_manager import world_location_manager
        
        world_location_manager.update_location_environment(
            self.test_room, 
            {"spiritual_energy": "extreme", "danger_level": "low"}
        )
        
        # 验证TagProperty查询能反映变化
        time.sleep(1)  # 等待更新
        
        extreme_energy_locations = world_query_manager.find_locations_by_spiritual_energy("extreme")
        room_ids = [loc.id for loc in extreme_energy_locations] if extreme_energy_locations else []
        self.assertIn(self.test_room.id, room_ids, "TagProperty未反映世界系统的更新")
        
        log_info("世界系统与TagProperty数据一致性测试完成")


class AIDirectorNPCIntegrationTest(XianxiaIntegrationTest):
    """AI导演与NPC系统集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试环境
        self.test_room = create_test_room("npc_test_room")
        self.test_npc = create_test_npc("test_integration_npc", self.test_room)
        self.test_char = create_test_character("test_integration_char", self.test_room)
        
        # 初始化AI导演系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_ai_director_npc_behavior_control(self):
        """测试AI导演控制NPC行为"""
        log_info("开始测试AI导演控制NPC行为")
        
        # 获取器灵导演（负责个体级事件）
        qiling_scripts = search_script("qiling_director_script")
        self.assertTrue(qiling_scripts, "器灵导演未找到")
        
        qiling = qiling_scripts[0]
        
        # 模拟AI导演决策影响NPC
        if hasattr(qiling, 'influence_npc_behavior'):
            behavior_change = {
                "npc_id": self.test_npc.id,
                "behavior_type": "friendly_greeting",
                "trigger_condition": "player_approach"
            }
            
            qiling.influence_npc_behavior(behavior_change)
            time.sleep(1)
            
            # 验证NPC行为是否改变
            if hasattr(self.test_npc, 'current_behavior'):
                self.assertEqual(self.test_npc.current_behavior, "friendly_greeting")
        
        log_info("AI导演控制NPC行为测试完成")
    
    def test_npc_ai_director_feedback(self):
        """测试NPC向AI导演反馈信息"""
        log_info("开始测试NPC向AI导演反馈")
        
        # 模拟NPC与玩家互动
        if hasattr(self.test_npc, 'interact_with_character'):
            interaction_result = self.test_npc.interact_with_character(
                self.test_char, 
                "greeting"
            )
            
            # 验证互动结果是否反馈给AI导演
            time.sleep(2)
            
            qiling_scripts = search_script("qiling_director_script")
            if qiling_scripts:
                qiling = qiling_scripts[0]
                if hasattr(qiling, 'recent_npc_interactions'):
                    interactions = qiling.recent_npc_interactions
                    self.assertTrue(interactions, "AI导演未接收到NPC互动反馈")
        
        log_info("NPC向AI导演反馈测试完成")


class WorldSystemCombatIntegrationTest(XianxiaIntegrationTest):
    """世界系统与战斗系统集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试环境
        self.test_room = create_test_room("combat_test_room")
        self.test_char1 = create_test_character("combat_char1", self.test_room)
        self.test_char2 = create_test_character("combat_char2", self.test_room)
        
        # 设置房间环境属性
        self.test_room.tags.add("fire_element", category="environment")
        self.test_room.tags.add("high", category="spiritual_level")
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_environment_combat_effects(self):
        """测试环境对战斗的影响"""
        log_info("开始测试环境对战斗的影响")
        
        # 初始化战斗系统
        from ..systems.xiuxian_combat_handler import XianxiaCombatHandler
        
        combat_handler = XianxiaCombatHandler(self.test_char1, self.test_char2)
        
        # 获取环境修正
        if hasattr(combat_handler, 'get_environment_modifiers'):
            modifiers = combat_handler.get_environment_modifiers(self.test_room)
            
            # 验证火元素环境对火系技能的加成
            self.assertIn('fire_element', modifiers, "环境修正未包含火元素")
            
            if 'fire_element' in modifiers:
                fire_bonus = modifiers['fire_element']
                self.assertGreater(fire_bonus, 0, "火元素环境应该提供正面加成")
        
        log_info("环境对战斗影响测试完成")
    
    def test_combat_world_event_generation(self):
        """测试战斗生成世界事件"""
        log_info("开始测试战斗生成世界事件")
        
        # 模拟战斗开始
        combat_event = BaseEvent(
            event_type="combat_started",
            event_data={
                "participants": [self.test_char1.id, self.test_char2.id],
                "location": self.test_room.id,
                "combat_type": "player_vs_player"
            },
            priority="MEDIUM"
        )
        
        # 发布战斗事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(combat_event)
            
            time.sleep(2)
            
            # 验证世界系统是否接收到战斗事件
            from ..systems.world_events import world_event_system
            if hasattr(world_event_system, 'recent_combat_events'):
                recent_events = world_event_system.recent_combat_events
                self.assertTrue(recent_events, "世界系统未接收到战斗事件")
        
        log_info("战斗生成世界事件测试完成")


class SocialSystemEventBusIntegrationTest(XianxiaIntegrationTest):
    """社交系统与事件总线集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试角色
        self.test_room = create_test_room("social_test_room")
        self.char1 = create_test_character("social_char1", self.test_room)
        self.char2 = create_test_character("social_char2", self.test_room)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_social_interaction_event_publishing(self):
        """测试社交互动事件发布"""
        log_info("开始测试社交互动事件发布")
        
        # 模拟社交互动
        from ..systems.social_relationship_manager import social_relationship_manager
        
        # 建立友谊关系
        social_relationship_manager.establish_relationship(
            self.char1, self.char2, "friendship", strength=50
        )
        
        time.sleep(1)
        
        # 验证事件是否发布到事件总线
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            if hasattr(event_bus, 'recent_events'):
                recent_events = event_bus.recent_events
                
                # 查找社交事件
                social_events = [e for e in recent_events if e.event_type == "relationship_established"]
                self.assertTrue(social_events, "社交互动未生成事件")
        
        log_info("社交互动事件发布测试完成")
    
    def test_event_driven_social_updates(self):
        """测试事件驱动的社交更新"""
        log_info("开始测试事件驱动的社交更新")
        
        # 创建影响社交关系的事件
        social_event = BaseEvent(
            event_type="character_reputation_change",
            event_data={
                "character_id": self.char1.id,
                "reputation_type": "sect_standing",
                "old_value": 50,
                "new_value": 80,
                "reason": "heroic_deed"
            },
            priority="MEDIUM"
        )
        
        # 发布事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(social_event)
            
            time.sleep(2)
            
            # 验证社交系统是否响应事件
            from ..systems.social_relationship_manager import social_relationship_manager
            if hasattr(social_relationship_manager, 'process_reputation_change'):
                # 检查声望变化是否影响了社交关系
                char1_reputation = social_relationship_manager.get_character_reputation(self.char1)
                self.assertGreaterEqual(char1_reputation.get("sect_standing", 0), 80)
        
        log_info("事件驱动的社交更新测试完成")


if __name__ == '__main__':
    # 运行微集成测试
    unittest.main(verbosity=2)
