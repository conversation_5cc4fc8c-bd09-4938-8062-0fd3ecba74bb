"""
仙侠战斗处理器

基于Evennia TurnBattle系统的仙侠战斗处理器。
集成五行相克、境界差异、技能系统等仙侠特色功能。
"""

from evennia.contrib.game_systems.turnbattle.tb_basic import EvAdventureTurnbasedCombatHandler
from evennia.utils import logger
from .wuxing_calculator import get_wuxing_calculator
from .xiuxian_skill_system import get_skill_system
from .event_system import (
    CombatStateEvent, SkillCastEvent, publish_event
)
from .combat_event_publisher import (
    get_combat_event_publisher, CombatStartEvent, CombatEndEvent, WuxingInteractionEvent
)
import time
import random


class XianxiaCombatHandler(EvAdventureTurnbasedCombatHandler):
    """
    仙侠战斗处理器
    
    扩展TurnBattle处理器，添加仙侠特色功能：
    - 五行相克自动计算
    - 境界差异影响
    - 仙侠技能处理
    - 战斗事件发布
    - 灵力管理
    """
    
    def __init__(self, *args, **kwargs):
        """初始化仙侠战斗处理器"""
        super().__init__(*args, **kwargs)
        
        # 初始化仙侠系统
        self.wuxing_calc = get_wuxing_calculator()
        self.skill_system = get_skill_system()
        
        # 战斗统计
        self.combat_stats = {
            "start_time": time.time(),
            "rounds": 0,
            "skills_used": 0,
            "wuxing_interactions": 0,
            "total_damage": 0
        }
        
        logger.log_info("XianxiaCombatHandler initialized")
    
    def at_script_creation(self):
        """脚本创建时的初始化"""
        super().at_script_creation()
        
        # 发布战斗开始事件
        self._publish_combat_start_event()
    
    def _publish_combat_start_event(self):
        """发布战斗开始事件"""
        combatants_data = []
        
        for combatant in self.db.combatants:
            combatant_data = {
                "id": str(combatant.id),
                "name": combatant.key,
                "realm": getattr(combatant, '修为境界', '练气'),
                "element": getattr(combatant, '五行属性', '土'),
                "hp": getattr(combatant, 'hp', 100),
                "max_hp": getattr(combatant, 'max_hp', 100)
            }
            combatants_data.append(combatant_data)
        
        event = CombatStartEvent(
            data={
                "combatants": combatants_data,
                "location": str(self.obj.location.id) if self.obj.location else None,
                "combat_type": "xiuxian_combat"
            }
        )
        
        publish_event(event)
    
    def process_xiuxian_action(self, combatant, action_dict: dict) -> dict:
        """
        处理仙侠战斗行动
        
        Args:
            combatant: 战斗者
            action_dict: 行动字典
            
        Returns:
            dict: 行动结果
        """
        action_type = action_dict.get("action", "attack")
        target = action_dict.get("target")
        
        result = {
            "success": False,
            "message": "",
            "effects": [],
            "events": []
        }
        
        if action_type == "xiuxian_skill":
            # 处理仙侠技能
            skill_name = action_dict.get("skill_name")
            result = self._process_xiuxian_skill(combatant, skill_name, target)
            
        elif action_type == "basic_attack":
            # 处理基础攻击（带五行相克）
            result = self._process_basic_attack(combatant, target)
            
        elif action_type == "defend":
            # 处理防御
            result = self._process_defend(combatant)
            
        else:
            # 使用父类处理其他行动
            return super().process_action(combatant, action_dict)
        
        # 更新战斗统计
        self._update_combat_stats(action_type, result)
        
        return result
    
    def _process_xiuxian_skill(self, caster, skill_name: str, target) -> dict:
        """
        处理仙侠技能使用
        
        Args:
            caster: 施法者
            skill_name: 技能名称
            target: 目标
            
        Returns:
            dict: 技能使用结果
        """
        # 检查技能是否可用
        if not hasattr(caster, 'use_xiuxian_skill'):
            return {"success": False, "message": "角色不支持仙侠技能"}
        
        # 使用技能
        skill_result = caster.use_xiuxian_skill(skill_name, target)
        
        if not skill_result.get("success", False):
            return skill_result
        
        # 处理技能效果
        effects = skill_result.get("effects", [])
        processed_effects = []
        
        for effect in effects:
            processed_effect = self._apply_skill_effect(caster, target, effect)
            processed_effects.append(processed_effect)
            
            # 发布五行相克事件
            if effect.get("wuxing_bonus", 1.0) != 1.0:
                self._publish_wuxing_event(caster, target, effect)
        
        return {
            "success": True,
            "message": f"{caster.key} 使用了 {skill_name}",
            "effects": processed_effects,
            "skill_name": skill_name
        }
    
    def _apply_skill_effect(self, caster, target, effect: dict) -> dict:
        """
        应用技能效果
        
        Args:
            caster: 施法者
            target: 目标
            effect: 效果数据
            
        Returns:
            dict: 处理后的效果
        """
        effect_type = effect.get("type")
        value = effect.get("value", 0)
        
        processed_effect = effect.copy()
        
        if effect_type == "damage" and target:
            # 应用伤害
            damage = self._apply_damage(target, value)
            processed_effect["actual_damage"] = damage
            processed_effect["target_hp"] = getattr(target, 'hp', 100)
            
        elif effect_type == "heal" and target:
            # 应用治疗
            heal = self._apply_heal(target, value)
            processed_effect["actual_heal"] = heal
            processed_effect["target_hp"] = getattr(target, 'hp', 100)
            
        elif effect_type == "defense":
            # 应用防御加成
            duration = effect.get("duration", 1)
            self._apply_defense_buff(caster, value, duration)
            processed_effect["applied"] = True
        
        return processed_effect
    
    def _apply_damage(self, target, damage: int) -> int:
        """
        应用伤害到目标
        
        Args:
            target: 目标对象
            damage: 伤害值
            
        Returns:
            int: 实际造成的伤害
        """
        if not hasattr(target, 'hp'):
            return 0
        
        # 计算防御减免
        defense = getattr(target, 'defense', 0)
        defense_buffs = getattr(target, 'db', {}).get('defense_buffs', [])
        
        total_defense = defense
        for buff in defense_buffs:
            if buff.get('expires', 0) > time.time():
                total_defense += buff.get('value', 0)
        
        # 计算实际伤害
        actual_damage = max(1, damage - total_defense // 2)  # 防御减少一半伤害
        
        # 应用伤害
        target.hp = max(0, target.hp - actual_damage)
        
        # 更新统计
        self.combat_stats["total_damage"] += actual_damage
        
        return actual_damage
    
    def _apply_heal(self, target, heal: int) -> int:
        """
        应用治疗到目标
        
        Args:
            target: 目标对象
            heal: 治疗值
            
        Returns:
            int: 实际治疗量
        """
        if not hasattr(target, 'hp') or not hasattr(target, 'max_hp'):
            return 0
        
        old_hp = target.hp
        target.hp = min(target.max_hp, target.hp + heal)
        actual_heal = target.hp - old_hp
        
        return actual_heal
    
    def _apply_defense_buff(self, target, defense_value: int, duration: int):
        """
        应用防御增益
        
        Args:
            target: 目标对象
            defense_value: 防御值
            duration: 持续时间（回合）
        """
        if not hasattr(target, 'db'):
            return
        
        if 'defense_buffs' not in target.db:
            target.db.defense_buffs = []
        
        # 添加防御增益
        buff = {
            'value': defense_value,
            'expires': time.time() + duration * 30,  # 假设每回合30秒
            'source': 'xiuxian_skill'
        }
        
        target.db.defense_buffs.append(buff)
    
    def _process_basic_attack(self, attacker, target) -> dict:
        """
        处理基础攻击（带五行相克）
        
        Args:
            attacker: 攻击者
            target: 目标
            
        Returns:
            dict: 攻击结果
        """
        if not target:
            return {"success": False, "message": "没有目标"}
        
        # 基础伤害
        base_damage = getattr(attacker, 'attack', 20)
        
        # 境界加成
        if hasattr(attacker, 'get_realm_combat_bonus'):
            realm_bonus = attacker.get_realm_combat_bonus()
            base_damage *= realm_bonus
        
        # 五行相克计算
        attacker_element = getattr(attacker, '五行属性', '土')
        target_element = getattr(target, '五行属性', '土')
        
        wuxing_bonus = self.wuxing_calc.calculate_elemental_bonus(attacker_element, target_element)
        final_damage = int(base_damage * wuxing_bonus)
        
        # 应用伤害
        actual_damage = self._apply_damage(target, final_damage)
        
        # 发布五行相克事件
        if wuxing_bonus != 1.0:
            self._publish_wuxing_event(attacker, target, {
                "element": attacker_element,
                "wuxing_bonus": wuxing_bonus
            })
        
        return {
            "success": True,
            "message": f"{attacker.key} 攻击了 {target.key}",
            "effects": [{
                "type": "damage",
                "value": final_damage,
                "actual_damage": actual_damage,
                "wuxing_bonus": wuxing_bonus,
                "target": target.key
            }]
        }
    
    def _process_defend(self, defender) -> dict:
        """
        处理防御行动
        
        Args:
            defender: 防御者
            
        Returns:
            dict: 防御结果
        """
        # 防御提供临时防御加成
        defense_bonus = 10
        duration = 1  # 持续1回合
        
        self._apply_defense_buff(defender, defense_bonus, duration)
        
        return {
            "success": True,
            "message": f"{defender.key} 进入防御姿态",
            "effects": [{
                "type": "defense",
                "value": defense_bonus,
                "duration": duration
            }]
        }
    
    def _publish_wuxing_event(self, attacker, target, effect: dict):
        """
        发布五行相克事件
        
        Args:
            attacker: 攻击者
            target: 目标
            effect: 效果数据
        """
        event = WuxingInteractionEvent(
            source_id=str(attacker.id),
            target_id=str(target.id),
            data={
                "attacker_element": getattr(attacker, '五行属性', '土'),
                "target_element": getattr(target, '五行属性', '土'),
                "bonus": effect.get("wuxing_bonus", 1.0),
                "skill_element": effect.get("element", "无"),
                "interaction_type": self._get_interaction_type(effect.get("wuxing_bonus", 1.0))
            }
        )
        
        publish_event(event)
        self.combat_stats["wuxing_interactions"] += 1
    
    def _get_interaction_type(self, bonus: float) -> str:
        """
        根据加成值确定相克类型
        
        Args:
            bonus: 加成倍数
            
        Returns:
            str: 相克类型
        """
        if bonus > 1.0:
            return "相克"
        elif bonus < 1.0:
            return "被克"
        else:
            return "平衡"
    
    def calculate_wuxing_interaction(self, attacker, defender, skill: dict = None) -> dict:
        """
        计算五行相克交互
        
        Args:
            attacker: 攻击者
            defender: 防御者
            skill: 技能数据（可选）
            
        Returns:
            dict: 相克计算结果
        """
        attacker_element = getattr(attacker, '五行属性', '土')
        defender_element = getattr(defender, '五行属性', '土')
        
        # 如果有技能，使用技能的五行属性
        if skill:
            skill_element = skill.get("element", attacker_element)
            bonus = self.wuxing_calc.calculate_elemental_bonus(skill_element, defender_element)
        else:
            bonus = self.wuxing_calc.calculate_elemental_bonus(attacker_element, defender_element)
        
        return {
            "attacker_element": attacker_element,
            "defender_element": defender_element,
            "bonus": bonus,
            "interaction_type": self._get_interaction_type(bonus)
        }
    
    def apply_realm_modifiers(self, damage: int, attacker_realm: str, defender_realm: str) -> int:
        """
        应用境界差异修正
        
        Args:
            damage: 基础伤害
            attacker_realm: 攻击者境界
            defender_realm: 防御者境界
            
        Returns:
            int: 修正后的伤害
        """
        realm_levels = {
            "练气": 0, "筑基": 1, "金丹": 2, "元婴": 3, "化神": 4,
            "炼虚": 5, "合体": 6, "大乘": 7, "渡劫": 8, "仙人": 9
        }
        
        attacker_level = realm_levels.get(attacker_realm, 0)
        defender_level = realm_levels.get(defender_realm, 0)
        
        level_diff = attacker_level - defender_level
        
        # 每级差异提供10%的伤害修正
        modifier = 1.0 + (level_diff * 0.1)
        
        # 限制修正范围在0.5-2.0之间
        modifier = max(0.5, min(2.0, modifier))
        
        return int(damage * modifier)
    
    def _update_combat_stats(self, action_type: str, result: dict):
        """
        更新战斗统计
        
        Args:
            action_type: 行动类型
            result: 行动结果
        """
        if action_type == "xiuxian_skill":
            self.combat_stats["skills_used"] += 1
        
        # 统计伤害
        for effect in result.get("effects", []):
            if effect.get("type") == "damage":
                damage = effect.get("actual_damage", 0)
                self.combat_stats["total_damage"] += damage
    
    def publish_combat_events(self, event_type: str, data: dict):
        """
        发布战斗事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type == "combat_end":
            event = CombatEndEvent(data=data)
        else:
            event = CombatStateEvent(data=data)
        
        publish_event(event)
    
    def at_script_shutdown(self):
        """脚本关闭时的清理"""
        # 发布战斗结束事件
        end_data = {
            "duration": time.time() - self.combat_stats["start_time"],
            "stats": self.combat_stats,
            "winner": self._determine_winner(),
            "combatants": [str(c.id) for c in self.db.combatants]
        }
        
        self.publish_combat_events("combat_end", end_data)
        
        super().at_script_shutdown()
        logger.log_info("XianxiaCombatHandler shutdown")
    
    def _determine_winner(self) -> str:
        """
        确定战斗胜利者
        
        Returns:
            str: 胜利者信息
        """
        alive_combatants = [c for c in self.db.combatants if getattr(c, 'hp', 0) > 0]
        
        if len(alive_combatants) == 1:
            return alive_combatants[0].key
        elif len(alive_combatants) == 0:
            return "平局"
        else:
            return "战斗中"
