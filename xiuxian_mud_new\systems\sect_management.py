"""
仙侠门派管理系统

基于Evennia Channel和Attributes实现的门派管理，包括：
- 门派创建和解散
- 门派资源管理
- 门派等级和声望系统
- 门派事件和活动管理

完全基于Evennia原生组件，与现有社交系统完美集成。
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from evennia.utils import logger
from evennia import search, create_object


class SectManager:
    """
    门派管理器
    
    提供门派的完整管理功能：
    - 门派创建、解散和基本信息管理
    - 门派资源和财富管理
    - 门派等级和声望系统
    - 门派成员管理和权限控制
    """
    
    @staticmethod
    def create_sect(founder, sect_name: str, sect_type: str = "修炼门派", **kwargs) -> bool:
        """
        创建新门派
        
        Args:
            founder: 创始人角色
            sect_name: 门派名称
            sect_type: 门派类型
            **kwargs: 额外的门派数据
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查门派名称是否已存在
            from typeclasses.sect_channels import SectChannelManager
            existing_channel = SectChannelManager.find_sect_channel(sect_name)
            if existing_channel:
                logger.log_warn(f"门派名称已存在: {sect_name}")
                return False
            
            # 检查创始人是否已有门派
            if getattr(founder, '门派归属', '无门派') != '无门派':
                logger.log_warn(f"{founder.key} 已有门派，不能创建新门派")
                return False
            
            # 创建门派频道
            sect_channel = SectChannelManager.create_sect_channel(
                sect_name, 
                founder,
                description=kwargs.get("description", f"{sect_name}的门派频道")
            )
            
            if not sect_channel:
                logger.log_warn(f"创建门派频道失败: {sect_name}")
                return False
            
            # 初始化门派数据
            SectManager._init_sect_data(sect_channel, founder, sect_type, **kwargs)
            
            # 设置创始人为掌门
            founder.门派归属 = sect_name
            founder.set_sect_rank("掌门")
            
            # 发布门派创建事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                founder,
                sect_name,
                SocialEventType.SECT_FOUNDED.value,
                founder_name=founder.key,
                sect_type=sect_type,
                creation_time=time.time(),
                **kwargs
            )
            
            logger.log_info(f"门派创建成功: {sect_name} (创始人: {founder.key})")
            return True
            
        except Exception as e:
            logger.log_err(f"创建门派失败: {e}")
            return False
    
    @staticmethod
    def _init_sect_data(sect_channel, founder, sect_type: str, **kwargs):
        """初始化门派数据"""
        sect_data = {
            "name": sect_channel.key,
            "type": sect_type,
            "founder": {
                "id": founder.id,
                "name": founder.key
            },
            "creation_time": time.time(),
            "level": 1,
            "reputation": 0,
            "resources": {
                "spirit_stones": kwargs.get("initial_spirit_stones", 1000),
                "cultivation_materials": kwargs.get("initial_materials", 100),
                "sect_points": 0
            },
            "territories": [],
            "alliances": [],
            "enemies": [],
            "special_buildings": [],
            "sect_techniques": [],
            "sect_rules": kwargs.get("rules", []),
            "description": kwargs.get("description", ""),
            "motto": kwargs.get("motto", ""),
            "recruitment_status": "open",  # open, closed, selective
            "max_members": SectManager._calculate_max_members(1),
            "statistics": {
                "total_members_ever": 1,
                "current_active_members": 1,
                "total_contributions": 0,
                "major_events": []
            }
        }
        
        sect_channel.attributes.add("sect_data", sect_data, category="management")
    
    @staticmethod
    def _calculate_max_members(sect_level: int) -> int:
        """根据门派等级计算最大成员数"""
        base_members = 50
        return base_members + (sect_level - 1) * 25
    
    @staticmethod
    def dissolve_sect(sect_name: str, dissolver, reason: str = "门派解散") -> bool:
        """
        解散门派
        
        Args:
            sect_name: 门派名称
            dissolver: 解散者（必须是掌门）
            reason: 解散原因
            
        Returns:
            bool: 解散是否成功
        """
        try:
            from typeclasses.sect_channels import SectChannelManager
            sect_channel = SectChannelManager.find_sect_channel(sect_name)
            
            if not sect_channel:
                logger.log_warn(f"找不到门派: {sect_name}")
                return False
            
            # 检查权限（只有掌门可以解散门派）
            if dissolver.get_sect_rank() != "掌门" or dissolver.门派归属 != sect_name:
                logger.log_warn(f"{dissolver.key} 没有权限解散门派 {sect_name}")
                return False
            
            # 获取所有成员
            members = sect_channel.get_sect_members()
            
            # 移除所有成员的门派归属
            for member_info in members:
                member_id = member_info.get("id")
                if member_id:
                    member_objs = search.search_object(f"#{member_id}")
                    if member_objs:
                        member = member_objs[0]
                        member.门派归属 = "无门派"
                        member.set_sect_rank("散修")
            
            # 发布门派解散事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                dissolver,
                sect_name,
                SocialEventType.SECT_DISSOLVED.value,
                dissolver_name=dissolver.key,
                reason=reason,
                dissolution_time=time.time(),
                member_count=len(members)
            )
            
            # 删除门派频道
            sect_channel.delete()
            
            logger.log_info(f"门派解散: {sect_name} (解散者: {dissolver.key}, 原因: {reason})")
            return True
            
        except Exception as e:
            logger.log_err(f"解散门派失败: {e}")
            return False
    
    @staticmethod
    def get_sect_info(sect_name: str) -> Optional[Dict[str, Any]]:
        """获取门派详细信息"""
        try:
            from typeclasses.sect_channels import SectChannelManager
            sect_channel = SectChannelManager.find_sect_channel(sect_name)
            
            if not sect_channel:
                return None
            
            sect_data = sect_channel.attributes.get("sect_data", {}, category="management")
            members = sect_channel.get_sect_members()
            
            # 计算实时统计
            active_members = len(members)
            total_contributions = sum(m.get("contributions", 0) for m in members)
            
            # 更新统计数据
            sect_data["statistics"]["current_active_members"] = active_members
            sect_data["statistics"]["total_contributions"] = total_contributions
            
            return {
                **sect_data,
                "current_members": members,
                "member_count": active_members
            }
            
        except Exception as e:
            logger.log_err(f"获取门派信息失败: {e}")
            return None
    
    @staticmethod
    def upgrade_sect(sect_name: str, upgrader) -> bool:
        """
        升级门派等级
        
        Args:
            sect_name: 门派名称
            upgrader: 升级者（必须是掌门或长老）
            
        Returns:
            bool: 升级是否成功
        """
        try:
            from typeclasses.sect_channels import SectChannelManager
            sect_channel = SectChannelManager.find_sect_channel(sect_name)
            
            if not sect_channel:
                logger.log_warn(f"找不到门派: {sect_name}")
                return False
            
            # 检查权限
            upgrader_rank = upgrader.get_sect_rank()
            if upgrader_rank not in ["掌门", "长老"] or upgrader.门派归属 != sect_name:
                logger.log_warn(f"{upgrader.key} 没有权限升级门派")
                return False
            
            sect_data = sect_channel.attributes.get("sect_data", {}, category="management")
            current_level = sect_data.get("level", 1)
            
            # 检查升级条件
            if not SectManager._check_upgrade_requirements(sect_data, current_level):
                logger.log_warn(f"门派 {sect_name} 不满足升级条件")
                return False
            
            # 执行升级
            new_level = current_level + 1
            sect_data["level"] = new_level
            sect_data["max_members"] = SectManager._calculate_max_members(new_level)
            
            # 消耗升级资源
            upgrade_cost = SectManager._calculate_upgrade_cost(current_level)
            for resource, cost in upgrade_cost.items():
                sect_data["resources"][resource] -= cost
            
            # 记录升级事件
            upgrade_event = {
                "timestamp": time.time(),
                "type": "sect_upgrade",
                "from_level": current_level,
                "to_level": new_level,
                "upgrader": upgrader.key
            }
            sect_data["statistics"]["major_events"].append(upgrade_event)
            
            sect_channel.attributes.add("sect_data", sect_data, category="management")
            
            # 发布门派升级事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                upgrader,
                sect_name,
                SocialEventType.SECT_UPGRADED.value,
                upgrader_name=upgrader.key,
                from_level=current_level,
                to_level=new_level,
                upgrade_time=time.time()
            )
            
            logger.log_info(f"门派升级: {sect_name} {current_level} -> {new_level}")
            return True
            
        except Exception as e:
            logger.log_err(f"门派升级失败: {e}")
            return False
    
    @staticmethod
    def _check_upgrade_requirements(sect_data: Dict[str, Any], current_level: int) -> bool:
        """检查门派升级条件"""
        requirements = {
            1: {"members": 10, "spirit_stones": 5000, "reputation": 100},
            2: {"members": 25, "spirit_stones": 15000, "reputation": 500},
            3: {"members": 50, "spirit_stones": 50000, "reputation": 1500},
            4: {"members": 100, "spirit_stones": 150000, "reputation": 5000},
            5: {"members": 200, "spirit_stones": 500000, "reputation": 15000}
        }
        
        if current_level >= 5:  # 最高等级
            return False
        
        req = requirements.get(current_level, {})
        stats = sect_data.get("statistics", {})
        resources = sect_data.get("resources", {})
        
        # 检查成员数量
        if stats.get("current_active_members", 0) < req.get("members", 0):
            return False
        
        # 检查灵石数量
        if resources.get("spirit_stones", 0) < req.get("spirit_stones", 0):
            return False
        
        # 检查门派声望
        if sect_data.get("reputation", 0) < req.get("reputation", 0):
            return False
        
        return True
    
    @staticmethod
    def _calculate_upgrade_cost(current_level: int) -> Dict[str, int]:
        """计算升级消耗"""
        costs = {
            1: {"spirit_stones": 5000, "cultivation_materials": 100},
            2: {"spirit_stones": 15000, "cultivation_materials": 300},
            3: {"spirit_stones": 50000, "cultivation_materials": 1000},
            4: {"spirit_stones": 150000, "cultivation_materials": 3000},
            5: {"spirit_stones": 500000, "cultivation_materials": 10000}
        }
        
        return costs.get(current_level, {"spirit_stones": 1000, "cultivation_materials": 50})
    
    @staticmethod
    def manage_sect_resources(sect_name: str, manager, resource_type: str, 
                            amount: int, operation: str = "add") -> bool:
        """
        管理门派资源
        
        Args:
            sect_name: 门派名称
            manager: 管理者
            resource_type: 资源类型
            amount: 数量
            operation: 操作类型 (add/remove)
            
        Returns:
            bool: 操作是否成功
        """
        try:
            from typeclasses.sect_channels import SectChannelManager
            sect_channel = SectChannelManager.find_sect_channel(sect_name)
            
            if not sect_channel:
                logger.log_warn(f"找不到门派: {sect_name}")
                return False
            
            # 检查权限
            manager_rank = manager.get_sect_rank()
            if manager_rank not in ["掌门", "长老", "执事"] or manager.门派归属 != sect_name:
                logger.log_warn(f"{manager.key} 没有权限管理门派资源")
                return False
            
            sect_data = sect_channel.attributes.get("sect_data", {}, category="management")
            resources = sect_data.get("resources", {})
            
            if resource_type not in resources:
                resources[resource_type] = 0
            
            if operation == "add":
                resources[resource_type] += amount
            elif operation == "remove":
                if resources[resource_type] < amount:
                    logger.log_warn(f"门派 {sect_name} 的 {resource_type} 不足")
                    return False
                resources[resource_type] -= amount
            else:
                logger.log_warn(f"无效的操作类型: {operation}")
                return False
            
            sect_data["resources"] = resources
            sect_channel.attributes.add("sect_data", sect_data, category="management")
            
            # 记录资源变化
            resource_event = {
                "timestamp": time.time(),
                "type": "resource_change",
                "resource_type": resource_type,
                "amount": amount,
                "operation": operation,
                "manager": manager.key,
                "new_total": resources[resource_type]
            }
            sect_data["statistics"]["major_events"].append(resource_event)
            
            logger.log_info(f"门派资源变化: {sect_name} {resource_type} {operation} {amount}")
            return True
            
        except Exception as e:
            logger.log_err(f"管理门派资源失败: {e}")
            return False
    
    @staticmethod
    def get_all_sects() -> List[Dict[str, Any]]:
        """获取所有门派列表"""
        try:
            from typeclasses.sect_channels import SectChannelManager
            all_channels = search.search_object_tag("sect_channel", category="channel_type")
            
            sects = []
            for channel in all_channels:
                sect_info = SectManager.get_sect_info(channel.key)
                if sect_info:
                    sects.append({
                        "name": channel.key,
                        "level": sect_info.get("level", 1),
                        "member_count": sect_info.get("member_count", 0),
                        "reputation": sect_info.get("reputation", 0),
                        "type": sect_info.get("type", "修炼门派"),
                        "recruitment_status": sect_info.get("recruitment_status", "open")
                    })
            
            # 按声望排序
            sects.sort(key=lambda x: x["reputation"], reverse=True)
            return sects
            
        except Exception as e:
            logger.log_err(f"获取门派列表失败: {e}")
            return []
    
    @staticmethod
    def find_sects_by_level(min_level: int = 1, max_level: int = 5) -> List[Dict[str, Any]]:
        """根据等级范围查找门派"""
        all_sects = SectManager.get_all_sects()
        return [sect for sect in all_sects 
                if min_level <= sect.get("level", 1) <= max_level]
    
    @staticmethod
    def find_recruiting_sects() -> List[Dict[str, Any]]:
        """查找正在招募的门派"""
        all_sects = SectManager.get_all_sects()
        return [sect for sect in all_sects 
                if sect.get("recruitment_status") == "open"]


# 便利函数

def create_new_sect(founder, sect_name: str, **kwargs) -> bool:
    """创建新门派的便利函数"""
    return SectManager.create_sect(founder, sect_name, **kwargs)


def get_sect_by_name(sect_name: str) -> Optional[Dict[str, Any]]:
    """根据名称获取门派信息"""
    return SectManager.get_sect_info(sect_name)


def get_top_sects(limit: int = 10) -> List[Dict[str, Any]]:
    """获取顶级门派排行榜"""
    all_sects = SectManager.get_all_sects()
    return all_sects[:limit]
