"""
三层AI导演系统初始化脚本

负责创建和注册三层AI导演系统的所有组件：
- 创建导演集成系统
- 初始化三个导演层级
- 注册导演到集成系统
- 启动系统
"""

from evennia import DefaultScript
from evennia.utils import logger
from evennia.utils.search import search_script

from scripts.ai_directors.director_integration import Director<PERSON><PERSON>er, get_director_integration
from scripts.ai_directors.tiandao_director import TiandaoDirector
from scripts.ai_directors.diling_director import DilingDirector
from scripts.ai_directors.qiling_director import QilingDirector


class DirectorInitializationScript(DefaultScript):
    """
    导演系统初始化脚本
    
    这个脚本负责：
    1. 创建导演集成系统
    2. 创建三个导演层级的脚本
    3. 注册导演到集成系统
    4. 启动整个系统
    """
    
    def at_script_creation(self):
        """脚本创建时的初始化"""
        self.key = "director_initialization_script"
        self.desc = "三层AI导演系统初始化脚本"
        self.interval = 0  # 只运行一次
        self.persistent = True
        
        # 初始化状态
        self.db.initialization_status = {
            "integration_created": False,
            "tiandao_created": False,
            "diling_created": False,
            "qiling_created": False,
            "directors_registered": False,
            "system_started": False,
            "initialization_complete": False
        }
        
        logger.log_info("导演系统初始化脚本已创建")
    
    def at_start(self):
        """脚本启动时执行初始化"""
        logger.log_info("开始初始化三层AI导演系统...")
        
        try:
            # 步骤1: 创建导演集成系统
            self._create_integration_system()
            
            # 步骤2: 创建三个导演层级
            self._create_directors()
            
            # 步骤3: 注册导演到集成系统
            self._register_directors()
            
            # 步骤4: 启动系统
            self._start_system()
            
            # 标记初始化完成
            self.db.initialization_status["initialization_complete"] = True
            logger.log_info("三层AI导演系统初始化完成")
            
            # 初始化完成后停止脚本
            self.stop()
            
        except Exception as e:
            logger.log_err(f"导演系统初始化失败: {e}")
            self.db.initialization_error = str(e)
    
    def _create_integration_system(self):
        """创建导演集成系统"""
        logger.log_info("创建导演集成系统...")
        
        # 检查是否已存在集成系统
        existing_integration = search_script("director_integration_script")
        if existing_integration:
            logger.log_info("导演集成系统已存在")
            self.db.initialization_status["integration_created"] = True
            return
        
        # 创建新的集成系统
        from scripts.ai_directors.director_integration import DirectorIntegrationScript
        
        integration_script = DirectorIntegrationScript.create(
            key="director_integration_script",
            desc="三层AI导演集成系统"
        )
        
        if integration_script:
            logger.log_info("导演集成系统创建成功")
            self.db.initialization_status["integration_created"] = True
        else:
            raise Exception("导演集成系统创建失败")
    
    def _create_directors(self):
        """创建三个导演层级"""
        logger.log_info("创建三个导演层级...")
        
        # 创建天道导演
        self._create_tiandao_director()
        
        # 创建地灵导演
        self._create_diling_director()
        
        # 创建器灵导演
        self._create_qiling_director()
    
    def _create_tiandao_director(self):
        """创建天道导演"""
        logger.log_info("创建天道导演...")
        
        # 检查是否已存在
        existing_tiandao = search_script("tiandao_director_script")
        if existing_tiandao:
            logger.log_info("天道导演已存在")
            self.db.initialization_status["tiandao_created"] = True
            return
        
        # 创建新的天道导演
        tiandao_script = TiandaoDirector.create(
            key="tiandao_director_script",
            desc="天道导演 - 世界级AI导演，5分钟决策周期"
        )
        
        if tiandao_script:
            logger.log_info("天道导演创建成功")
            self.db.initialization_status["tiandao_created"] = True
        else:
            raise Exception("天道导演创建失败")
    
    def _create_diling_director(self):
        """创建地灵导演"""
        logger.log_info("创建地灵导演...")
        
        # 检查是否已存在
        existing_diling = search_script("diling_director_script")
        if existing_diling:
            logger.log_info("地灵导演已存在")
            self.db.initialization_status["diling_created"] = True
            return
        
        # 创建新的地灵导演
        diling_script = DilingDirector.create(
            key="diling_director_script",
            desc="地灵导演 - 区域级AI导演，1分钟决策周期"
        )
        
        if diling_script:
            logger.log_info("地灵导演创建成功")
            self.db.initialization_status["diling_created"] = True
        else:
            raise Exception("地灵导演创建失败")
    
    def _create_qiling_director(self):
        """创建器灵导演"""
        logger.log_info("创建器灵导演...")
        
        # 检查是否已存在
        existing_qiling = search_script("qiling_director_script")
        if existing_qiling:
            logger.log_info("器灵导演已存在")
            self.db.initialization_status["qiling_created"] = True
            return
        
        # 创建新的器灵导演
        qiling_script = QilingDirector.create(
            key="qiling_director_script",
            desc="器灵导演 - 个体级AI导演，10秒决策周期"
        )
        
        if qiling_script:
            logger.log_info("器灵导演创建成功")
            self.db.initialization_status["qiling_created"] = True
        else:
            raise Exception("器灵导演创建失败")
    
    def _register_directors(self):
        """注册导演到集成系统"""
        logger.log_info("注册导演到集成系统...")
        
        # 获取集成系统
        integration = get_director_integration()
        if not integration:
            raise Exception("导演集成系统未找到")
        
        # 获取三个导演脚本
        tiandao_script = search_script("tiandao_director_script")
        diling_script = search_script("diling_director_script")
        qiling_script = search_script("qiling_director_script")
        
        if not tiandao_script:
            raise Exception("天道导演脚本未找到")
        if not diling_script:
            raise Exception("地灵导演脚本未找到")
        if not qiling_script:
            raise Exception("器灵导演脚本未找到")
        
        # 注册导演
        integration.register_director(DirectorLayer.TIANDAO, tiandao_script[0])
        integration.register_director(DirectorLayer.DILING, diling_script[0])
        integration.register_director(DirectorLayer.QILING, qiling_script[0])
        
        logger.log_info("导演注册完成")
        self.db.initialization_status["directors_registered"] = True
    
    def _start_system(self):
        """启动系统"""
        logger.log_info("启动三层AI导演系统...")
        
        # 获取集成系统
        integration = get_director_integration()
        if not integration:
            raise Exception("导演集成系统未找到")
        
        # 启动集成系统
        if not integration.is_active:
            integration.start()
        
        # 启动所有导演
        for layer, director in integration.db.registered_directors.items():
            if director and not director.is_active:
                director.start()
                logger.log_info(f"{layer.value.upper()}导演已启动")
        
        logger.log_info("三层AI导演系统启动完成")
        self.db.initialization_status["system_started"] = True
    
    def get_initialization_status(self):
        """获取初始化状态"""
        return self.db.initialization_status
    
    def get_initialization_error(self):
        """获取初始化错误"""
        return getattr(self.db, 'initialization_error', None)


def initialize_director_system():
    """
    初始化三层AI导演系统的便捷函数
    
    返回:
        bool: 初始化是否成功
    """
    try:
        # 检查是否已有初始化脚本在运行
        existing_init = search_script("director_initialization_script")
        if existing_init:
            init_script = existing_init[0]
            if init_script.is_active:
                logger.log_info("导演系统初始化脚本已在运行")
                return False
            
            # 检查之前的初始化状态
            status = init_script.get_initialization_status()
            if status.get("initialization_complete", False):
                logger.log_info("导演系统已完成初始化")
                return True
        
        # 创建新的初始化脚本
        init_script = DirectorInitializationScript.create(
            key="director_initialization_script",
            desc="三层AI导演系统初始化脚本"
        )
        
        if init_script:
            logger.log_info("导演系统初始化脚本已创建并启动")
            return True
        else:
            logger.log_err("导演系统初始化脚本创建失败")
            return False
            
    except Exception as e:
        logger.log_err(f"导演系统初始化失败: {e}")
        return False


def get_director_system_status():
    """
    获取导演系统状态
    
    返回:
        dict: 系统状态信息
    """
    status = {
        "integration_available": False,
        "directors_registered": {},
        "directors_active": {},
        "initialization_complete": False,
        "initialization_error": None
    }
    
    try:
        # 检查集成系统
        integration = get_director_integration()
        if integration:
            status["integration_available"] = True
            
            # 检查导演注册状态
            for layer in DirectorLayer:
                director = integration.db.registered_directors.get(layer)
                status["directors_registered"][layer.value] = director is not None
                status["directors_active"][layer.value] = director.is_active if director else False
        
        # 检查初始化状态
        init_scripts = search_script("director_initialization_script")
        if init_scripts:
            init_script = init_scripts[0]
            init_status = init_script.get_initialization_status()
            status["initialization_complete"] = init_status.get("initialization_complete", False)
            status["initialization_error"] = init_script.get_initialization_error()
    
    except Exception as e:
        status["initialization_error"] = str(e)
    
    return status
