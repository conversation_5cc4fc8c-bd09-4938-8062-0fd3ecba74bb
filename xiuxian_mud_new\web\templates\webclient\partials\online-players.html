{% comment %}
在线玩家组件模板

功能：
- 实时在线玩家列表显示
- 玩家等级、门派、状态信息
- 快速交互功能（私聊、查看、邀请）
- 玩家搜索和过滤功能
- 好友系统集成
- 排行榜显示

技术集成：
- 与Evennia Session系统完美集成
- WebSocket实时玩家状态更新
- 本地存储好友列表和偏好设置
- 移动端友好的触控操作
- 完整的玩家交互功能
{% endcomment %}

<div class="online-players" id="online-players">
    <!-- 头部信息 -->
    <div class="online-players__header">
        <div class="online-players__title-section">
            <span class="online-players__icon">👥</span>
            <span class="online-players__title">在线玩家</span>
            <span class="online-players__count" id="total-players-count">42人在线</span>
        </div>
        <div class="online-players__header-controls">
            <button type="button" class="player-control-btn" id="player-search-toggle" title="搜索玩家">
                <span class="btn-icon">🔍</span>
            </button>
            <button type="button" class="player-control-btn" id="player-filter-toggle" title="过滤选项">
                <span class="btn-icon">🔽</span>
            </button>
            <button type="button" class="player-control-btn" id="player-refresh" title="刷新列表">
                <span class="btn-icon">🔄</span>
            </button>
            <button type="button" class="player-control-btn" id="player-settings" title="显示设置">
                <span class="btn-icon">⚙️</span>
            </button>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="online-players__search hidden" id="player-search">
        <div class="search-input-group">
            <input type="text" class="search-input" id="player-search-input" placeholder="搜索玩家名称...">
            <button type="button" class="search-btn" id="player-search-btn">
                <span class="btn-icon">🔍</span>
            </button>
            <button type="button" class="search-clear-btn" id="player-search-clear">
                <span class="btn-icon">✕</span>
            </button>
        </div>
    </div>

    <!-- 过滤选项 -->
    <div class="online-players__filters hidden" id="player-filters">
        <div class="filter-group">
            <label class="filter-label">门派筛选：</label>
            <select id="sect-filter" class="filter-select">
                <option value="all">全部门派</option>
                <option value="wudang">武当派</option>
                <option value="shaolin">少林寺</option>
                <option value="emei">峨眉派</option>
                <option value="huashan">华山派</option>
                <option value="kunlun">昆仑派</option>
                <option value="none">散修</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">等级筛选：</label>
            <select id="level-filter" class="filter-select">
                <option value="all">全部等级</option>
                <option value="qi">练气期</option>
                <option value="foundation">筑基期</option>
                <option value="golden">金丹期</option>
                <option value="nascent">元婴期</option>
                <option value="spirit">化神期</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">状态筛选：</label>
            <select id="status-filter" class="filter-select">
                <option value="all">全部状态</option>
                <option value="online">在线</option>
                <option value="busy">忙碌</option>
                <option value="away">离开</option>
                <option value="cultivation">修炼中</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label class="filter-checkbox">
                <input type="checkbox" id="friends-only">
                <span class="checkbox-text">仅显示好友</span>
            </label>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="online-players__stats">
        <div class="stat-item">
            <span class="stat-label">总计:</span>
            <span class="stat-value" id="stat-total">42</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">好友:</span>
            <span class="stat-value" id="stat-friends">8</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">同门:</span>
            <span class="stat-value" id="stat-sect">12</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">新手:</span>
            <span class="stat-value" id="stat-newbies">6</span>
        </div>
    </div>

    <!-- 玩家列表 -->
    <div class="online-players__content" id="players-content">
        <div class="players-list" id="players-list">
            <!-- 示例玩家项 -->
            <div class="player-item" data-player="张三丰" data-sect="wudang" data-level="nascent" data-status="online">
                <div class="player-avatar">
                    <img src="/static/images/avatars/default.png" alt="张三丰" class="avatar-img">
                    <span class="player-status online" title="在线"></span>
                </div>
                <div class="player-info">
                    <div class="player-name-section">
                        <span class="player-name">张三丰</span>
                        <span class="player-title">[真人]</span>
                        <span class="friend-indicator" title="好友">⭐</span>
                    </div>
                    <div class="player-details">
                        <span class="player-sect">武当派</span>
                        <span class="player-level">元婴期</span>
                        <span class="player-location">武当山</span>
                    </div>
                    <div class="player-stats">
                        <span class="stat-item">等级: 89</span>
                        <span class="stat-item">战力: 15420</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button type="button" class="player-action-btn primary" data-action="chat" title="私聊">
                        <span class="btn-icon">💬</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="view" title="查看">
                        <span class="btn-icon">👁️</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="invite" title="邀请">
                        <span class="btn-icon">🤝</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="more" title="更多">
                        <span class="btn-icon">⋯</span>
                    </button>
                </div>
            </div>

            <div class="player-item" data-player="小龙女" data-sect="none" data-level="golden" data-status="cultivation">
                <div class="player-avatar">
                    <img src="/static/images/avatars/default.png" alt="小龙女" class="avatar-img">
                    <span class="player-status cultivation" title="修炼中"></span>
                </div>
                <div class="player-info">
                    <div class="player-name-section">
                        <span class="player-name">小龙女</span>
                        <span class="player-title">[仙子]</span>
                    </div>
                    <div class="player-details">
                        <span class="player-sect">散修</span>
                        <span class="player-level">金丹期</span>
                        <span class="player-location">古墓</span>
                    </div>
                    <div class="player-stats">
                        <span class="stat-item">等级: 67</span>
                        <span class="stat-item">战力: 8930</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button type="button" class="player-action-btn primary" data-action="chat" title="私聊">
                        <span class="btn-icon">💬</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="view" title="查看">
                        <span class="btn-icon">👁️</span>
                    </button>
                    <button type="button" class="player-action-btn disabled" data-action="invite" title="修炼中无法邀请">
                        <span class="btn-icon">🤝</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="more" title="更多">
                        <span class="btn-icon">⋯</span>
                    </button>
                </div>
            </div>

            <div class="player-item" data-player="令狐冲" data-sect="huashan" data-level="foundation" data-status="busy">
                <div class="player-avatar">
                    <img src="/static/images/avatars/default.png" alt="令狐冲" class="avatar-img">
                    <span class="player-status busy" title="忙碌"></span>
                </div>
                <div class="player-info">
                    <div class="player-name-section">
                        <span class="player-name">令狐冲</span>
                        <span class="player-title">[大师兄]</span>
                        <span class="sect-mate-indicator" title="同门">🏛️</span>
                    </div>
                    <div class="player-details">
                        <span class="player-sect">华山派</span>
                        <span class="player-level">筑基期</span>
                        <span class="player-location">华山</span>
                    </div>
                    <div class="player-stats">
                        <span class="stat-item">等级: 45</span>
                        <span class="stat-item">战力: 3240</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button type="button" class="player-action-btn primary" data-action="chat" title="私聊">
                        <span class="btn-icon">💬</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="view" title="查看">
                        <span class="btn-icon">👁️</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="invite" title="邀请">
                        <span class="btn-icon">🤝</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="more" title="更多">
                        <span class="btn-icon">⋯</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="no-players hidden" id="no-players">
            <span class="no-players-icon">👥</span>
            <span class="no-players-text">没有找到符合条件的玩家</span>
            <button type="button" class="clear-filters-btn" id="clear-filters">清除筛选</button>
        </div>
    </div>

    <!-- 快速操作栏 -->
    <div class="online-players__quick-actions">
        <button type="button" class="quick-action-btn" id="find-friends" title="寻找好友">
            <span class="btn-icon">👫</span>
            <span class="btn-text">寻找好友</span>
        </button>
        <button type="button" class="quick-action-btn" id="sect-members" title="同门师兄弟">
            <span class="btn-icon">🏛️</span>
            <span class="btn-text">同门</span>
        </button>
        <button type="button" class="quick-action-btn" id="nearby-players" title="附近玩家">
            <span class="btn-icon">📍</span>
            <span class="btn-text">附近</span>
        </button>
        <button type="button" class="quick-action-btn" id="ranking-list" title="排行榜">
            <span class="btn-icon">🏆</span>
            <span class="btn-text">排行榜</span>
        </button>
    </div>
</div>

<!-- 玩家详情对话框 -->
<div class="player-details-dialog hidden" id="player-details-dialog">
    <div class="player-details-dialog__backdrop"></div>
    <div class="player-details-dialog__content">
        <div class="player-details-dialog__header">
            <h4 id="player-details-title">玩家详情</h4>
            <button type="button" class="player-details-dialog__close">×</button>
        </div>
        <div class="player-details-dialog__body" id="player-details-body">
            <!-- 玩家详情内容将由JavaScript动态生成 -->
        </div>
        <div class="player-details-dialog__footer">
            <button type="button" class="btn btn-secondary" id="player-details-close">关闭</button>
            <button type="button" class="btn" id="add-friend-btn">添加好友</button>
        </div>
    </div>
</div>

<!-- 玩家操作菜单 -->
<div class="player-context-menu hidden" id="player-context-menu">
    <div class="context-menu-item" data-action="chat">
        <span class="menu-icon">💬</span>
        <span class="menu-text">发起私聊</span>
    </div>
    <div class="context-menu-item" data-action="view">
        <span class="menu-icon">👁️</span>
        <span class="menu-text">查看详情</span>
    </div>
    <div class="context-menu-item" data-action="invite">
        <span class="menu-icon">🤝</span>
        <span class="menu-text">邀请组队</span>
    </div>
    <div class="context-menu-item" data-action="friend">
        <span class="menu-icon">⭐</span>
        <span class="menu-text">添加好友</span>
    </div>
    <div class="context-menu-item" data-action="ignore">
        <span class="menu-icon">🚫</span>
        <span class="menu-text">屏蔽玩家</span>
    </div>
    <div class="context-menu-item" data-action="report">
        <span class="menu-icon">⚠️</span>
        <span class="menu-text">举报玩家</span>
    </div>
</div>

<!-- 排行榜对话框 -->
<div class="ranking-dialog hidden" id="ranking-dialog">
    <div class="ranking-dialog__backdrop"></div>
    <div class="ranking-dialog__content">
        <div class="ranking-dialog__header">
            <h4>排行榜</h4>
            <button type="button" class="ranking-dialog__close">×</button>
        </div>
        <div class="ranking-dialog__tabs">
            <button type="button" class="ranking-tab active" data-ranking="level">等级榜</button>
            <button type="button" class="ranking-tab" data-ranking="power">战力榜</button>
            <button type="button" class="ranking-tab" data-ranking="wealth">财富榜</button>
            <button type="button" class="ranking-tab" data-ranking="sect">门派榜</button>
        </div>
        <div class="ranking-dialog__body" id="ranking-content">
            <!-- 排行榜内容将由JavaScript动态生成 -->
        </div>
    </div>
</div>

<script>
// 在线玩家管理器
window.OnlinePlayersManager = {
    // 配置
    config: {
        refreshInterval: 30000, // 30秒刷新一次
        maxPlayers: 200,
        searchDelay: 300
    },

    // 状态
    state: {
        players: [],
        filteredPlayers: [],
        friends: new Set(),
        ignoredPlayers: new Set(),
        currentFilters: {
            search: '',
            sect: 'all',
            level: 'all',
            status: 'all',
            friendsOnly: false
        },
        selectedPlayer: null,
        contextMenuTarget: null
    },

    // 初始化
    init: function() {
        this.bindEvents();
        this.loadSettings();
        this.connectWebSocket();
        this.startRefreshTimer();
        this.requestPlayerList();
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 头部控制按钮
        document.getElementById('player-search-toggle').addEventListener('click', function() {
            self.toggleSearch();
        });

        document.getElementById('player-filter-toggle').addEventListener('click', function() {
            self.toggleFilters();
        });

        document.getElementById('player-refresh').addEventListener('click', function() {
            self.refreshPlayerList();
        });

        document.getElementById('player-settings').addEventListener('click', function() {
            self.showSettings();
        });

        // 搜索功能
        document.getElementById('player-search-btn').addEventListener('click', function() {
            self.performSearch();
        });

        document.getElementById('player-search-clear').addEventListener('click', function() {
            self.clearSearch();
        });

        let searchTimeout;
        document.getElementById('player-search-input').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                self.performSearch();
            }, self.config.searchDelay);
        });

        // 过滤器
        document.getElementById('sect-filter').addEventListener('change', function() {
            self.updateFilter('sect', this.value);
        });

        document.getElementById('level-filter').addEventListener('change', function() {
            self.updateFilter('level', this.value);
        });

        document.getElementById('status-filter').addEventListener('change', function() {
            self.updateFilter('status', this.value);
        });

        document.getElementById('friends-only').addEventListener('change', function() {
            self.updateFilter('friendsOnly', this.checked);
        });

        // 清除筛选
        document.getElementById('clear-filters').addEventListener('click', function() {
            self.clearAllFilters();
        });

        // 玩家操作
        document.addEventListener('click', function(e) {
            if (e.target.closest('.player-action-btn')) {
                const btn = e.target.closest('.player-action-btn');
                const action = btn.dataset.action;
                const playerItem = btn.closest('.player-item');
                const playerName = playerItem.dataset.player;

                self.handlePlayerAction(action, playerName, playerItem);
            }
        });

        // 玩家项右键菜单
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.player-item')) {
                e.preventDefault();
                const playerItem = e.target.closest('.player-item');
                const playerName = playerItem.dataset.player;
                self.showContextMenu(e.clientX, e.clientY, playerName);
            }
        });

        // 上下文菜单
        document.addEventListener('click', function(e) {
            if (e.target.closest('.context-menu-item')) {
                const action = e.target.closest('.context-menu-item').dataset.action;
                self.handlePlayerAction(action, self.state.contextMenuTarget);
                self.hideContextMenu();
            } else if (!e.target.closest('.player-context-menu')) {
                self.hideContextMenu();
            }
        });

        // 快速操作
        document.getElementById('find-friends').addEventListener('click', function() {
            self.showFriendsOnly();
        });

        document.getElementById('sect-members').addEventListener('click', function() {
            self.showSectMembers();
        });

        document.getElementById('nearby-players').addEventListener('click', function() {
            self.showNearbyPlayers();
        });

        document.getElementById('ranking-list').addEventListener('click', function() {
            self.showRankingList();
        });

        // 对话框关闭
        document.querySelectorAll('.player-details-dialog__close, #player-details-close').forEach(function(btn) {
            btn.addEventListener('click', function() {
                self.hidePlayerDetails();
            });
        });

        document.querySelectorAll('.ranking-dialog__close').forEach(function(btn) {
            btn.addEventListener('click', function() {
                self.hideRankingDialog();
            });
        });

        // 添加好友
        document.getElementById('add-friend-btn').addEventListener('click', function() {
            self.addFriend(self.state.selectedPlayer);
        });

        // 排行榜标签
        document.querySelectorAll('.ranking-tab').forEach(function(tab) {
            tab.addEventListener('click', function() {
                const ranking = this.dataset.ranking;
                self.switchRankingTab(ranking);
            });
        });
    },

    // 切换搜索
    toggleSearch: function() {
        const searchElement = document.getElementById('player-search');
        const isVisible = !searchElement.classList.contains('hidden');

        if (isVisible) {
            searchElement.classList.add('hidden');
        } else {
            searchElement.classList.remove('hidden');
            document.getElementById('player-search-input').focus();
        }
    },

    // 切换过滤器
    toggleFilters: function() {
        const filtersElement = document.getElementById('player-filters');
        filtersElement.classList.toggle('hidden');
    },

    // 执行搜索
    performSearch: function() {
        const query = document.getElementById('player-search-input').value.trim();
        this.updateFilter('search', query);
    },

    // 清除搜索
    clearSearch: function() {
        document.getElementById('player-search-input').value = '';
        this.updateFilter('search', '');
    },

    // 更新过滤器
    updateFilter: function(filterType, value) {
        this.state.currentFilters[filterType] = value;
        this.applyFilters();
        this.saveFilters();
    },

    // 应用过滤器
    applyFilters: function() {
        const filters = this.state.currentFilters;

        this.state.filteredPlayers = this.state.players.filter(function(player) {
            // 搜索过滤
            if (filters.search && !player.name.toLowerCase().includes(filters.search.toLowerCase())) {
                return false;
            }

            // 门派过滤
            if (filters.sect !== 'all' && player.sect !== filters.sect) {
                return false;
            }

            // 等级过滤
            if (filters.level !== 'all' && player.level !== filters.level) {
                return false;
            }

            // 状态过滤
            if (filters.status !== 'all' && player.status !== filters.status) {
                return false;
            }

            // 仅好友过滤
            if (filters.friendsOnly && !this.state.friends.has(player.name)) {
                return false;
            }

            return true;
        }.bind(this));

        this.renderPlayerList();
        this.updateStats();
    },

    // 清除所有过滤器
    clearAllFilters: function() {
        this.state.currentFilters = {
            search: '',
            sect: 'all',
            level: 'all',
            status: 'all',
            friendsOnly: false
        };

        // 重置表单
        document.getElementById('player-search-input').value = '';
        document.getElementById('sect-filter').value = 'all';
        document.getElementById('level-filter').value = 'all';
        document.getElementById('status-filter').value = 'all';
        document.getElementById('friends-only').checked = false;

        this.applyFilters();
    },

    // 处理玩家操作
    handlePlayerAction: function(action, playerName, playerItem) {
        switch (action) {
            case 'chat':
                this.startPrivateChat(playerName);
                break;
            case 'view':
                this.showPlayerDetails(playerName);
                break;
            case 'invite':
                this.invitePlayer(playerName);
                break;
            case 'more':
                this.showPlayerMenu(playerName, playerItem);
                break;
            case 'friend':
                this.addFriend(playerName);
                break;
            case 'ignore':
                this.ignorePlayer(playerName);
                break;
            case 'report':
                this.reportPlayer(playerName);
                break;
        }
    },

    // 开始私聊
    startPrivateChat: function(playerName) {
        // 集成聊天频道组件
        if (window.ChatChannelsManager) {
            window.ChatChannelsManager.switchChannel('private');
            window.ChatChannelsManager.switchPrivateChat(playerName);
        }
        this.showNotification(`开始与 ${playerName} 私聊`, 'info');
    },

    // 显示玩家详情
    showPlayerDetails: function(playerName) {
        this.state.selectedPlayer = playerName;
        const player = this.state.players.find(function(p) { return p.name === playerName; });

        if (!player) return;

        // 构建详情内容
        const detailsHTML = `
            <div class="player-details">
                <div class="player-details__avatar">
                    <img src="${player.avatar || '/static/images/avatars/default.png'}" alt="${player.name}">
                    <span class="player-status ${player.status}">${this.getStatusText(player.status)}</span>
                </div>
                <div class="player-details__info">
                    <h5 class="player-name">${player.name} ${player.title || ''}</h5>
                    <div class="detail-row">
                        <span class="detail-label">门派:</span>
                        <span class="detail-value">${this.getSectText(player.sect)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">修为:</span>
                        <span class="detail-value">${this.getLevelText(player.level)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">等级:</span>
                        <span class="detail-value">${player.playerLevel || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">战力:</span>
                        <span class="detail-value">${player.power || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">位置:</span>
                        <span class="detail-value">${player.location || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">在线时间:</span>
                        <span class="detail-value">${this.formatOnlineTime(player.onlineTime)}</span>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('player-details-body').innerHTML = detailsHTML;
        document.getElementById('player-details-title').textContent = `${player.name} 的详情`;

        // 更新添加好友按钮
        const addFriendBtn = document.getElementById('add-friend-btn');
        if (this.state.friends.has(playerName)) {
            addFriendBtn.textContent = '已是好友';
            addFriendBtn.disabled = true;
        } else {
            addFriendBtn.textContent = '添加好友';
            addFriendBtn.disabled = false;
        }

        document.getElementById('player-details-dialog').classList.remove('hidden');
    },

    // 隐藏玩家详情
    hidePlayerDetails: function() {
        document.getElementById('player-details-dialog').classList.add('hidden');
        this.state.selectedPlayer = null;
    },

    // 邀请玩家
    invitePlayer: function(playerName) {
        this.sendPlayerCommand('invite_player', { target: playerName });
        this.showNotification(`已向 ${playerName} 发送组队邀请`, 'success');
    },

    // 添加好友
    addFriend: function(playerName) {
        if (this.state.friends.has(playerName)) {
            this.showNotification(`${playerName} 已经是你的好友`, 'info');
            return;
        }

        this.state.friends.add(playerName);
        this.sendPlayerCommand('add_friend', { target: playerName });
        this.showNotification(`已添加 ${playerName} 为好友`, 'success');
        this.saveFriends();
        this.renderPlayerList();
    },

    // 屏蔽玩家
    ignorePlayer: function(playerName) {
        if (confirm(`确定要屏蔽玩家 ${playerName} 吗？`)) {
            this.state.ignoredPlayers.add(playerName);
            this.sendPlayerCommand('ignore_player', { target: playerName });
            this.showNotification(`已屏蔽 ${playerName}`, 'success');
            this.saveIgnoredPlayers();
            this.renderPlayerList();
        }
    },

    // 举报玩家
    reportPlayer: function(playerName) {
        if (confirm(`确定要举报玩家 ${playerName} 吗？`)) {
            this.sendPlayerCommand('report_player', { target: playerName });
            this.showNotification(`已举报 ${playerName}`, 'success');
        }
    },

    // 显示上下文菜单
    showContextMenu: function(x, y, playerName) {
        this.state.contextMenuTarget = playerName;
        const menu = document.getElementById('player-context-menu');

        menu.style.left = x + 'px';
        menu.style.top = y + 'px';
        menu.classList.remove('hidden');

        // 调整菜单位置避免超出屏幕
        const rect = menu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            menu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            menu.style.top = (y - rect.height) + 'px';
        }
    },

    // 隐藏上下文菜单
    hideContextMenu: function() {
        document.getElementById('player-context-menu').classList.add('hidden');
        this.state.contextMenuTarget = null;
    },

    // 快速操作：仅显示好友
    showFriendsOnly: function() {
        document.getElementById('friends-only').checked = true;
        this.updateFilter('friendsOnly', true);
    },

    // 快速操作：显示同门
    showSectMembers: function() {
        // 获取当前玩家门派
        const currentPlayerSect = this.getCurrentPlayerSect();
        if (currentPlayerSect && currentPlayerSect !== 'none') {
            document.getElementById('sect-filter').value = currentPlayerSect;
            this.updateFilter('sect', currentPlayerSect);
        } else {
            this.showNotification('你还没有加入任何门派', 'info');
        }
    },

    // 快速操作：显示附近玩家
    showNearbyPlayers: function() {
        // 这里可以实现基于位置的过滤
        this.showNotification('附近玩家功能开发中', 'info');
    },

    // 显示排行榜
    showRankingList: function() {
        document.getElementById('ranking-dialog').classList.remove('hidden');
        this.switchRankingTab('level');
    },

    // 隐藏排行榜
    hideRankingDialog: function() {
        document.getElementById('ranking-dialog').classList.add('hidden');
    },

    // 切换排行榜标签
    switchRankingTab: function(ranking) {
        // 更新标签样式
        document.querySelectorAll('.ranking-tab').forEach(function(tab) {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-ranking="${ranking}"]`).classList.add('active');

        // 加载排行榜数据
        this.loadRankingData(ranking);
    },

    // 加载排行榜数据
    loadRankingData: function(ranking) {
        const content = document.getElementById('ranking-content');
        content.innerHTML = '<div class="loading">加载中...</div>';

        // 请求排行榜数据
        this.sendPlayerCommand('get_ranking', { type: ranking });
    },

    // 渲染玩家列表
    renderPlayerList: function() {
        const container = document.getElementById('players-list');
        const noPlayersElement = document.getElementById('no-players');

        if (this.state.filteredPlayers.length === 0) {
            container.innerHTML = '';
            noPlayersElement.classList.remove('hidden');
            return;
        }

        noPlayersElement.classList.add('hidden');

        const playersHTML = this.state.filteredPlayers.map(function(player) {
            return this.createPlayerItemHTML(player);
        }.bind(this)).join('');

        container.innerHTML = playersHTML;
    },

    // 创建玩家项HTML
    createPlayerItemHTML: function(player) {
        const isFriend = this.state.friends.has(player.name);
        const isSectMate = this.isSectMate(player);
        const isIgnored = this.state.ignoredPlayers.has(player.name);

        if (isIgnored) return ''; // 不显示被屏蔽的玩家

        return `
            <div class="player-item" data-player="${player.name}" data-sect="${player.sect}" data-level="${player.level}" data-status="${player.status}">
                <div class="player-avatar">
                    <img src="${player.avatar || '/static/images/avatars/default.png'}" alt="${player.name}" class="avatar-img">
                    <span class="player-status ${player.status}" title="${this.getStatusText(player.status)}"></span>
                </div>
                <div class="player-info">
                    <div class="player-name-section">
                        <span class="player-name">${player.name}</span>
                        ${player.title ? `<span class="player-title">[${player.title}]</span>` : ''}
                        ${isFriend ? '<span class="friend-indicator" title="好友">⭐</span>' : ''}
                        ${isSectMate ? '<span class="sect-mate-indicator" title="同门">🏛️</span>' : ''}
                    </div>
                    <div class="player-details">
                        <span class="player-sect">${this.getSectText(player.sect)}</span>
                        <span class="player-level">${this.getLevelText(player.level)}</span>
                        <span class="player-location">${player.location || '未知'}</span>
                    </div>
                    <div class="player-stats">
                        <span class="stat-item">等级: ${player.playerLevel || '?'}</span>
                        <span class="stat-item">战力: ${player.power || '?'}</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button type="button" class="player-action-btn primary" data-action="chat" title="私聊">
                        <span class="btn-icon">💬</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="view" title="查看">
                        <span class="btn-icon">👁️</span>
                    </button>
                    <button type="button" class="player-action-btn ${player.status === 'cultivation' ? 'disabled' : ''}" data-action="invite" title="${player.status === 'cultivation' ? '修炼中无法邀请' : '邀请'}">
                        <span class="btn-icon">🤝</span>
                    </button>
                    <button type="button" class="player-action-btn" data-action="more" title="更多">
                        <span class="btn-icon">⋯</span>
                    </button>
                </div>
            </div>
        `;
    },

    // 更新统计信息
    updateStats: function() {
        const total = this.state.filteredPlayers.length;
        const friends = this.state.filteredPlayers.filter(function(p) {
            return this.state.friends.has(p.name);
        }.bind(this)).length;
        const sectMates = this.state.filteredPlayers.filter(function(p) {
            return this.isSectMate(p);
        }.bind(this)).length;
        const newbies = this.state.filteredPlayers.filter(function(p) {
            return p.level === 'qi' || (p.playerLevel && p.playerLevel < 20);
        }).length;

        document.getElementById('total-players-count').textContent = `${total}人在线`;
        document.getElementById('stat-total').textContent = total;
        document.getElementById('stat-friends').textContent = friends;
        document.getElementById('stat-sect').textContent = sectMates;
        document.getElementById('stat-newbies').textContent = newbies;
    },

    // 工具方法：获取状态文本
    getStatusText: function(status) {
        const statusMap = {
            online: '在线',
            busy: '忙碌',
            away: '离开',
            cultivation: '修炼中'
        };
        return statusMap[status] || '未知';
    },

    // 工具方法：获取门派文本
    getSectText: function(sect) {
        const sectMap = {
            wudang: '武当派',
            shaolin: '少林寺',
            emei: '峨眉派',
            huashan: '华山派',
            kunlun: '昆仑派',
            none: '散修'
        };
        return sectMap[sect] || '未知';
    },

    // 工具方法：获取等级文本
    getLevelText: function(level) {
        const levelMap = {
            qi: '练气期',
            foundation: '筑基期',
            golden: '金丹期',
            nascent: '元婴期',
            spirit: '化神期'
        };
        return levelMap[level] || '未知';
    },

    // 工具方法：格式化在线时间
    formatOnlineTime: function(seconds) {
        if (!seconds) return '未知';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    },

    // 工具方法：判断是否同门
    isSectMate: function(player) {
        const currentPlayerSect = this.getCurrentPlayerSect();
        return currentPlayerSect && currentPlayerSect !== 'none' && player.sect === currentPlayerSect;
    },

    // 工具方法：获取当前玩家门派
    getCurrentPlayerSect: function() {
        // 这里应该从Evennia获取当前玩家门派信息
        return window.Evennia && window.Evennia.player_sect || 'none';
    },

    // WebSocket集成
    connectWebSocket: function() {
        if (!window.Evennia || !window.Evennia.msg) {
            console.warn('Evennia WebSocket not available');
            return;
        }

        // 保存原始消息处理器
        this.originalEvenniaMsg = window.Evennia.msg;

        // 扩展消息处理器
        const self = this;
        window.Evennia.msg = function(cmdname, args, kwargs) {
            // 处理玩家相关消息
            if (cmdname === 'player_list') {
                self.handlePlayerListUpdate(args, kwargs);
            } else if (cmdname === 'player_status') {
                self.handlePlayerStatusUpdate(args, kwargs);
            } else if (cmdname === 'ranking_data') {
                self.handleRankingData(args, kwargs);
            }

            // 调用原始处理器
            if (self.originalEvenniaMsg) {
                self.originalEvenniaMsg(cmdname, args, kwargs);
            }
        };
    },

    // 处理玩家列表更新
    handlePlayerListUpdate: function(args, kwargs) {
        this.state.players = args.players || [];
        this.applyFilters();
    },

    // 处理玩家状态更新
    handlePlayerStatusUpdate: function(args, kwargs) {
        const playerName = args.player;
        const newStatus = args.status;

        const player = this.state.players.find(function(p) { return p.name === playerName; });
        if (player) {
            player.status = newStatus;
            this.applyFilters();
        }
    },

    // 处理排行榜数据
    handleRankingData: function(args, kwargs) {
        const rankingType = args.type;
        const data = args.data;

        this.renderRankingData(rankingType, data);
    },

    // 渲染排行榜数据
    renderRankingData: function(type, data) {
        const content = document.getElementById('ranking-content');

        if (!data || data.length === 0) {
            content.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        const rankingHTML = data.map(function(item, index) {
            const rank = index + 1;
            const medalClass = rank <= 3 ? `medal-${rank}` : '';

            return `
                <div class="ranking-item ${medalClass}">
                    <div class="ranking-rank">${rank}</div>
                    <div class="ranking-player">
                        <span class="player-name">${item.name}</span>
                        <span class="player-sect">${this.getSectText(item.sect)}</span>
                    </div>
                    <div class="ranking-value">${item.value}</div>
                </div>
            `;
        }.bind(this)).join('');

        content.innerHTML = rankingHTML;
    },

    // 发送玩家命令
    sendPlayerCommand: function(command, data) {
        if (window.Evennia && window.Evennia.msg) {
            window.Evennia.msg('player_command', [command], data);
        }
    },

    // 请求玩家列表
    requestPlayerList: function() {
        this.sendPlayerCommand('get_player_list', {});
    },

    // 刷新玩家列表
    refreshPlayerList: function() {
        this.requestPlayerList();
        this.showNotification('正在刷新玩家列表...', 'info');
    },

    // 开始刷新定时器
    startRefreshTimer: function() {
        const self = this;
        setInterval(function() {
            self.requestPlayerList();
        }, this.config.refreshInterval);
    },

    // 显示通知
    showNotification: function(message, type) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `player-notification ${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(function() {
            notification.remove();
        }, 3000);
    },

    // 加载设置
    loadSettings: function() {
        // 加载好友列表
        const friends = JSON.parse(localStorage.getItem('playerFriends') || '[]');
        this.state.friends = new Set(friends);

        // 加载屏蔽列表
        const ignored = JSON.parse(localStorage.getItem('playerIgnored') || '[]');
        this.state.ignoredPlayers = new Set(ignored);

        // 加载过滤器设置
        const filters = JSON.parse(localStorage.getItem('playerFilters') || '{}');
        Object.assign(this.state.currentFilters, filters);

        // 应用到表单
        this.applyFiltersToForm();
    },

    // 应用过滤器到表单
    applyFiltersToForm: function() {
        const filters = this.state.currentFilters;

        document.getElementById('player-search-input').value = filters.search || '';
        document.getElementById('sect-filter').value = filters.sect || 'all';
        document.getElementById('level-filter').value = filters.level || 'all';
        document.getElementById('status-filter').value = filters.status || 'all';
        document.getElementById('friends-only').checked = filters.friendsOnly || false;
    },

    // 保存好友列表
    saveFriends: function() {
        localStorage.setItem('playerFriends', JSON.stringify(Array.from(this.state.friends)));
    },

    // 保存屏蔽列表
    saveIgnoredPlayers: function() {
        localStorage.setItem('playerIgnored', JSON.stringify(Array.from(this.state.ignoredPlayers)));
    },

    // 保存过滤器设置
    saveFilters: function() {
        localStorage.setItem('playerFilters', JSON.stringify(this.state.currentFilters));
    },

    // 显示设置
    showSettings: function() {
        this.showNotification('玩家设置功能开发中', 'info');
    },

    // 销毁组件
    destroy: function() {
        // 恢复原始消息处理器
        if (this.originalEvenniaMsg) {
            window.Evennia.msg = this.originalEvenniaMsg;
        }
    }
};

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.OnlinePlayersManager) {
        window.OnlinePlayersManager.init();
    }
});
</script>
