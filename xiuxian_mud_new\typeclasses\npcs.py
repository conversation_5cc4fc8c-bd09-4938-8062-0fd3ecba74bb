"""
智能NPC系统

基于Evennia LLM系统的仙侠智能NPC实现：
- IntelligentNPC基类：支持智能对话和个性化交互
- 专门化NPC子类：长老、师兄、师弟等不同角色类型
- 个性系统：基于修为境界、门派归属、五行属性的动态个性
- 上下文感知：实时感知世界状态和玩家历史
- AI导演集成：与三层AI导演系统深度协同
"""

import time
import random
from typing import Dict, Any, List, Optional

from evennia import DefaultCharacter
from evennia.utils import logger

# 尝试导入Evennia LLM系统
try:
    from evennia.contrib.rpg.llm import LLMCharacter
    LLM_AVAILABLE = True
except ImportError:
    logger.log_warn("Evennia LLM系统不可用，使用模拟模式")
    LLM_AVAILABLE = False
    # 创建模拟的LLMCharacter基类
    class LLMCharacter(DefaultCharacter):
        """LLM系统不可用时的模拟基类"""
        pass

from systems.tag_property_system import XianxiaTagProperty, TagPropertyQueryManager
from systems.event_system import XianxiaEventBus


class IntelligentNPC(LLMCharacter):
    """
    智能仙侠NPC基类

    基于Evennia LLM系统，支持：
    - 智能对话和个性化交互
    - 修为境界影响的对话风格
    - 门派文化塑造的性格特征
    - 五行属性表达风格
    - 与三层AI导演系统集成
    """

    # 仙侠特色属性（用于个性化和查询）
    修为境界 = XianxiaTagProperty(
        category="npc_realm",
        default="练气",
        xianxia_type="cultivation",
        valid_values=TagPropertyQueryManager.REALM_LEVELS
    )

    门派归属 = XianxiaTagProperty(
        category="npc_sect",
        default="无门派",
        xianxia_type="politics",
        valid_values=TagPropertyQueryManager.SECT_TERRITORIES
    )

    五行属性 = XianxiaTagProperty(
        category="npc_element",
        default="土",
        xianxia_type="elemental",
        valid_values=TagPropertyQueryManager.ELEMENTAL_TYPES
    )

    角色类型 = XianxiaTagProperty(
        category="npc_role",
        default="普通弟子",
        xianxia_type="social",
        valid_values=["长老", "师兄", "师姐", "师弟", "师妹", "普通弟子", "门派掌门", "客卿长老"]
    )

    def at_object_creation(self):
        """NPC创建时的初始化"""
        super().at_object_creation()

        # 标记为非战斗NPC
        self.tags.add("non_combat_npc", category="ai_type")
        self.tags.add("intelligent_npc", category="npc_type")

        # 初始化LLM个性系统
        if LLM_AVAILABLE:
            self._initialize_llm_personality()

        # 初始化NPC状态
        self.db.npc_state = {
            "creation_time": time.time(),
            "total_conversations": 0,
            "last_interaction_time": 0,
            "personality_traits": [],
            "conversation_history": {},
            "world_knowledge": {},
            "cultivation_guidance_given": [],
            "relationship_status": {}
        }

        # 初始化个性特征
        self._generate_personality_traits()

        # 订阅事件总线
        self._subscribe_to_events()

        logger.log_info(f"智能NPC {self.key} 创建成功")

    def _initialize_llm_personality(self):
        """初始化LLM个性系统"""
        if not LLM_AVAILABLE:
            return

        # 设置基础个性提示
        base_personality = self._generate_base_personality()

        # 设置LLM属性
        self.attributes.add("llm_personality", base_personality, category="ai")

        # 设置思考消息（仙侠风格）
        thinking_messages = [
            f"{self.key}沉思片刻...",
            f"{self.key}正在思考...",
            f"{self.key}掐指一算...",
            f"{self.key}运转心法，思索良久...",
            f"{self.key}闭目凝神，仔细考虑...",
            f"{self.key}抚须沉吟..."
        ]
        self.attributes.add("thinking_messages", thinking_messages, category="ai")

        # 设置对话记忆大小
        self.attributes.add("max_chat_memory_size", 25, category="ai")

    def _generate_base_personality(self) -> Dict[str, Any]:
        """生成基础个性设定"""
        realm = self.修为境界
        sect = self.门派归属
        element = self.五行属性
        role = self.角色类型

        personality = {
            "name": self.key,
            "realm": realm,
            "sect": sect,
            "element": element,
            "role": role,
            "base_prompt": self._create_personality_prompt(realm, sect, element, role),
            "dialogue_style": self._get_dialogue_style(realm, element),
            "knowledge_areas": self._get_knowledge_areas(role, sect),
            "personality_traits": self._get_personality_traits(sect, element, role)
        }

        return personality

    def _create_personality_prompt(self, realm: str, sect: str, element: str, role: str) -> str:
        """创建个性化的LLM提示"""
        base_prompt = f"""你是{self.key}，一位修仙世界中的{role}。

基本信息：
- 修为境界：{realm}
- 门派归属：{sect}
- 五行属性：{element}
- 角色定位：{role}

性格特征：
{self._get_personality_description(sect, element, role)}

对话风格：
{self._get_dialogue_style_description(realm, element)}

知识领域：
{self._get_knowledge_description(role, sect)}

请以{self.key}的身份与玩家对话，保持角色的一致性和仙侠世界的氛围。
回答要简洁明了，符合角色的修为境界和性格特征。
"""
        return base_prompt

    def _get_personality_description(self, sect: str, element: str, role: str) -> str:
        """获取性格描述"""
        descriptions = []

        # 门派文化影响
        sect_traits = {
            "青云门": "正直、守序、重视道德修养",
            "天音寺": "慈悲、宽容、佛法精深",
            "鬼王宗": "神秘、强势、不拘小节",
            "焚香谷": "优雅、高贵、注重礼仪",
            "合欢派": "魅惑、灵活、善于交际",
            "无门派": "自由、独立、不受约束"
        }
        descriptions.append(sect_traits.get(sect, "平和、中庸"))

        # 五行属性影响
        element_traits = {
            "金": "刚毅、果断、有原则",
            "木": "温和、包容、富有生机",
            "水": "灵活、智慧、善于变通",
            "火": "热情、急躁、充满活力",
            "土": "稳重、可靠、脚踏实地"
        }
        descriptions.append(element_traits.get(element, "平衡"))

        # 角色类型影响
        role_traits = {
            "长老": "威严、睿智、经验丰富",
            "师兄": "负责、可靠、乐于助人",
            "师姐": "温柔、细心、善解人意",
            "师弟": "活泼、好学、充满好奇",
            "师妹": "可爱、聪慧、天真烂漫",
            "普通弟子": "努力、上进、渴望成长",
            "门派掌门": "威严、智慧、统领全局",
            "客卿长老": "超然、淡泊、见多识广"
        }
        descriptions.append(role_traits.get(role, "普通"))

        return "、".join(descriptions)

    def _get_dialogue_style_description(self, realm: str, element: str) -> str:
        """获取对话风格描述"""
        style_parts = []

        # 修为境界影响对话风格
        realm_styles = {
            "练气": "谦逊、好学，经常询问和请教",
            "筑基": "稍有自信，但仍保持谦逊",
            "金丹": "从容、自信，言语中带有威严",
            "元婴": "深沉、睿智，话语简洁有力",
            "化神": "超然、淡泊，言语中蕴含深意",
            "炼虚": "高深莫测，话语如天音",
            "合体": "返璞归真，言语朴实却蕴含大道",
            "大乘": "如仙人般超脱，话语充满智慧",
            "仙人": "超凡脱俗，每句话都蕴含天地至理"
        }
        style_parts.append(realm_styles.get(realm, "平和自然"))

        # 五行属性影响表达方式
        element_styles = {
            "金": "言语锐利、直接，不喜欢拐弯抹角",
            "木": "语调温和、包容，善于安慰他人",
            "水": "表达灵活、机智，善于随机应变",
            "火": "语速较快、热情，容易激动",
            "土": "说话稳重、可靠，逻辑清晰"
        }
        style_parts.append(element_styles.get(element, "自然流畅"))

        return "；".join(style_parts)

    def _get_knowledge_description(self, role: str, sect: str) -> str:
        """获取知识领域描述"""
        knowledge_areas = []

        # 角色类型决定的知识领域
        role_knowledge = {
            "长老": "精通门派功法、修炼心得、门派历史、人生智慧",
            "师兄": "熟悉基础修炼、门派规矩、师弟指导、实战经验",
            "师姐": "了解修炼技巧、生活常识、情感指导、细节关怀",
            "师弟": "基础知识、修炼疑问、新鲜见闻、青春活力",
            "师妹": "入门知识、天真疑问、可爱见解、纯真想法",
            "普通弟子": "基础修炼、门派生活、同门关系、成长烦恼",
            "门派掌门": "门派大事、修仙界局势、高深功法、领导智慧",
            "客卿长老": "江湖见闻、各派功法、世事洞察、超然智慧"
        }
        knowledge_areas.append(role_knowledge.get(role, "基础知识"))

        # 门派特色知识
        sect_knowledge = {
            "青云门": "太极玄清道、七星剑阵、正道修炼心法",
            "天音寺": "大梵般若、佛法禅理、慈悲度世之道",
            "鬼王宗": "鬼道功法、阴阳术数、魔道秘术",
            "焚香谷": "焚香玉册、毒术炼丹、南疆秘法",
            "合欢派": "合欢心法、魅惑之术、双修之道",
            "无门派": "江湖见闻、各派功法、自由修炼"
        }
        if sect in sect_knowledge:
            knowledge_areas.append(sect_knowledge[sect])

        return "、".join(knowledge_areas)

    def _generate_personality_traits(self):
        """生成个性特征列表"""
        traits = []

        # 基于门派的特征
        sect_traits = {
            "青云门": ["正直", "守序", "重义"],
            "天音寺": ["慈悲", "宽容", "智慧"],
            "鬼王宗": ["神秘", "强势", "独立"],
            "焚香谷": ["优雅", "高贵", "细致"],
            "合欢派": ["魅惑", "灵活", "善变"],
            "无门派": ["自由", "独立", "随性"]
        }
        traits.extend(sect_traits.get(self.门派归属, ["平和"]))

        # 基于五行的特征
        element_traits = {
            "金": ["刚毅", "果断", "锐利"],
            "木": ["温和", "包容", "生机"],
            "水": ["灵活", "智慧", "深邃"],
            "火": ["热情", "急躁", "活力"],
            "土": ["稳重", "可靠", "厚重"]
        }
        traits.extend(element_traits.get(self.五行属性, ["平衡"]))

        # 基于角色的特征
        role_traits = {
            "长老": ["威严", "睿智", "经验丰富"],
            "师兄": ["负责", "可靠", "乐于助人"],
            "师姐": ["温柔", "细心", "善解人意"],
            "师弟": ["活泼", "好学", "好奇"],
            "师妹": ["可爱", "聪慧", "天真"],
            "普通弟子": ["努力", "上进", "谦逊"],
            "门派掌门": ["威严", "智慧", "统领"],
            "客卿长老": ["超然", "淡泊", "见识广博"]
        }
        traits.extend(role_traits.get(self.角色类型, ["普通"]))

        # 保存特征到数据库
        self.db.npc_state["personality_traits"] = list(set(traits))

    def _subscribe_to_events(self):
        """订阅事件总线"""
        try:
            # 订阅世界事件（从天道导演）
            self.tags.add("world_events_subscriber", category="event_subscription")

            # 订阅区域事件（从地灵导演）
            self.tags.add("regional_events_subscriber", category="event_subscription")

            # 订阅个体事件（从器灵导演）
            self.tags.add("individual_events_subscriber", category="event_subscription")

            logger.log_info(f"NPC {self.key} 已订阅事件总线")
        except Exception as e:
            logger.log_err(f"NPC {self.key} 订阅事件失败: {e}")

    def at_say(self, speaker, message, **kwargs):
        """处理玩家对话"""
        if not speaker:
            return

        # 更新交互统计
        self.db.npc_state["total_conversations"] += 1
        self.db.npc_state["last_interaction_time"] = time.time()

        # 记录对话历史
        if speaker.key not in self.db.npc_state["conversation_history"]:
            self.db.npc_state["conversation_history"][speaker.key] = []

        conversation_entry = {
            "timestamp": time.time(),
            "speaker": speaker.key,
            "message": message,
            "location": self.location.key if self.location else "未知"
        }
        self.db.npc_state["conversation_history"][speaker.key].append(conversation_entry)

        # 限制历史记录长度
        if len(self.db.npc_state["conversation_history"][speaker.key]) > 50:
            self.db.npc_state["conversation_history"][speaker.key] = \
                self.db.npc_state["conversation_history"][speaker.key][-25:]

        # 如果LLM可用，使用智能回复
        if LLM_AVAILABLE:
            self._handle_llm_conversation(speaker, message)
        else:
            self._handle_fallback_conversation(speaker, message)

    def _handle_llm_conversation(self, speaker, message):
        """处理LLM智能对话"""
        try:
            # 构建上下文
            context = self._build_conversation_context(speaker, message)

            # 更新LLM提示
            self._update_llm_context(context)

            # 让父类处理LLM对话
            super().at_say(speaker, message)

        except Exception as e:
            logger.log_err(f"NPC {self.key} LLM对话处理失败: {e}")
            # 降级到预设回复
            self._handle_fallback_conversation(speaker, message)

    def _handle_fallback_conversation(self, speaker, message):
        """处理降级对话（预设回复）"""
        # 基于关键词的简单回复系统
        responses = self._get_fallback_responses(message)

        if responses:
            response = responses[0]  # 选择第一个匹配的回复
            self.execute_cmd(f"say {response}")
        else:
            # 默认回复
            default_responses = [
                f"施主所言甚是，{self.key}深表赞同。",
                f"{self.key}正在思考施主的话语...",
                f"施主的见解很有道理，{self.key}受教了。",
                f"这个问题很有趣，{self.key}需要仔细考虑。"
            ]
            response = default_responses[hash(message) % len(default_responses)]
            self.execute_cmd(f"say {response}")

    def _build_conversation_context(self, speaker, message) -> Dict[str, Any]:
        """构建对话上下文"""
        context = {
            "npc_info": {
                "name": self.key,
                "realm": self.修为境界,
                "sect": self.门派归属,
                "element": self.五行属性,
                "role": self.角色类型,
                "personality_traits": self.db.npc_state.get("personality_traits", []),
                "location": self.location.key if self.location else "未知"
            },
            "speaker_info": {
                "name": speaker.key,
                "location": speaker.location.key if speaker.location else "未知"
            },
            "conversation_history": self.db.npc_state["conversation_history"].get(speaker.key, [])[-5:],
            "current_message": message,
            "world_state": self._get_current_world_state(),
            "timestamp": time.time()
        }

        return context

    def _update_llm_context(self, context: Dict[str, Any]):
        """更新LLM上下文"""
        if not LLM_AVAILABLE:
            return

        # 动态生成提示
        dynamic_prompt = self._generate_dynamic_prompt(context)

        # 更新LLM属性
        current_personality = self.attributes.get("llm_personality", {})
        current_personality["dynamic_context"] = context
        current_personality["current_prompt"] = dynamic_prompt

        self.attributes.add("llm_personality", current_personality, category="ai")

    def _generate_dynamic_prompt(self, context: Dict[str, Any]) -> str:
        """生成动态提示"""
        npc_info = context["npc_info"]
        speaker_info = context["speaker_info"]
        world_state = context.get("world_state", {})

        prompt = f"""当前情况更新：

你是{npc_info['name']}，{npc_info['role']}，修为{npc_info['realm']}，来自{npc_info['sect']}。
当前位置：{npc_info['location']}
个性特征：{', '.join(npc_info['personality_traits'])}

对话对象：{speaker_info['name']}
对话地点：{speaker_info['location']}

当前世界状态：
{self._format_world_state(world_state)}

请以{npc_info['name']}的身份，根据当前情况和个性特征，自然地回应对话。
保持角色一致性，体现修为境界和门派特色。
"""
        return prompt

    def _get_current_world_state(self) -> Dict[str, Any]:
        """获取当前世界状态（简化版）"""
        # 这里应该从AI导演系统获取实时世界状态
        # 目前返回模拟数据
        return {
            "spiritual_tide": "平稳",
            "weather": "晴朗",
            "sect_status": "和谐",
            "recent_events": ["无特殊事件"]
        }

    def _format_world_state(self, world_state: Dict[str, Any]) -> str:
        """格式化世界状态信息"""
        formatted = []
        for key, value in world_state.items():
            if key == "spiritual_tide":
                formatted.append(f"灵气潮汐：{value}")
            elif key == "weather":
                formatted.append(f"天气：{value}")
            elif key == "sect_status":
                formatted.append(f"门派状况：{value}")
            elif key == "recent_events":
                formatted.append(f"近期事件：{', '.join(value)}")

        return "\n".join(formatted) if formatted else "世界平静"

    def _get_fallback_responses(self, message: str) -> List[str]:
        """获取降级回复"""
        message_lower = message.lower()
        responses = []

        # 修炼相关
        if any(word in message_lower for word in ["修炼", "功法", "境界", "突破"]):
            responses.extend([
                f"修炼之道，贵在坚持。{self.key}愿与施主共勉。",
                f"功法修炼需要循序渐进，切不可急于求成。",
                f"境界突破需要机缘，施主需要耐心等待。"
            ])

        # 门派相关
        if any(word in message_lower for word in ["门派", "师父", "师兄", "师姐"]):
            responses.extend([
                f"我们{self.门派归属}向来团结一心，互相扶持。",
                f"门派中的师兄弟姐妹都是值得信赖的伙伴。",
                f"师父的教导让{self.key}受益匪浅。"
            ])

        # 问候相关
        if any(word in message_lower for word in ["你好", "见过", "拜见", "问候"]):
            responses.extend([
                f"施主有礼了，{self.key}这厢有礼。",
                f"见过施主，不知施主来此有何贵干？",
                f"施主客气了，{self.key}只是一介修士。"
            ])

        return responses


# 专门化NPC子类

class ElderNPC(IntelligentNPC):
    """长老NPC - 门派中的高级修士"""

    def at_object_creation(self):
        """长老NPC初始化"""
        # 设置默认属性
        self.角色类型 = "长老"
        self.修为境界 = "金丹"  # 长老至少金丹期

        super().at_object_creation()

        # 长老特有的知识和能力
        self.db.npc_state.update({
            "can_teach_skills": True,
            "can_give_missions": True,
            "authority_level": "high",
            "specialties": ["功法传授", "修炼指导", "门派历史", "人生智慧"]
        })

    def _get_fallback_responses(self, message: str) -> List[str]:
        """长老的专门回复"""
        responses = super()._get_fallback_responses(message)

        # 长老特有的回复
        elder_responses = [
            f"老夫修炼多年，对此略有心得...",
            f"年轻人，修炼之路漫长，需要耐心。",
            f"门派传承千年，自有其道理。",
            f"以老夫的经验来看，此事需要慎重考虑。"
        ]

        responses.extend(elder_responses)
        return responses


class SeniorBrotherNPC(IntelligentNPC):
    """师兄NPC - 负责指导师弟师妹"""

    def at_object_creation(self):
        """师兄NPC初始化"""
        self.角色类型 = "师兄"
        self.修为境界 = "筑基"  # 师兄通常筑基期

        super().at_object_creation()

        # 师兄特有的属性
        self.db.npc_state.update({
            "can_teach_basic_skills": True,
            "can_guide_cultivation": True,
            "authority_level": "medium",
            "specialties": ["基础修炼", "门派规矩", "实战经验", "师弟指导"]
        })

    def _get_fallback_responses(self, message: str) -> List[str]:
        """师兄的专门回复"""
        responses = super()._get_fallback_responses(message)

        # 师兄特有的回复
        brother_responses = [
            f"师弟/师妹，有什么不懂的尽管问师兄。",
            f"这个问题师兄当年也遇到过，让我来告诉你...",
            f"门派的规矩就是这样，师弟/师妹要记住。",
            f"修炼要循序渐进，师兄会帮助你的。"
        ]

        responses.extend(brother_responses)
        return responses


class SeniorSisterNPC(IntelligentNPC):
    """师姐NPC - 温柔细心的指导者"""

    def at_object_creation(self):
        """师姐NPC初始化"""
        self.角色类型 = "师姐"
        self.修为境界 = "筑基"  # 师姐通常筑基期

        super().at_object_creation()

        # 师姐特有的属性
        self.db.npc_state.update({
            "can_teach_basic_skills": True,
            "can_provide_care": True,
            "authority_level": "medium",
            "specialties": ["修炼技巧", "生活指导", "情感关怀", "细节指点"]
        })

    def _get_fallback_responses(self, message: str) -> List[str]:
        """师姐的专门回复"""
        responses = super()._get_fallback_responses(message)

        # 师姐特有的回复
        sister_responses = [
            f"师弟/师妹，师姐来帮你看看。",
            f"这个问题确实有些复杂，师姐慢慢教你。",
            f"修炼时要注意身体，不要太勉强自己。",
            f"有什么困难都可以找师姐，不要客气。"
        ]

        responses.extend(sister_responses)
        return responses


class JuniorBrotherNPC(IntelligentNPC):
    """师弟NPC - 活泼好学的年轻修士"""

    def at_object_creation(self):
        """师弟NPC初始化"""
        self.角色类型 = "师弟"
        self.修为境界 = "练气"  # 师弟通常练气期

        super().at_object_creation()

        # 师弟特有的属性
        self.db.npc_state.update({
            "can_share_experiences": True,
            "can_ask_questions": True,
            "authority_level": "low",
            "specialties": ["基础知识", "修炼疑问", "新鲜见闻", "同门交流"]
        })

    def _get_fallback_responses(self, message: str) -> List[str]:
        """师弟的专门回复"""
        responses = super()._get_fallback_responses(message)

        # 师弟特有的回复
        junior_responses = [
            f"师兄/师姐，这个问题我也很好奇！",
            f"哇，原来是这样啊，我学到了！",
            f"师兄/师姐真厉害，我要向你学习。",
            f"我们一起努力修炼吧！"
        ]

        responses.extend(junior_responses)
        return responses


class JuniorSisterNPC(IntelligentNPC):
    """师妹NPC - 可爱聪慧的年轻修士"""

    def at_object_creation(self):
        """师妹NPC初始化"""
        self.角色类型 = "师妹"
        self.修为境界 = "练气"  # 师妹通常练气期

        super().at_object_creation()

        # 师妹特有的属性
        self.db.npc_state.update({
            "can_share_insights": True,
            "can_provide_comfort": True,
            "authority_level": "low",
            "specialties": ["入门知识", "天真疑问", "可爱见解", "纯真想法"]
        })

    def _get_fallback_responses(self, message: str) -> List[str]:
        """师妹的专门回复"""
        responses = super()._get_fallback_responses(message)

        # 师妹特有的回复
        junior_sister_responses = [
            f"师兄/师姐，师妹有个问题想请教...",
            f"这个好有趣呀，师妹想知道更多！",
            f"师兄/师姐说得对，师妹明白了。",
            f"我们一起加油吧，师妹会努力的！"
        ]

        responses.extend(junior_sister_responses)
        return responses


# NPC工厂类

class NPCFactory:
    """NPC创建工厂"""

    NPC_TYPES = {
        "elder": ElderNPC,
        "senior_brother": SeniorBrotherNPC,
        "senior_sister": SeniorSisterNPC,
        "junior_brother": JuniorBrotherNPC,
        "junior_sister": JuniorSisterNPC,
        "disciple": IntelligentNPC  # 普通弟子
    }

    @classmethod
    def create_npc(cls, npc_type: str, name: str, location=None, **kwargs) -> IntelligentNPC:
        """创建指定类型的NPC"""
        if npc_type not in cls.NPC_TYPES:
            raise ValueError(f"未知的NPC类型: {npc_type}")

        npc_class = cls.NPC_TYPES[npc_type]

        # 创建NPC对象
        npc = npc_class.create(
            key=name,
            location=location,
            **kwargs
        )

        # 设置额外属性
        for attr_name, attr_value in kwargs.items():
            if hasattr(npc, attr_name):
                setattr(npc, attr_name, attr_value)

        logger.log_info(f"创建{npc_type}类型NPC: {name}")
        return npc

    @classmethod
    def get_available_types(cls) -> List[str]:
        """获取可用的NPC类型"""
        return list(cls.NPC_TYPES.keys())

    @classmethod
    def create_sect_npcs(cls, sect_name: str, location=None) -> List[IntelligentNPC]:
        """为门派创建一套标准NPC"""
        npcs = []

        # 创建长老
        elder = cls.create_npc(
            "elder",
            f"{sect_name}长老",
            location=location,
            门派归属=sect_name,
            修为境界="元婴"
        )
        npcs.append(elder)

        # 创建师兄
        senior_brother = cls.create_npc(
            "senior_brother",
            f"{sect_name}师兄",
            location=location,
            门派归属=sect_name,
            修为境界="金丹"
        )
        npcs.append(senior_brother)

        # 创建师姐
        senior_sister = cls.create_npc(
            "senior_sister",
            f"{sect_name}师姐",
            location=location,
            门派归属=sect_name,
            修为境界="筑基"
        )
        npcs.append(senior_sister)

        # 创建师弟
        junior_brother = cls.create_npc(
            "junior_brother",
            f"{sect_name}师弟",
            location=location,
            门派归属=sect_name,
            修为境界="练气"
        )
        npcs.append(junior_brother)

        # 创建师妹
        junior_sister = cls.create_npc(
            "junior_sister",
            f"{sect_name}师妹",
            location=location,
            门派归属=sect_name,
            修为境界="练气"
        )
        npcs.append(junior_sister)

        logger.log_info(f"为{sect_name}创建了{len(npcs)}个NPC")
        return npcs