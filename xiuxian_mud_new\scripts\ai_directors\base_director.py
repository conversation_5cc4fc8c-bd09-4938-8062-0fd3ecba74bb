"""
基础AI导演类

为三层AI导演架构提供统一的基础功能：
- 统一的决策接口
- 事件总线集成
- TagProperty查询集成
- LLM决策引擎调用
- 性能监控和日志记录
"""

import time
import json
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

from evennia.scripts.scripts import DefaultScript
from evennia.utils import logger
from evennia.utils.utils import class_from_module

from systems.event_system import publish_event, BaseEvent, EventPriority
from systems.query_interfaces import AIDirectorQueryInterface


class BaseDirector(DefaultScript, ABC):
    """
    AI导演基础类
    
    提供三层AI导演架构的统一基础功能
    """
    
    def at_script_creation(self):
        """脚本创建时的初始化"""
        self.persistent = True
        self.db.is_active = True
        self.db.decision_history = []
        self.db.performance_stats = {
            "total_decisions": 0,
            "total_processing_time": 0.0,
            "average_response_time": 0.0,
            "last_decision_time": None,
            "error_count": 0
        }
        
        # 子类需要设置的属性
        if not hasattr(self, 'director_type'):
            self.director_type = "base"
        if not hasattr(self, 'decision_interval'):
            self.decision_interval = 60  # 默认1分钟
            
        self.interval = self.decision_interval
        
        logger.log_info(f"{self.director_type}导演已创建，决策周期：{self.decision_interval}秒")
    
    def at_repeat(self):
        """主要的决策循环"""
        if not self.db.is_active:
            return
            
        start_time = time.perf_counter()
        
        try:
            # 收集上下文数据
            context_data = self.collect_context_data()
            
            # 进行AI决策
            decision = self.make_ai_decision(context_data)
            
            # 执行决策
            if decision:
                self.execute_decision(decision)
                
            # 更新性能统计
            self.update_performance_stats(start_time, success=True)
            
        except Exception as e:
            logger.log_err(f"{self.director_type}导演决策出错: {e}")
            self.update_performance_stats(start_time, success=False)
    
    @abstractmethod
    def collect_context_data(self) -> Dict[str, Any]:
        """
        收集决策所需的上下文数据
        
        子类必须实现此方法，返回该层级所需的数据
        """
        pass
    
    @abstractmethod
    def make_ai_decision(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        基于上下文数据进行AI决策
        
        子类必须实现此方法，返回决策结果
        """
        pass
    
    @abstractmethod
    def execute_decision(self, decision: Dict[str, Any]):
        """
        执行AI决策结果
        
        子类必须实现此方法，执行具体的决策动作
        """
        pass
    
    def get_ai_decision_engine(self):
        """获取AI决策引擎实例"""
        try:
            from systems.ai_decision_engine import AIDecisionEngine
            return AIDecisionEngine()
        except ImportError:
            logger.log_warn(f"{self.director_type}导演: AI决策引擎未找到")
            return None
    
    def publish_director_event(self, event_type: str, data: Dict[str, Any], 
                             priority: EventPriority = EventPriority.NORMAL):
        """发布导演事件到事件总线"""
        event = BaseEvent(
            event_type=f"{self.director_type}_{event_type}",
            source_id=self.key,
            data=data,
            priority=priority
        )
        
        success = publish_event(event)
        if not success:
            logger.log_warn(f"{self.director_type}导演: 事件发布失败 - {event_type}")
        
        return success

    def handle_inter_director_message(self, message: Dict[str, Any]):
        """处理导演间消息"""
        try:
            message_type = message.get("message_type")
            data = message.get("data", {})
            from_layer = message.get("from_layer")

            logger.log_info(f"{self.key}收到来自{from_layer}的消息: {message_type}")

            # 子类可以重写此方法来处理特定消息
            self._process_inter_director_message(message_type, data, from_layer)

        except Exception as e:
            logger.log_err(f"处理导演间消息失败: {e}")

    def handle_coordination_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理协调请求"""
        try:
            coordination_type = request.get("coordination_type")
            context = request.get("context", {})

            logger.log_info(f"{self.key}处理协调请求: {coordination_type}")

            # 子类可以重写此方法来处理特定协调请求
            return self._process_coordination_request(coordination_type, context)

        except Exception as e:
            logger.log_err(f"处理协调请求失败: {e}")
            return {"status": "error", "reason": str(e)}

    def _process_inter_director_message(self, message_type: str, data: Dict[str, Any], from_layer: str):
        """处理导演间消息的具体实现（子类重写）"""
        logger.log_info(f"默认消息处理: {message_type}")

    def _process_coordination_request(self, coordination_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理协调请求的具体实现（子类重写）"""
        return {"status": "approved", "reason": "default_approval"}

    def update_performance_stats(self, start_time: float, success: bool = True):
        """更新性能统计"""
        processing_time = time.perf_counter() - start_time
        
        stats = self.db.performance_stats
        stats["total_decisions"] += 1
        stats["total_processing_time"] += processing_time
        stats["average_response_time"] = (
            stats["total_processing_time"] / stats["total_decisions"]
        )
        stats["last_decision_time"] = time.time()
        
        if not success:
            stats["error_count"] += 1
        
        # 记录决策历史（保留最近100条）
        decision_record = {
            "timestamp": time.time(),
            "processing_time": processing_time,
            "success": success,
            "director_type": self.director_type
        }
        
        history = self.db.decision_history or []
        history.append(decision_record)
        if len(history) > 100:
            history = history[-100:]
        self.db.decision_history = history
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.db.performance_stats.copy()
        stats.update({
            "director_type": self.director_type,
            "decision_interval": self.decision_interval,
            "is_active": self.db.is_active,
            "recent_decisions": len(self.db.decision_history or [])
        })
        return stats
    
    def get_status(self) -> Dict[str, Any]:
        """获取导演状态信息"""
        return {
            "director_type": self.director_type,
            "is_active": self.db.is_active,
            "decision_interval": self.decision_interval,
            "last_run": self.db.performance_stats.get("last_decision_time"),
            "total_decisions": self.db.performance_stats.get("total_decisions", 0),
            "average_response_time": self.db.performance_stats.get("average_response_time", 0),
            "error_count": self.db.performance_stats.get("error_count", 0)
        }
    
    def activate(self):
        """激活导演"""
        self.db.is_active = True
        logger.log_info(f"{self.director_type}导演已激活")
    
    def deactivate(self):
        """停用导演"""
        self.db.is_active = False
        logger.log_info(f"{self.director_type}导演已停用")
    
    def force_decision(self):
        """强制执行一次决策"""
        logger.log_info(f"强制执行{self.director_type}导演决策")
        self.at_repeat()
    
    def get_decision_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取决策历史"""
        history = self.db.decision_history or []
        return history[-limit:] if limit > 0 else history
    
    def clear_decision_history(self):
        """清空决策历史"""
        self.db.decision_history = []
        logger.log_info(f"{self.director_type}导演决策历史已清空")
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.db.performance_stats = {
            "total_decisions": 0,
            "total_processing_time": 0.0,
            "average_response_time": 0.0,
            "last_decision_time": None,
            "error_count": 0
        }
        logger.log_info(f"{self.director_type}导演性能统计已重置")


class DirectorEventTypes:
    """导演事件类型常量"""
    
    # 天道导演事件
    WORLD_EVENT_TRIGGERED = "world_event_triggered"
    CROSS_REGIONAL_CONFLICT = "cross_regional_conflict"
    MAJOR_HISTORICAL_CHANGE = "major_historical_change"
    GLOBAL_RESOURCE_ALLOCATION = "global_resource_allocation"
    
    # 地灵导演事件
    CELESTIAL_EVENT = "celestial_event"
    SPIRITUAL_TIDE_CHANGE = "spiritual_tide_change"
    SECT_DYNAMICS_UPDATE = "sect_dynamics_update"
    REGIONAL_ENVIRONMENT_CHANGE = "regional_environment_change"
    
    # 器灵导演事件
    INDIVIDUAL_INTERACTION = "individual_interaction"
    EXPERIENCE_OPTIMIZATION = "experience_optimization"
    SOCIAL_RELATIONSHIP_UPDATE = "social_relationship_update"
    GROWTH_GUIDANCE = "growth_guidance"


def get_director_by_type(director_type: str) -> Optional[BaseDirector]:
    """根据类型获取导演实例"""
    from evennia import search_script
    
    script_key = f"{director_type}_director"
    scripts = search_script(script_key)
    
    return scripts[0] if scripts else None


def get_all_directors() -> List[BaseDirector]:
    """获取所有导演实例"""
    directors = []
    for director_type in ["tiandao", "diling", "qiling"]:
        director = get_director_by_type(director_type)
        if director:
            directors.append(director)
    
    return directors
