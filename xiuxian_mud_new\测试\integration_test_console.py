"""
集成测试指挥中心

统一的测试控制界面，用于管理所有集成测试套件：
- 测试套件管理和执行
- 实时测试状态监控
- 测试结果汇总和分析
- 测试报告生成和导出
- 测试环境管理
"""

import os
import sys
import time
import json
import threading
import unittest
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from evennia.utils.logger import log_info, log_err
from evennia.utils.search import search_script

from .integration_test_framework import XianxiaIntegrationTest, cleanup_test_objects
from .test_micro_integration import *
from .test_macro_integration import *
from .test_end_to_end import *
from .test_ai_director_coordination import *
from .test_performance_stress import *
from .test_data_generator import test_data_generator


class TestSuiteManager:
    """测试套件管理器"""
    
    def __init__(self):
        self.test_suites = {
            "micro_integration": {
                "name": "微集成测试",
                "description": "测试两个系统之间的集成",
                "test_classes": [
                    "EventBusAIDirectorIntegrationTest",
                    "TagPropertyWorldSystemIntegrationTest", 
                    "AIDirectorNPCIntegrationTest",
                    "WorldSystemCombatIntegrationTest",
                    "SocialSystemEventBusIntegrationTest"
                ],
                "estimated_time": "5分钟",
                "priority": "HIGH"
            },
            "macro_integration": {
                "name": "宏集成测试",
                "description": "测试多个系统的协调工作",
                "test_classes": [
                    "CultivationBreakthroughEventChainTest",
                    "CombatEventMultiSystemTest",
                    "WorldChangesPropagationTest"
                ],
                "estimated_time": "8分钟",
                "priority": "HIGH"
            },
            "end_to_end": {
                "name": "端到端测试",
                "description": "完整的游戏会话测试",
                "test_classes": [
                    "CompleteGameSessionTest",
                    "MultiPlayerConcurrentTest",
                    "LongRunningStabilityTest"
                ],
                "estimated_time": "15分钟",
                "priority": "MEDIUM"
            },
            "ai_director_coordination": {
                "name": "AI导演协调测试",
                "description": "测试三层AI导演系统协调",
                "test_classes": [
                    "AIDirectorHierarchyTest",
                    "AIDirectorLoadBalancingTest"
                ],
                "estimated_time": "10分钟",
                "priority": "HIGH"
            },
            "performance_stress": {
                "name": "性能压力测试",
                "description": "系统性能和负载测试",
                "test_classes": [
                    "TagPropertyPerformanceStressTest",
                    "EventSystemThroughputTest",
                    "AIDirectorDecisionLatencyTest",
                    "ConcurrentUserSimulationTest"
                ],
                "estimated_time": "20分钟",
                "priority": "MEDIUM"
            }
        }
        
        self.test_results = {}
        self.test_status = {}
        self.test_logs = {}
    
    def get_available_suites(self) -> Dict[str, Dict]:
        """获取可用的测试套件"""
        return self.test_suites
    
    def get_suite_status(self, suite_name: str) -> Dict[str, Any]:
        """获取测试套件状态"""
        return self.test_status.get(suite_name, {"status": "NOT_STARTED"})
    
    def get_suite_results(self, suite_name: str) -> Dict[str, Any]:
        """获取测试套件结果"""
        return self.test_results.get(suite_name, {})


class TestExecutionEngine:
    """测试执行引擎"""
    
    def __init__(self, suite_manager: TestSuiteManager):
        self.suite_manager = suite_manager
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.running_tests = {}
        self.test_environment = None
    
    def setup_test_environment(self, scenario_type: str = "comprehensive"):
        """设置测试环境"""
        log_info(f"设置测试环境: {scenario_type}")
        
        try:
            # 清理现有环境
            cleanup_test_objects()
            
            # 生成测试场景
            scenario = test_data_generator.generate_test_scenario(scenario_type)
            
            # 创建测试对象
            from .test_data_generator import create_test_environment_from_scenario
            self.test_environment = create_test_environment_from_scenario(scenario)
            
            log_info(f"测试环境设置完成: {len(self.test_environment.get('characters', []))} 个角色, "
                    f"{len(self.test_environment.get('locations', []))} 个地点")
            
            return True
            
        except Exception as e:
            log_err(f"测试环境设置失败: {e}")
            return False
    
    def execute_test_suite(self, suite_name: str, async_execution: bool = True) -> Optional[str]:
        """执行测试套件"""
        if suite_name not in self.suite_manager.test_suites:
            log_err(f"未知的测试套件: {suite_name}")
            return None
        
        suite_info = self.suite_manager.test_suites[suite_name]
        
        # 更新状态
        self.suite_manager.test_status[suite_name] = {
            "status": "RUNNING",
            "start_time": datetime.now(),
            "progress": 0,
            "current_test": None
        }
        
        if async_execution:
            # 异步执行
            future = self.executor.submit(self._run_test_suite, suite_name, suite_info)
            self.running_tests[suite_name] = future
            return suite_name
        else:
            # 同步执行
            return self._run_test_suite(suite_name, suite_info)
    
    def _run_test_suite(self, suite_name: str, suite_info: Dict) -> str:
        """运行测试套件"""
        log_info(f"开始执行测试套件: {suite_info['name']}")
        
        try:
            # 创建测试加载器
            loader = unittest.TestLoader()
            suite = unittest.TestSuite()
            
            # 加载测试类
            test_classes = suite_info["test_classes"]
            total_tests = len(test_classes)
            
            for i, test_class_name in enumerate(test_classes):
                try:
                    # 更新进度
                    self.suite_manager.test_status[suite_name]["progress"] = (i / total_tests) * 100
                    self.suite_manager.test_status[suite_name]["current_test"] = test_class_name
                    
                    # 动态导入测试类
                    test_class = self._get_test_class(test_class_name)
                    if test_class:
                        suite.addTest(loader.loadTestsFromTestCase(test_class))
                        log_info(f"加载测试类: {test_class_name}")
                    else:
                        log_err(f"无法找到测试类: {test_class_name}")
                        
                except Exception as e:
                    log_err(f"加载测试类失败 {test_class_name}: {e}")
            
            # 执行测试
            runner = unittest.TextTestRunner(verbosity=2, stream=open(os.devnull, 'w'))
            result = runner.run(suite)
            
            # 处理结果
            test_result = {
                "suite_name": suite_name,
                "total_tests": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "skipped": len(result.skipped) if hasattr(result, 'skipped') else 0,
                "success_rate": ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
                "execution_time": time.time() - self.suite_manager.test_status[suite_name]["start_time"].timestamp(),
                "details": {
                    "failures": [{"test": str(test), "error": error} for test, error in result.failures],
                    "errors": [{"test": str(test), "error": error} for test, error in result.errors]
                }
            }
            
            # 更新状态和结果
            self.suite_manager.test_status[suite_name] = {
                "status": "COMPLETED",
                "end_time": datetime.now(),
                "progress": 100,
                "success": len(result.failures) == 0 and len(result.errors) == 0
            }
            
            self.suite_manager.test_results[suite_name] = test_result
            
            log_info(f"测试套件完成: {suite_info['name']}, 成功率: {test_result['success_rate']:.1f}%")
            
            return suite_name
            
        except Exception as e:
            log_err(f"测试套件执行失败: {e}")
            
            # 更新失败状态
            self.suite_manager.test_status[suite_name] = {
                "status": "FAILED",
                "end_time": datetime.now(),
                "error": str(e)
            }
            
            return suite_name
    
    def _get_test_class(self, class_name: str):
        """获取测试类"""
        # 尝试从各个模块中获取测试类
        modules = [
            sys.modules.get(__name__),
            sys.modules.get('test_micro_integration'),
            sys.modules.get('test_macro_integration'),
            sys.modules.get('test_end_to_end'),
            sys.modules.get('test_ai_director_coordination'),
            sys.modules.get('test_performance_stress')
        ]
        
        for module in modules:
            if module and hasattr(module, class_name):
                return getattr(module, class_name)
        
        return None
    
    def stop_test_suite(self, suite_name: str) -> bool:
        """停止测试套件执行"""
        if suite_name in self.running_tests:
            future = self.running_tests[suite_name]
            if not future.done():
                future.cancel()
                
                self.suite_manager.test_status[suite_name] = {
                    "status": "CANCELLED",
                    "end_time": datetime.now()
                }
                
                log_info(f"测试套件已停止: {suite_name}")
                return True
        
        return False
    
    def get_execution_status(self) -> Dict[str, Any]:
        """获取执行状态"""
        status = {
            "running_tests": list(self.running_tests.keys()),
            "completed_tests": [],
            "failed_tests": [],
            "total_progress": 0
        }
        
        total_suites = len(self.suite_manager.test_suites)
        completed_count = 0
        
        for suite_name, suite_status in self.suite_manager.test_status.items():
            if suite_status["status"] == "COMPLETED":
                status["completed_tests"].append(suite_name)
                completed_count += 1
            elif suite_status["status"] == "FAILED":
                status["failed_tests"].append(suite_name)
                completed_count += 1
        
        if total_suites > 0:
            status["total_progress"] = (completed_count / total_suites) * 100
        
        return status


class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, suite_manager: TestSuiteManager):
        self.suite_manager = suite_manager
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """生成汇总报告"""
        report = {
            "generation_time": datetime.now().isoformat(),
            "total_suites": len(self.suite_manager.test_suites),
            "executed_suites": len(self.suite_manager.test_results),
            "overall_statistics": {
                "total_tests": 0,
                "total_failures": 0,
                "total_errors": 0,
                "overall_success_rate": 0,
                "total_execution_time": 0
            },
            "suite_results": [],
            "recommendations": []
        }
        
        # 统计总体数据
        for suite_name, result in self.suite_manager.test_results.items():
            report["overall_statistics"]["total_tests"] += result.get("total_tests", 0)
            report["overall_statistics"]["total_failures"] += result.get("failures", 0)
            report["overall_statistics"]["total_errors"] += result.get("errors", 0)
            report["overall_statistics"]["total_execution_time"] += result.get("execution_time", 0)
            
            # 添加套件结果
            suite_info = self.suite_manager.test_suites.get(suite_name, {})
            suite_result = {
                "suite_name": suite_name,
                "display_name": suite_info.get("name", suite_name),
                "priority": suite_info.get("priority", "MEDIUM"),
                "success_rate": result.get("success_rate", 0),
                "execution_time": result.get("execution_time", 0),
                "status": "PASSED" if result.get("success_rate", 0) == 100 else "FAILED"
            }
            report["suite_results"].append(suite_result)
        
        # 计算总体成功率
        total_tests = report["overall_statistics"]["total_tests"]
        if total_tests > 0:
            successful_tests = total_tests - report["overall_statistics"]["total_failures"] - report["overall_statistics"]["total_errors"]
            report["overall_statistics"]["overall_success_rate"] = (successful_tests / total_tests) * 100
        
        # 生成建议
        report["recommendations"] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        overall_success_rate = report["overall_statistics"]["overall_success_rate"]
        
        if overall_success_rate < 80:
            recommendations.append("整体测试成功率较低，建议优先修复失败的测试用例")
        
        if overall_success_rate < 95:
            recommendations.append("建议增加错误处理和异常情况的测试覆盖")
        
        # 检查高优先级套件
        high_priority_failed = [s for s in report["suite_results"] 
                               if s["priority"] == "HIGH" and s["status"] == "FAILED"]
        
        if high_priority_failed:
            recommendations.append("高优先级测试套件存在失败，建议立即修复")
        
        # 检查执行时间
        total_time = report["overall_statistics"]["total_execution_time"]
        if total_time > 3600:  # 超过1小时
            recommendations.append("测试执行时间过长，建议优化测试性能或并行执行")
        
        return recommendations
    
    def export_report(self, report: Dict[str, Any], format: str = "json", filename: str = None) -> str:
        """导出报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"integration_test_report_{timestamp}"
        
        if format == "json":
            filepath = f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        
        elif format == "html":
            filepath = f"{filename}.html"
            html_content = self._generate_html_report(report)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        else:
            raise ValueError(f"不支持的报告格式: {format}")
        
        log_info(f"测试报告已导出: {filepath}")
        return filepath
    
    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>仙侠MUD集成测试报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .statistics { display: flex; justify-content: space-around; margin: 20px 0; }
                .stat-box { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .suite-results { margin: 20px 0; }
                .suite-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
                .passed { border-left-color: #4CAF50; }
                .failed { border-left-color: #f44336; }
                .recommendations { background-color: #fff3cd; padding: 15px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>仙侠MUD集成测试报告</h1>
                <p>生成时间: {generation_time}</p>
            </div>
            
            <div class="statistics">
                <div class="stat-box">
                    <h3>总测试数</h3>
                    <p>{total_tests}</p>
                </div>
                <div class="stat-box">
                    <h3>成功率</h3>
                    <p>{success_rate:.1f}%</p>
                </div>
                <div class="stat-box">
                    <h3>执行时间</h3>
                    <p>{execution_time:.1f}秒</p>
                </div>
            </div>
            
            <div class="suite-results">
                <h2>测试套件结果</h2>
                {suite_items}
            </div>
            
            <div class="recommendations">
                <h2>改进建议</h2>
                <ul>
                    {recommendation_items}
                </ul>
            </div>
        </body>
        </html>
        """
        
        # 生成套件结果HTML
        suite_items = ""
        for suite in report["suite_results"]:
            status_class = "passed" if suite["status"] == "PASSED" else "failed"
            suite_items += f"""
            <div class="suite-item {status_class}">
                <h3>{suite['display_name']}</h3>
                <p>成功率: {suite['success_rate']:.1f}% | 执行时间: {suite['execution_time']:.1f}秒</p>
            </div>
            """
        
        # 生成建议HTML
        recommendation_items = ""
        for rec in report["recommendations"]:
            recommendation_items += f"<li>{rec}</li>"
        
        return html_template.format(
            generation_time=report["generation_time"],
            total_tests=report["overall_statistics"]["total_tests"],
            success_rate=report["overall_statistics"]["overall_success_rate"],
            execution_time=report["overall_statistics"]["total_execution_time"],
            suite_items=suite_items,
            recommendation_items=recommendation_items
        )


class IntegrationTestConsole:
    """集成测试指挥中心主控制台"""
    
    def __init__(self):
        self.suite_manager = TestSuiteManager()
        self.execution_engine = TestExecutionEngine(self.suite_manager)
        self.report_generator = TestReportGenerator(self.suite_manager)
        self.console_running = False
    
    def start_console(self):
        """启动控制台"""
        self.console_running = True
        log_info("集成测试指挥中心已启动")
        
        # 显示欢迎信息
        self._display_welcome()
        
        # 主控制循环
        while self.console_running:
            try:
                self._display_main_menu()
                choice = input("\n请选择操作 (输入数字): ").strip()
                self._handle_menu_choice(choice)
                
            except KeyboardInterrupt:
                log_info("用户中断，正在退出...")
                self.console_running = False
            except Exception as e:
                log_err(f"控制台错误: {e}")
    
    def _display_welcome(self):
        """显示欢迎信息"""
        print("\n" + "="*60)
        print("           仙侠MUD集成测试指挥中心")
        print("="*60)
        print("欢迎使用集成测试管理系统！")
        print("本系统提供完整的测试套件管理和执行功能。")
        print("="*60)
    
    def _display_main_menu(self):
        """显示主菜单"""
        print("\n主菜单:")
        print("1. 查看测试套件")
        print("2. 执行单个测试套件")
        print("3. 执行所有测试套件")
        print("4. 查看测试状态")
        print("5. 生成测试报告")
        print("6. 设置测试环境")
        print("7. 停止运行中的测试")
        print("8. 退出")
    
    def _handle_menu_choice(self, choice: str):
        """处理菜单选择"""
        if choice == "1":
            self._show_test_suites()
        elif choice == "2":
            self._execute_single_suite()
        elif choice == "3":
            self._execute_all_suites()
        elif choice == "4":
            self._show_test_status()
        elif choice == "5":
            self._generate_report()
        elif choice == "6":
            self._setup_environment()
        elif choice == "7":
            self._stop_running_tests()
        elif choice == "8":
            self.console_running = False
        else:
            print("无效选择，请重新输入。")
    
    def _show_test_suites(self):
        """显示测试套件"""
        suites = self.suite_manager.get_available_suites()
        
        print("\n可用的测试套件:")
        print("-" * 80)
        for i, (suite_id, suite_info) in enumerate(suites.items(), 1):
            print(f"{i}. {suite_info['name']}")
            print(f"   描述: {suite_info['description']}")
            print(f"   预计时间: {suite_info['estimated_time']}")
            print(f"   优先级: {suite_info['priority']}")
            print()
    
    def _execute_single_suite(self):
        """执行单个测试套件"""
        suites = self.suite_manager.get_available_suites()
        suite_list = list(suites.keys())
        
        print("\n选择要执行的测试套件:")
        for i, suite_id in enumerate(suite_list, 1):
            print(f"{i}. {suites[suite_id]['name']}")
        
        try:
            choice = int(input("\n请输入套件编号: ")) - 1
            if 0 <= choice < len(suite_list):
                suite_id = suite_list[choice]
                print(f"\n开始执行: {suites[suite_id]['name']}")
                self.execution_engine.execute_test_suite(suite_id, async_execution=False)
                print("测试套件执行完成！")
            else:
                print("无效的套件编号。")
        except ValueError:
            print("请输入有效的数字。")
    
    def _execute_all_suites(self):
        """执行所有测试套件"""
        suites = self.suite_manager.get_available_suites()
        
        print(f"\n将执行 {len(suites)} 个测试套件...")
        
        for suite_id in suites.keys():
            print(f"执行: {suites[suite_id]['name']}")
            self.execution_engine.execute_test_suite(suite_id, async_execution=False)
        
        print("\n所有测试套件执行完成！")
    
    def _show_test_status(self):
        """显示测试状态"""
        status = self.execution_engine.get_execution_status()
        
        print("\n测试执行状态:")
        print("-" * 50)
        print(f"总体进度: {status['total_progress']:.1f}%")
        print(f"运行中: {len(status['running_tests'])} 个")
        print(f"已完成: {len(status['completed_tests'])} 个")
        print(f"失败: {len(status['failed_tests'])} 个")
        
        if status['running_tests']:
            print(f"\n运行中的测试: {', '.join(status['running_tests'])}")
        
        if status['completed_tests']:
            print(f"已完成的测试: {', '.join(status['completed_tests'])}")
        
        if status['failed_tests']:
            print(f"失败的测试: {', '.join(status['failed_tests'])}")
    
    def _generate_report(self):
        """生成测试报告"""
        if not self.suite_manager.test_results:
            print("没有可用的测试结果，请先执行测试。")
            return
        
        print("\n生成测试报告...")
        report = self.report_generator.generate_summary_report()
        
        # 显示报告摘要
        print(f"\n测试报告摘要:")
        print(f"总测试数: {report['overall_statistics']['total_tests']}")
        print(f"成功率: {report['overall_statistics']['overall_success_rate']:.1f}%")
        print(f"执行时间: {report['overall_statistics']['total_execution_time']:.1f}秒")
        
        # 询问是否导出
        export_choice = input("\n是否导出报告? (y/n): ").strip().lower()
        if export_choice == 'y':
            format_choice = input("选择格式 (json/html): ").strip().lower()
            if format_choice in ['json', 'html']:
                filepath = self.report_generator.export_report(report, format_choice)
                print(f"报告已导出到: {filepath}")
            else:
                print("无效的格式选择。")
    
    def _setup_environment(self):
        """设置测试环境"""
        print("\n设置测试环境...")
        
        scenario_types = ["comprehensive", "combat", "social", "cultivation", "exploration"]
        print("可用的测试场景:")
        for i, scenario in enumerate(scenario_types, 1):
            print(f"{i}. {scenario}")
        
        try:
            choice = int(input("\n选择场景类型 (默认为1): ") or "1") - 1
            if 0 <= choice < len(scenario_types):
                scenario_type = scenario_types[choice]
                success = self.execution_engine.setup_test_environment(scenario_type)
                if success:
                    print("测试环境设置成功！")
                else:
                    print("测试环境设置失败。")
            else:
                print("无效的场景选择。")
        except ValueError:
            print("请输入有效的数字。")
    
    def _stop_running_tests(self):
        """停止运行中的测试"""
        status = self.execution_engine.get_execution_status()
        
        if not status['running_tests']:
            print("当前没有运行中的测试。")
            return
        
        print(f"\n运行中的测试: {', '.join(status['running_tests'])}")
        suite_name = input("输入要停止的测试套件名称: ").strip()
        
        if self.execution_engine.stop_test_suite(suite_name):
            print(f"测试套件 {suite_name} 已停止。")
        else:
            print(f"无法停止测试套件 {suite_name}。")


# 全局控制台实例
integration_console = IntegrationTestConsole()


def start_integration_test_console():
    """启动集成测试控制台"""
    integration_console.start_console()


if __name__ == "__main__":
    # 直接启动控制台
    start_integration_test_console()
