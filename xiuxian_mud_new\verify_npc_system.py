"""
智能NPC系统验证脚本

验证智能NPC系统的核心组件是否正确实现
"""

import sys
import os

# 配置Django设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

def verify_npc_system():
    """验证NPC系统组件"""
    print("🔍 开始验证智能NPC系统...")
    
    verification_results = []
    
    # 1. 验证核心NPC类
    try:
        from typeclasses.npcs import IntelligentNPC, NPCFactory
        from typeclasses.npcs import ElderNPC, SeniorBrotherNPC, SeniorSisterNPC
        verification_results.append("✅ 核心NPC类导入成功")
    except ImportError as e:
        verification_results.append(f"❌ 核心NPC类导入失败: {e}")
    
    # 2. 验证个性引擎
    try:
        from systems.npc_personality_engine import NPCPersonalityEngine
        engine = NPCPersonalityEngine()
        
        # 测试个性生成
        test_attributes = {
            "修为境界": "金丹",
            "门派归属": "青云门", 
            "五行属性": "火",
            "角色类型": "师兄"
        }
        profile = engine.generate_personality_profile(test_attributes)
        
        if "basic_info" in profile and "dialogue_style" in profile:
            verification_results.append("✅ NPC个性引擎验证成功")
        else:
            verification_results.append("❌ NPC个性引擎返回格式错误")
            
    except Exception as e:
        verification_results.append(f"❌ NPC个性引擎验证失败: {e}")
    
    # 3. 验证上下文管理器
    try:
        from systems.npc_context_manager import NPCContextManager
        manager = NPCContextManager()
        verification_results.append("✅ NPC上下文管理器导入成功")
    except ImportError as e:
        verification_results.append(f"❌ NPC上下文管理器导入失败: {e}")
    
    # 4. 验证AI导演集成
    try:
        from systems.npc_integration_system import NPCDirectorIntegration
        integration = NPCDirectorIntegration()
        verification_results.append("✅ NPC导演集成系统导入成功")
    except ImportError as e:
        verification_results.append(f"❌ NPC导演集成系统导入失败: {e}")
    
    # 5. 验证管理命令
    try:
        from commands.npc_management_commands import CmdCreateNPC, CmdNPCStatus
        from commands.npc_management_commands import CmdNPCConfig, CmdNPCHistory
        verification_results.append("✅ NPC管理命令导入成功")
    except ImportError as e:
        verification_results.append(f"❌ NPC管理命令导入失败: {e}")
    
    # 6. 验证工厂模式
    try:
        from typeclasses.npcs import NPCFactory
        
        # 检查工厂方法
        if hasattr(NPCFactory, 'create_npc') and hasattr(NPCFactory, 'create_sect_npcs'):
            verification_results.append("✅ NPC工厂模式验证成功")
        else:
            verification_results.append("❌ NPC工厂模式缺少必要方法")
            
    except Exception as e:
        verification_results.append(f"❌ NPC工厂模式验证失败: {e}")
    
    # 7. 验证TagProperty集成
    try:
        from systems.tag_property_system import XianxiaTagProperty
        verification_results.append("✅ TagProperty系统集成验证成功")
    except ImportError as e:
        verification_results.append(f"❌ TagProperty系统集成验证失败: {e}")
    
    # 输出验证结果
    print("\n📋 验证结果:")
    for result in verification_results:
        print(f"  {result}")
    
    # 统计结果
    success_count = len([r for r in verification_results if r.startswith("✅")])
    total_count = len(verification_results)
    
    print(f"\n📊 验证统计: {success_count}/{total_count} 项通过")
    
    if success_count == total_count:
        print("🎉 智能NPC系统验证完全通过！")
        return True
    else:
        print("⚠️ 智能NPC系统存在问题，需要修复")
        return False

def verify_file_structure():
    """验证文件结构"""
    print("\n📁 验证文件结构...")
    
    expected_files = [
        "typeclasses/npcs.py",
        "systems/npc_personality_engine.py", 
        "systems/npc_context_manager.py",
        "systems/npc_integration_system.py",
        "commands/npc_management_commands.py"
    ]
    
    file_results = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            file_results.append(f"✅ {file_path}")
        else:
            file_results.append(f"❌ {file_path} 缺失")
    
    for result in file_results:
        print(f"  {result}")
    
    success_count = len([r for r in file_results if r.startswith("✅")])
    total_count = len(file_results)
    
    print(f"\n📊 文件结构: {success_count}/{total_count} 文件存在")
    
    return success_count == total_count

def main():
    """主函数"""
    print("=" * 60)
    print("智能NPC系统验证工具")
    print("=" * 60)
    
    # 验证文件结构
    file_structure_ok = verify_file_structure()
    
    # 验证系统功能
    system_ok = verify_npc_system()
    
    print("\n" + "=" * 60)
    
    if file_structure_ok and system_ok:
        print("🎊 智能NPC系统完整验证通过！")
        print("✨ Day 10-11 智能NPC系统开发完成")
        return True
    else:
        print("🔧 智能NPC系统需要进一步修复")
        return False

if __name__ == "__main__":
    main()
