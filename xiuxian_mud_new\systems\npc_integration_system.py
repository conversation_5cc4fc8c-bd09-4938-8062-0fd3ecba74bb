"""
NPC与AI导演集成系统

负责智能NPC与三层AI导演系统的深度集成：
- 事件订阅机制
- 状态同步系统
- 协同决策接口
- 叙事连贯性管理
- 修炼指导系统
"""

import time
from typing import Dict, Any, List, Optional
from evennia.utils import logger, search
from evennia.scripts.models import ScriptDB


class NPCDirectorIntegration:
    """NPC与AI导演集成管理器"""
    
    def __init__(self):
        """初始化集成管理器"""
        self.event_subscriptions = {}
        self.director_cache = {}
        self.cache_expiry = 300  # 5分钟缓存
        self.last_cache_update = 0
        logger.log_info("NPC导演集成系统初始化完成")
    
    def register_npc_with_directors(self, npc):
        """将NPC注册到AI导演系统"""
        try:
            # 获取NPC基本信息
            npc_info = {
                "name": npc.key,
                "realm": getattr(npc, "修为境界", "练气"),
                "sect": getattr(npc, "门派归属", "无门派"),
                "element": getattr(npc, "五行属性", "土"),
                "role": getattr(npc, "角色类型", "普通弟子"),
                "location": npc.location.key if npc.location else "未知"
            }
            
            # 注册到器灵导演（个体级）
            self._register_to_qiling_director(npc, npc_info)
            
            # 根据角色类型注册到地灵导演（区域级）
            if npc_info["role"] in ["长老", "门派掌门"]:
                self._register_to_diling_director(npc, npc_info)
            
            # 重要NPC注册到天道导演（世界级）
            if npc_info["role"] in ["门派掌门", "客卿长老"] or npc_info["realm"] in ["大乘", "仙人"]:
                self._register_to_tiandao_director(npc, npc_info)
            
            logger.log_info(f"NPC {npc.key} 已注册到AI导演系统")
            
        except Exception as e:
            logger.log_err(f"NPC {npc.key} 注册到AI导演系统失败: {e}")
    
    def _register_to_qiling_director(self, npc, npc_info):
        """注册到器灵导演"""
        qiling_director = self._get_director_script("qiling_director")
        if qiling_director:
            # 添加NPC到器灵导演的管理列表
            managed_npcs = qiling_director.db.managed_npcs or {}
            managed_npcs[npc.key] = {
                "npc_ref": npc,
                "npc_info": npc_info,
                "registration_time": time.time(),
                "last_interaction": 0,
                "interaction_count": 0,
                "guidance_provided": []
            }
            qiling_director.db.managed_npcs = managed_npcs
            
            # 订阅个体事件
            npc.tags.add("qiling_managed", category="ai_director")
    
    def _register_to_diling_director(self, npc, npc_info):
        """注册到地灵导演"""
        diling_director = self._get_director_script("diling_director")
        if diling_director:
            # 添加NPC到地灵导演的区域管理
            regional_npcs = diling_director.db.regional_npcs or {}
            location_key = npc_info["location"]
            
            if location_key not in regional_npcs:
                regional_npcs[location_key] = []
            
            regional_npcs[location_key].append({
                "npc_key": npc.key,
                "npc_info": npc_info,
                "registration_time": time.time()
            })
            
            diling_director.db.regional_npcs = regional_npcs
            npc.tags.add("diling_managed", category="ai_director")
    
    def _register_to_tiandao_director(self, npc, npc_info):
        """注册到天道导演"""
        tiandao_director = self._get_director_script("tiandao_director")
        if tiandao_director:
            # 添加重要NPC到天道导演的世界管理
            important_npcs = tiandao_director.db.important_npcs or {}
            important_npcs[npc.key] = {
                "npc_info": npc_info,
                "registration_time": time.time(),
                "world_influence": self._calculate_world_influence(npc_info)
            }
            
            tiandao_director.db.important_npcs = important_npcs
            npc.tags.add("tiandao_managed", category="ai_director")
    
    def _get_director_script(self, director_name: str):
        """获取AI导演脚本"""
        current_time = time.time()
        
        # 检查缓存
        if (current_time - self.last_cache_update) < self.cache_expiry and director_name in self.director_cache:
            return self.director_cache[director_name]
        
        # 搜索导演脚本
        scripts = search.search_script(director_name)
        director = scripts[0] if scripts else None
        
        # 更新缓存
        self.director_cache[director_name] = director
        if not self.director_cache:  # 如果是第一次更新
            self.last_cache_update = current_time
        
        return director
    
    def _calculate_world_influence(self, npc_info: Dict[str, Any]) -> float:
        """计算NPC的世界影响力"""
        influence = 0.0
        
        # 修为境界影响
        realm_influence = {
            "练气": 0.1, "筑基": 0.2, "金丹": 0.4, "元婴": 0.6,
            "化神": 0.7, "炼虚": 0.8, "合体": 0.9, "大乘": 0.95, "仙人": 1.0
        }
        influence += realm_influence.get(npc_info["realm"], 0.1)
        
        # 角色类型影响
        role_influence = {
            "门派掌门": 0.8, "客卿长老": 0.6, "长老": 0.4,
            "师兄": 0.2, "师姐": 0.2, "普通弟子": 0.1
        }
        influence += role_influence.get(npc_info["role"], 0.1)
        
        return min(influence, 1.0)
    
    def sync_npc_with_world_state(self, npc):
        """同步NPC与世界状态"""
        try:
            world_state = self._get_current_world_state()
            
            # 更新NPC的世界知识
            if not hasattr(npc.db, "npc_state"):
                npc.db.npc_state = {}
            
            npc.db.npc_state["world_knowledge"] = {
                "last_update": time.time(),
                "world_state": world_state,
                "known_events": self._get_relevant_events_for_npc(npc, world_state)
            }
            
        except Exception as e:
            logger.log_err(f"同步NPC {npc.key} 世界状态失败: {e}")
    
    def _get_current_world_state(self) -> Dict[str, Any]:
        """获取当前世界状态"""
        world_state = {}
        
        # 从天道导演获取世界状态
        tiandao_director = self._get_director_script("tiandao_director")
        if tiandao_director:
            world_state.update(tiandao_director.db.world_state or {})
        
        # 从地灵导演获取区域状态
        diling_director = self._get_director_script("diling_director")
        if diling_director:
            regional_state = diling_director.db.regional_state or {}
            world_state["regional_states"] = regional_state
        
        return world_state
    
    def _get_relevant_events_for_npc(self, npc, world_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取与NPC相关的事件"""
        relevant_events = []
        
        npc_sect = getattr(npc, "门派归属", "无门派")
        npc_location = npc.location.key if npc.location else "未知"
        npc_role = getattr(npc, "角色类型", "普通弟子")
        
        # 获取门派相关事件
        if npc_sect != "无门派":
            sect_events = world_state.get("sect_events", {}).get(npc_sect, [])
            relevant_events.extend(sect_events)
        
        # 获取区域相关事件
        regional_states = world_state.get("regional_states", {})
        if npc_location in regional_states:
            location_events = regional_states[npc_location].get("recent_events", [])
            relevant_events.extend(location_events)
        
        # 获取角色相关事件
        if npc_role in ["长老", "门派掌门"]:
            important_events = world_state.get("important_events", [])
            relevant_events.extend(important_events)
        
        return relevant_events[-10:]  # 返回最近10个相关事件
    
    def provide_cultivation_guidance(self, npc, player) -> Optional[str]:
        """提供修炼指导"""
        try:
            # 获取玩家修炼状态
            player_realm = getattr(player, "修为境界", "练气")
            player_element = getattr(player, "五行属性", "土")
            
            # 获取NPC指导能力
            npc_realm = getattr(npc, "修为境界", "练气")
            npc_role = getattr(npc, "角色类型", "普通弟子")
            
            # 检查是否有指导资格
            if not self._can_provide_guidance(npc_realm, npc_role, player_realm):
                return None
            
            # 生成指导内容
            guidance = self._generate_cultivation_guidance(npc, player, player_realm, player_element)
            
            # 记录指导历史
            self._record_guidance_provided(npc, player, guidance)
            
            return guidance
            
        except Exception as e:
            logger.log_err(f"NPC {npc.key} 提供修炼指导失败: {e}")
            return None
    
    def _can_provide_guidance(self, npc_realm: str, npc_role: str, player_realm: str) -> bool:
        """检查NPC是否有资格提供指导"""
        # 境界等级映射
        realm_levels = {
            "练气": 1, "筑基": 2, "金丹": 3, "元婴": 4, "化神": 5,
            "炼虚": 6, "合体": 7, "大乘": 8, "仙人": 9
        }
        
        npc_level = realm_levels.get(npc_realm, 1)
        player_level = realm_levels.get(player_realm, 1)
        
        # NPC境界必须高于玩家，或者是有教学权威的角色
        if npc_level > player_level:
            return True
        
        if npc_role in ["长老", "师兄", "师姐"] and npc_level >= player_level:
            return True
        
        return False
    
    def _generate_cultivation_guidance(self, npc, player, player_realm: str, player_element: str) -> str:
        """生成修炼指导内容"""
        guidance_templates = {
            "练气": [
                "练气期最重要的是打好基础，建议每日坚持吐纳练习。",
                "五行调和是练气期的关键，要注意{element}属性的平衡。",
                "心境平和，不可急于求成，稳扎稳打方能筑基成功。"
            ],
            "筑基": [
                "筑基期要注重灵力的精炼和压缩，为金丹期做准备。",
                "建议多练习{element}系功法，增强属性亲和力。",
                "此时可以开始学习一些基础的法术和技能。"
            ],
            "金丹": [
                "金丹期的关键是凝聚金丹，需要极大的耐心和毅力。",
                "要注意金丹的品质，宁缺毋滥，追求完美的金丹。",
                "可以开始接触一些高深的功法和秘术。"
            ]
        }
        
        templates = guidance_templates.get(player_realm, guidance_templates["练气"])
        guidance = templates[hash(player.key) % len(templates)]
        
        # 替换占位符
        guidance = guidance.format(element=player_element)
        
        return guidance
    
    def _record_guidance_provided(self, npc, player, guidance: str):
        """记录提供的指导"""
        if not hasattr(npc.db, "npc_state"):
            npc.db.npc_state = {}
        
        guidance_history = npc.db.npc_state.get("cultivation_guidance_given", [])
        guidance_history.append({
            "timestamp": time.time(),
            "player": player.key,
            "guidance": guidance,
            "player_realm": getattr(player, "修为境界", "练气")
        })
        
        # 限制历史记录数量
        if len(guidance_history) > 50:
            guidance_history = guidance_history[-25:]
        
        npc.db.npc_state["cultivation_guidance_given"] = guidance_history
    
    def handle_director_event(self, npc, event_type: str, event_data: Dict[str, Any]):
        """处理来自AI导演的事件"""
        try:
            if event_type == "world_event":
                self._handle_world_event(npc, event_data)
            elif event_type == "regional_event":
                self._handle_regional_event(npc, event_data)
            elif event_type == "individual_event":
                self._handle_individual_event(npc, event_data)
            elif event_type == "cultivation_opportunity":
                self._handle_cultivation_opportunity(npc, event_data)
            
        except Exception as e:
            logger.log_err(f"NPC {npc.key} 处理导演事件失败: {e}")
    
    def _handle_world_event(self, npc, event_data: Dict[str, Any]):
        """处理世界级事件"""
        # 更新NPC的世界知识
        world_knowledge = npc.db.npc_state.get("world_knowledge", {})
        world_events = world_knowledge.get("world_events", [])
        
        world_events.append({
            "timestamp": time.time(),
            "event": event_data,
            "npc_reaction": self._generate_npc_reaction(npc, event_data)
        })
        
        # 限制事件记录数量
        if len(world_events) > 20:
            world_events = world_events[-10:]
        
        world_knowledge["world_events"] = world_events
        npc.db.npc_state["world_knowledge"] = world_knowledge
    
    def _handle_regional_event(self, npc, event_data: Dict[str, Any]):
        """处理区域级事件"""
        # 如果事件影响NPC所在区域，更新NPC状态
        event_location = event_data.get("location")
        npc_location = npc.location.key if npc.location else None
        
        if event_location == npc_location:
            regional_knowledge = npc.db.npc_state.get("regional_knowledge", [])
            regional_knowledge.append({
                "timestamp": time.time(),
                "event": event_data,
                "direct_impact": True
            })
            
            if len(regional_knowledge) > 15:
                regional_knowledge = regional_knowledge[-10:]
            
            npc.db.npc_state["regional_knowledge"] = regional_knowledge
    
    def _handle_individual_event(self, npc, event_data: Dict[str, Any]):
        """处理个体级事件"""
        # 个体事件通常是针对特定玩家的，NPC需要了解并可能提供帮助
        target_player = event_data.get("target_player")
        
        if target_player:
            individual_knowledge = npc.db.npc_state.get("individual_knowledge", {})
            if target_player not in individual_knowledge:
                individual_knowledge[target_player] = []
            
            individual_knowledge[target_player].append({
                "timestamp": time.time(),
                "event": event_data,
                "potential_help": self._assess_help_potential(npc, event_data)
            })
            
            npc.db.npc_state["individual_knowledge"] = individual_knowledge
    
    def _handle_cultivation_opportunity(self, npc, event_data: Dict[str, Any]):
        """处理修炼机会事件"""
        # NPC可以向玩家推荐修炼机会
        opportunity_knowledge = npc.db.npc_state.get("cultivation_opportunities", [])
        opportunity_knowledge.append({
            "timestamp": time.time(),
            "opportunity": event_data,
            "recommendation_made": False
        })
        
        if len(opportunity_knowledge) > 10:
            opportunity_knowledge = opportunity_knowledge[-5:]
        
        npc.db.npc_state["cultivation_opportunities"] = opportunity_knowledge
    
    def _generate_npc_reaction(self, npc, event_data: Dict[str, Any]) -> str:
        """生成NPC对事件的反应"""
        npc_role = getattr(npc, "角色类型", "普通弟子")
        event_type = event_data.get("type", "unknown")
        
        reactions = {
            "长老": {
                "sect_conflict": "门派之间的冲突需要谨慎处理，和平为上。",
                "cultivation_breakthrough": "有弟子突破境界，实乃门派之幸。",
                "natural_disaster": "天灾降临，需要门派上下齐心协力。"
            },
            "师兄": {
                "sect_conflict": "师弟师妹们要小心，最近局势不太平。",
                "cultivation_breakthrough": "恭喜师弟/师妹突破境界！",
                "natural_disaster": "大家要注意安全，互相照应。"
            },
            "普通弟子": {
                "sect_conflict": "听说外面不太平，我们要更加努力修炼。",
                "cultivation_breakthrough": "真羡慕啊，我也要努力修炼！",
                "natural_disaster": "好可怕，希望长老们能保护我们。"
            }
        }
        
        role_reactions = reactions.get(npc_role, reactions["普通弟子"])
        return role_reactions.get(event_type, "这件事很有趣，值得关注。")
    
    def _assess_help_potential(self, npc, event_data: Dict[str, Any]) -> str:
        """评估NPC能提供的帮助"""
        npc_role = getattr(npc, "角色类型", "普通弟子")
        event_type = event_data.get("type", "unknown")
        
        help_potential = {
            "长老": {
                "cultivation_problem": "可以提供高深的修炼指导",
                "skill_learning": "可以传授高级功法",
                "life_guidance": "可以提供人生智慧"
            },
            "师兄": {
                "cultivation_problem": "可以分享修炼经验",
                "skill_learning": "可以教授基础技能",
                "daily_problem": "可以提供生活建议"
            },
            "师姐": {
                "cultivation_problem": "可以提供细致的修炼指导",
                "emotional_problem": "可以提供情感支持",
                "daily_problem": "可以提供贴心建议"
            }
        }
        
        role_help = help_potential.get(npc_role, {})
        return role_help.get(event_type, "可以提供一般性建议")
    
    def get_integration_status(self, npc) -> Dict[str, Any]:
        """获取NPC的集成状态"""
        status = {
            "registered_directors": [],
            "last_sync_time": 0,
            "event_subscriptions": [],
            "guidance_count": 0,
            "world_knowledge_items": 0
        }
        
        # 检查注册的导演
        if npc.tags.get("qiling_managed", category="ai_director"):
            status["registered_directors"].append("器灵导演")
        if npc.tags.get("diling_managed", category="ai_director"):
            status["registered_directors"].append("地灵导演")
        if npc.tags.get("tiandao_managed", category="ai_director"):
            status["registered_directors"].append("天道导演")
        
        # 获取同步时间
        world_knowledge = npc.db.npc_state.get("world_knowledge", {})
        status["last_sync_time"] = world_knowledge.get("last_update", 0)
        
        # 获取指导次数
        guidance_history = npc.db.npc_state.get("cultivation_guidance_given", [])
        status["guidance_count"] = len(guidance_history)
        
        # 获取世界知识数量
        world_events = world_knowledge.get("world_events", [])
        status["world_knowledge_items"] = len(world_events)
        
        return status
