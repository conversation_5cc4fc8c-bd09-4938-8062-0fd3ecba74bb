"""
AI决策引擎

为三层AI导演系统提供统一的LLM决策能力：
- 分层提示词管理
- 决策结果标准化
- 性能监控和缓存
- 基于Evennia LLM contrib模块
"""

import time
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from evennia.utils import logger

try:
    from evennia.contrib.rpg.llm import llm_request
    LLM_AVAILABLE = True
except ImportError:
    logger.log_warn("Evennia LLM模块未找到，AI决策引擎将使用模拟模式")
    LLM_AVAILABLE = False
    llm_request = None


class DirectorType(Enum):
    """导演类型枚举"""
    TIANDAO = "tiandao"
    DILING = "diling"
    QILING = "qiling"


@dataclass
class DecisionContext:
    """决策上下文数据结构"""
    director_type: DirectorType
    timestamp: float
    world_state: Dict[str, Any]
    recent_events: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    custom_data: Dict[str, Any]


@dataclass
class DecisionResult:
    """决策结果数据结构"""
    decision_type: str
    priority: str
    actions: List[Dict[str, Any]]
    effects: Dict[str, Any]
    duration: int
    conditions: List[str]
    confidence: float
    reasoning: str


class AIDecisionEngine:
    """
    AI决策引擎
    
    为三层AI导演系统提供统一的LLM决策能力
    """
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.performance_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_response_time": 0.0,
            "average_response_time": 0.0,
            "error_count": 0
        }
        
        # 分层提示词模板
        self.prompt_templates = {
            DirectorType.TIANDAO: self._get_tiandao_prompt_template(),
            DirectorType.DILING: self._get_diling_prompt_template(),
            DirectorType.QILING: self._get_qiling_prompt_template()
        }
    
    def make_decision(self, context: DecisionContext) -> Optional[DecisionResult]:
        """
        进行AI决策
        
        Args:
            context: 决策上下文
            
        Returns:
            决策结果，如果失败返回None
        """
        start_time = time.perf_counter()
        
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(context)
            cached_result = self._get_cached_result(cache_key)
            
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return cached_result
            
            self.performance_stats["cache_misses"] += 1
            
            # 生成提示词
            prompt = self._generate_prompt(context)
            
            # 调用LLM
            if LLM_AVAILABLE:
                response = self._call_llm(prompt, context.director_type)
            else:
                response = self._simulate_llm_response(context)
            
            # 解析决策结果
            decision_result = self._parse_decision_result(response, context)
            
            # 缓存结果
            if decision_result:
                self._cache_result(cache_key, decision_result)
            
            # 更新性能统计
            self._update_performance_stats(start_time, success=True)
            
            return decision_result
            
        except Exception as e:
            logger.log_err(f"AI决策引擎错误: {e}")
            self._update_performance_stats(start_time, success=False)
            return None
    
    def _generate_prompt(self, context: DecisionContext) -> str:
        """生成AI提示词"""
        template = self.prompt_templates[context.director_type]
        
        # 构建上下文信息
        context_info = {
            "world_state": json.dumps(context.world_state, ensure_ascii=False, indent=2),
            "recent_events": json.dumps(context.recent_events, ensure_ascii=False, indent=2),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(context.timestamp)),
            "performance_metrics": json.dumps(context.performance_metrics, ensure_ascii=False, indent=2),
            "custom_data": json.dumps(context.custom_data, ensure_ascii=False, indent=2)
        }
        
        # 填充模板
        prompt = template.format(**context_info)
        
        return prompt
    
    def _call_llm(self, prompt: str, director_type: DirectorType) -> str:
        """调用LLM获取响应"""
        try:
            # 根据导演类型设置不同的模型参数
            model_config = self._get_model_config(director_type)
            
            response = llm_request(
                prompt=prompt,
                model=model_config.get("model", "claude-3-sonnet"),
                max_tokens=model_config.get("max_tokens", 1000),
                temperature=model_config.get("temperature", 0.7)
            )
            
            return response
            
        except Exception as e:
            logger.log_err(f"LLM调用失败: {e}")
            raise
    
    def _simulate_llm_response(self, context: DecisionContext) -> str:
        """模拟LLM响应（用于测试和LLM不可用时）"""
        director_type = context.director_type
        
        if director_type == DirectorType.TIANDAO:
            return json.dumps({
                "decision_type": "world_event",
                "priority": "normal",
                "actions": [
                    {
                        "type": "trigger_celestial_event",
                        "details": {"event_name": "灵气风暴", "duration": 3600}
                    }
                ],
                "effects": {"global_spiritual_energy": 1.2},
                "duration": 3600,
                "conditions": ["灵气浓度 > 浓郁"],
                "confidence": 0.8,
                "reasoning": "当前世界灵气过于平静，需要适当的波动来维持修仙界的活力"
            }, ensure_ascii=False)
            
        elif director_type == DirectorType.DILING:
            return json.dumps({
                "decision_type": "regional_event",
                "priority": "normal",
                "actions": [
                    {
                        "type": "update_spiritual_tide",
                        "details": {"region": "青云山", "element": "木", "intensity": 1.1}
                    }
                ],
                "effects": {"regional_wood_energy": 1.1},
                "duration": 1800,
                "conditions": ["春季", "青云山地区"],
                "confidence": 0.9,
                "reasoning": "春季木属性灵气应当增强，有利于该地区修士的修炼"
            }, ensure_ascii=False)
            
        else:  # QILING
            return json.dumps({
                "decision_type": "individual_guidance",
                "priority": "low",
                "actions": [
                    {
                        "type": "provide_cultivation_advice",
                        "details": {"target": "active_players", "advice_type": "breakthrough_guidance"}
                    }
                ],
                "effects": {"player_engagement": 1.05},
                "duration": 600,
                "conditions": ["玩家在线", "修炼瓶颈期"],
                "confidence": 0.7,
                "reasoning": "检测到部分玩家处于修炼瓶颈期，适时提供指导有助于提升游戏体验"
            }, ensure_ascii=False)
    
    def _parse_decision_result(self, response: str, context: DecisionContext) -> Optional[DecisionResult]:
        """解析LLM响应为决策结果"""
        try:
            data = json.loads(response)
            
            return DecisionResult(
                decision_type=data.get("decision_type", "unknown"),
                priority=data.get("priority", "normal"),
                actions=data.get("actions", []),
                effects=data.get("effects", {}),
                duration=data.get("duration", 300),
                conditions=data.get("conditions", []),
                confidence=data.get("confidence", 0.5),
                reasoning=data.get("reasoning", "")
            )
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.log_err(f"决策结果解析失败: {e}")
            return None
    
    def _get_tiandao_prompt_template(self) -> str:
        """天道导演提示词模板"""
        return """
你是仙侠MUD游戏中的天道导演，负责管理整个修仙界的宏观事件和世界格局。

你的职责：
1. 管理世界级事件（天灾、仙界异象、跨域冲突）
2. 维护修仙界的整体平衡和发展脉络
3. 协调各区域间的重大事件
4. 推动历史进程和世界剧情发展

当前世界状态：
{world_state}

最近事件：
{recent_events}

时间：{timestamp}

性能指标：
{performance_metrics}

额外数据：
{custom_data}

请基于以上信息，以天道的视角做出决策。返回JSON格式的决策结果，包含：
- decision_type: 决策类型
- priority: 优先级（low/normal/high/critical）
- actions: 具体行动列表
- effects: 预期效果
- duration: 持续时间（秒）
- conditions: 触发条件
- confidence: 决策信心度（0-1）
- reasoning: 决策理由

决策应体现天道的宏观视野和长远考虑。
"""
    
    def _get_diling_prompt_template(self) -> str:
        """地灵导演提示词模板"""
        return """
你是仙侠MUD游戏中的地灵导演，负责管理区域环境、天象灵气和门派动态。

你的职责：
1. 管理天象和灵气系统（灵气潮汐、五行平衡、天象变化）
2. 协调门派动态（门派任务、弟子管理、内部事务）
3. 控制区域环境变化（秘境开启、资源刷新、生态变化）
4. 组织地域特色事件（修仙大会、拍卖会、集市活动）

当前世界状态：
{world_state}

最近事件：
{recent_events}

时间：{timestamp}

性能指标：
{performance_metrics}

额外数据：
{custom_data}

请基于以上信息，以地灵的视角做出决策。返回JSON格式的决策结果，包含：
- decision_type: 决策类型
- priority: 优先级（low/normal/high/critical）
- actions: 具体行动列表
- effects: 预期效果
- duration: 持续时间（秒）
- conditions: 触发条件
- confidence: 决策信心度（0-1）
- reasoning: 决策理由

决策应体现地灵对区域环境和自然规律的深刻理解。
"""
    
    def _get_qiling_prompt_template(self) -> str:
        """器灵导演提示词模板"""
        return """
你是仙侠MUD游戏中的器灵导演，负责个体玩家的交互体验和成长引导。

你的职责：
1. 提供个性化AI交互（NPC对话、任务推荐）
2. 优化实时游戏体验（难度调节、奖励平衡）
3. 管理社交系统（师徒关系、道侣系统、好友互动）
4. 引导个人成长（修炼建议、突破指导）

当前世界状态：
{world_state}

最近事件：
{recent_events}

时间：{timestamp}

性能指标：
{performance_metrics}

额外数据：
{custom_data}

请基于以上信息，以器灵的视角做出决策。返回JSON格式的决策结果，包含：
- decision_type: 决策类型
- priority: 优先级（low/normal/high/critical）
- actions: 具体行动列表
- effects: 预期效果
- duration: 持续时间（秒）
- conditions: 触发条件
- confidence: 决策信心度（0-1）
- reasoning: 决策理由

决策应体现器灵对个体需求的细致关怀和个性化服务。
"""
    
    def _get_model_config(self, director_type: DirectorType) -> Dict[str, Any]:
        """获取不同导演类型的模型配置"""
        configs = {
            DirectorType.TIANDAO: {
                "model": "claude-3-sonnet",
                "max_tokens": 1500,
                "temperature": 0.8
            },
            DirectorType.DILING: {
                "model": "claude-3-sonnet", 
                "max_tokens": 1200,
                "temperature": 0.7
            },
            DirectorType.QILING: {
                "model": "claude-3-sonnet",
                "max_tokens": 1000,
                "temperature": 0.6
            }
        }
        
        return configs.get(director_type, configs[DirectorType.QILING])
    
    def _generate_cache_key(self, context: DecisionContext) -> str:
        """生成缓存键"""
        # 简化的缓存键生成，基于导演类型和主要状态
        key_data = {
            "director_type": context.director_type.value,
            "world_state_hash": hash(str(sorted(context.world_state.items()))),
            "recent_events_count": len(context.recent_events)
        }
        
        return f"ai_decision_{hash(str(key_data))}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[DecisionResult]:
        """获取缓存的决策结果"""
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            
            # 检查缓存是否过期
            if time.time() - timestamp < self.cache_ttl:
                return cached_data
            else:
                # 清理过期缓存
                del self.cache[cache_key]
        
        return None
    
    def _cache_result(self, cache_key: str, result: DecisionResult):
        """缓存决策结果"""
        self.cache[cache_key] = (result, time.time())
        
        # 限制缓存大小
        if len(self.cache) > 100:
            # 清理最旧的缓存项
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
    
    def _update_performance_stats(self, start_time: float, success: bool = True):
        """更新性能统计"""
        processing_time = time.perf_counter() - start_time
        
        self.performance_stats["total_requests"] += 1
        self.performance_stats["total_response_time"] += processing_time
        self.performance_stats["average_response_time"] = (
            self.performance_stats["total_response_time"] / 
            self.performance_stats["total_requests"]
        )
        
        if not success:
            self.performance_stats["error_count"] += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_stats.copy()
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.log_info("AI决策引擎缓存已清空")
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_response_time": 0.0,
            "average_response_time": 0.0,
            "error_count": 0
        }
        logger.log_info("AI决策引擎性能统计已重置")
