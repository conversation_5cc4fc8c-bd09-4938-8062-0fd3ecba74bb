"""
高性能查询接口

为AI导演系统提供专门的高性能查询接口，
基于TagProperty实现10-100倍性能提升的数据查询能力。

核心功能：
- AI导演系统专用查询接口
- 批量查询优化
- 查询结果缓存
- 复合条件查询支持
"""

from typing import Dict, Any, List, Optional, Union, Tuple
from django.db.models import QuerySet
from evennia import search_object_by_tag, search_tag
from evennia.utils.logger import log_info, log_err
from evennia.utils.utils import make_iter

from .tag_property_system import TagPropertyQueryManager


class AIDirectorQueryInterface:
    """
    AI导演系统查询接口
    
    为天道、地灵、器灵三层AI导演系统提供高性能查询支持：
    - 天道导演：世界级事件查询（5分钟周期）
    - 地灵导演：区域级事件查询（1分钟周期）
    - 器灵导演：实时事件查询（10秒周期）
    """
    
    @staticmethod
    def get_world_state_summary() -> Dict[str, Any]:
        """
        获取世界状态摘要（天道导演用）
        
        Returns:
            Dict: 世界状态摘要
        """
        try:
            # 统计各境界修士数量
            realm_stats = {}
            for realm in TagPropertyQueryManager.REALM_LEVELS:
                count = len(TagPropertyQueryManager.find_characters_by_realm(realm) or [])
                realm_stats[realm] = count
            
            # 统计各门派势力
            sect_stats = {}
            for sect in TagPropertyQueryManager.SECT_TERRITORIES:
                characters = search_object_by_tag(key=sect, category="sect_territory")
                sect_stats[sect] = len(characters) if characters else 0
            
            # 统计危险区域
            danger_stats = {}
            for level in TagPropertyQueryManager.DANGER_LEVELS:
                rooms = TagPropertyQueryManager.find_rooms_by_danger_level(level)
                danger_stats[level] = len(rooms) if rooms else 0
            
            return {
                "realm_distribution": realm_stats,
                "sect_power": sect_stats,
                "danger_zones": danger_stats,
                "total_characters": sum(realm_stats.values()),
                "total_rooms": sum(danger_stats.values())
            }
        except Exception as e:
            log_err(f"Error getting world state summary: {e}")
            return {}
    
    @staticmethod
    def get_regional_activity(location_types: List[str] = None) -> Dict[str, Any]:
        """
        获取区域活动状态（地灵导演用）
        
        Args:
            location_types: 要查询的地点类型列表
            
        Returns:
            Dict: 区域活动状态
        """
        if location_types is None:
            location_types = TagPropertyQueryManager.LOCATION_TYPES
        
        try:
            regional_data = {}
            
            for loc_type in location_types:
                # 查找该类型的所有房间
                rooms = search_object_by_tag(key=loc_type, category="location_type")
                if not rooms:
                    continue
                
                # 统计房间中的角色
                characters_in_region = []
                for room in rooms:
                    if hasattr(room, 'contents'):
                        for obj in room.contents:
                            if hasattr(obj, '修为境界'):  # 是仙侠角色
                                characters_in_region.append(obj)
                
                # 分析角色分布
                realm_distribution = {}
                sect_distribution = {}
                
                for char in characters_in_region:
                    realm = getattr(char, '修为境界', '未知')
                    sect = getattr(char, '门派归属', '未知')
                    
                    realm_distribution[realm] = realm_distribution.get(realm, 0) + 1
                    sect_distribution[sect] = sect_distribution.get(sect, 0) + 1
                
                regional_data[loc_type] = {
                    "total_rooms": len(rooms),
                    "total_characters": len(characters_in_region),
                    "realm_distribution": realm_distribution,
                    "sect_distribution": sect_distribution
                }
            
            return regional_data
        except Exception as e:
            log_err(f"Error getting regional activity: {e}")
            return {}
    
    @staticmethod
    def get_realtime_events(time_window: int = 10) -> Dict[str, Any]:
        """
        获取实时事件状态（器灵导演用）
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            Dict: 实时事件状态
        """
        try:
            # 查找正在进行特殊状态的角色
            active_characters = {}
            special_states = ["闭关", "历练", "突破", "渡劫", "中毒", "受伤"]
            
            for state in special_states:
                characters = search_object_by_tag(key=state, category="special_states")
                if characters:
                    active_characters[state] = [
                        {
                            "name": char.key,
                            "realm": getattr(char, '修为境界', '未知'),
                            "location": str(char.location) if char.location else '未知'
                        }
                        for char in characters
                    ]
            
            # 查找高危区域的活动
            danger_zones = TagPropertyQueryManager.find_rooms_by_danger_level("极危")
            high_risk_activity = []
            
            if danger_zones:
                for room in danger_zones:
                    if hasattr(room, 'contents'):
                        characters_in_danger = [
                            obj for obj in room.contents 
                            if hasattr(obj, '修为境界')
                        ]
                        if characters_in_danger:
                            high_risk_activity.append({
                                "room": room.key,
                                "characters": [char.key for char in characters_in_danger],
                                "danger_level": "极危"
                            })
            
            return {
                "active_characters": active_characters,
                "high_risk_activity": high_risk_activity,
                "timestamp": time_window
            }
        except Exception as e:
            log_err(f"Error getting realtime events: {e}")
            return {}


class BatchQueryInterface:
    """
    批量查询接口
    
    为大规模数据查询提供优化的批量查询功能
    """
    
    @staticmethod
    def batch_character_query(filters: List[Dict[str, str]]) -> Dict[str, List]:
        """
        批量角色查询
        
        Args:
            filters: 查询条件列表
                格式: [{"category": "value"}, {"category2": "value2"}]
        
        Returns:
            Dict: 批量查询结果
        """
        results = {}
        
        for i, filter_dict in enumerate(filters):
            try:
                query_result = TagPropertyQueryManager.complex_query(filter_dict)
                results[f"query_{i}"] = list(query_result) if query_result else []
            except Exception as e:
                log_err(f"Error in batch query {i}: {e}")
                results[f"query_{i}"] = []
        
        return results
    
    @staticmethod
    def batch_room_query(spiritual_levels: List[str], danger_levels: List[str]) -> Dict[str, Any]:
        """
        批量房间查询
        
        Args:
            spiritual_levels: 灵气浓度等级列表
            danger_levels: 危险等级列表
            
        Returns:
            Dict: 批量查询结果
        """
        results = {
            "spiritual_energy": {},
            "danger_levels": {},
            "combined_stats": {}
        }
        
        # 按灵气浓度查询
        for level in spiritual_levels:
            try:
                rooms = TagPropertyQueryManager.find_rooms_by_spiritual_energy(level)
                results["spiritual_energy"][level] = list(rooms) if rooms else []
            except Exception as e:
                log_err(f"Error querying spiritual energy {level}: {e}")
                results["spiritual_energy"][level] = []
        
        # 按危险等级查询
        for level in danger_levels:
            try:
                rooms = TagPropertyQueryManager.find_rooms_by_danger_level(level)
                results["danger_levels"][level] = list(rooms) if rooms else []
            except Exception as e:
                log_err(f"Error querying danger level {level}: {e}")
                results["danger_levels"][level] = []
        
        # 组合统计
        for s_level in spiritual_levels:
            for d_level in danger_levels:
                try:
                    combined_query = TagPropertyQueryManager.complex_query({
                        "spiritual_energy": s_level,
                        "danger_level": d_level
                    })
                    key = f"{s_level}_{d_level}"
                    results["combined_stats"][key] = len(combined_query) if combined_query else 0
                except Exception as e:
                    log_err(f"Error in combined query {s_level}_{d_level}: {e}")
                    results["combined_stats"][f"{s_level}_{d_level}"] = 0
        
        return results


class CachedQueryInterface:
    """
    缓存查询接口
    
    为频繁查询提供缓存支持，减少数据库访问
    """
    
    _cache = {}
    _cache_timeout = 30  # 缓存30秒
    
    @classmethod
    def get_cached_statistics(cls) -> Dict[str, int]:
        """
        获取缓存的统计信息
        
        Returns:
            Dict: 统计信息
        """
        import time
        current_time = time.time()
        
        # 检查缓存是否有效
        if ("statistics" in cls._cache and 
            current_time - cls._cache["statistics"]["timestamp"] < cls._cache_timeout):
            return cls._cache["statistics"]["data"]
        
        # 重新获取统计信息
        stats = TagPropertyQueryManager.get_statistics()
        cls._cache["statistics"] = {
            "data": stats,
            "timestamp": current_time
        }
        
        return stats
    
    @classmethod
    def clear_cache(cls):
        """清空缓存"""
        cls._cache.clear()
        log_info("Query cache cleared")


# 导出主要类和函数
__all__ = [
    'AIDirectorQueryInterface',
    'BatchQueryInterface', 
    'CachedQueryInterface',
]
