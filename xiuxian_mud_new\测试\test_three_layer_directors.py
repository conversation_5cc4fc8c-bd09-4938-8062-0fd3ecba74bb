"""
三层AI导演系统集成测试

测试三层AI导演系统的完整功能：
- 导演系统初始化
- 导演间通信
- 协调机制
- 性能监控
- 命令系统
"""

import time
from evennia.utils.test_resources import EvenniaTest
from evennia.utils import search
from evennia.commands.default.tests import CommandTest

from scripts.ai_directors.director_initialization import initialize_director_system, get_director_system_status
from scripts.ai_directors.director_integration import DirectorLayer, get_director_integration
from commands.three_layer_director_commands import (
    CmdDirectorStatus, CmdDirectorControl, CmdDirectorMessages, 
    CmdDirectorCoordination, CmdDirectorDebug
)


class ThreeLayerDirectorTest(EvenniaTest):
    """三层AI导演系统测试"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        self.integration = None
        self.directors = {}
    
    def tearDown(self):
        """测试清理"""
        # 清理创建的脚本
        scripts_to_clean = [
            "director_integration_script",
            "tiandao_director_script", 
            "diling_director_script",
            "qiling_director_script",
            "director_initialization_script"
        ]
        
        for script_key in scripts_to_clean:
            scripts = search.search_script(script_key)
            for script in scripts:
                script.delete()
        
        super().tearDown()
    
    def test_director_system_initialization(self):
        """测试导演系统初始化"""
        print("\n=== 测试导演系统初始化 ===")
        
        # 初始化系统
        success = initialize_director_system()
        self.assertTrue(success, "导演系统初始化应该成功")
        
        # 等待初始化完成
        time.sleep(1)
        
        # 检查系统状态
        status = get_director_system_status()
        print(f"系统状态: {status}")
        
        self.assertTrue(status["integration_available"], "集成系统应该可用")
        
        # 检查所有导演都已注册
        for layer in ["tiandao", "diling", "qiling"]:
            self.assertTrue(
                status["directors_registered"].get(layer, False),
                f"{layer}导演应该已注册"
            )
    
    def test_director_integration_system(self):
        """测试导演集成系统"""
        print("\n=== 测试导演集成系统 ===")
        
        # 初始化系统
        initialize_director_system()
        time.sleep(1)
        
        # 获取集成系统
        integration = get_director_integration()
        self.assertIsNotNone(integration, "应该能获取到集成系统")
        
        # 测试性能指标
        metrics = integration.get_performance_metrics()
        self.assertIsInstance(metrics, dict, "性能指标应该是字典")
        self.assertIn("message_count", metrics, "应该包含消息计数")
        self.assertIn("coordination_count", metrics, "应该包含协调计数")
        
        print(f"性能指标: {metrics}")
    
    def test_inter_director_communication(self):
        """测试导演间通信"""
        print("\n=== 测试导演间通信 ===")
        
        # 初始化系统
        initialize_director_system()
        time.sleep(1)
        
        integration = get_director_integration()
        self.assertIsNotNone(integration)
        
        # 测试发送消息
        integration.send_message(
            DirectorLayer.TIANDAO,
            DirectorLayer.DILING,
            "test_message",
            {"content": "测试消息", "priority": "normal"},
            "normal"
        )
        
        # 检查消息队列
        queue = integration.db.communication_state["message_queue"]
        self.assertGreater(len(queue), 0, "消息队列应该有消息")
        
        last_message = queue[-1]
        self.assertEqual(last_message["from_layer"], "tiandao")
        self.assertEqual(last_message["to_layer"], "diling")
        self.assertEqual(last_message["message_type"], "test_message")
        
        print(f"发送的消息: {last_message}")
    
    def test_coordination_requests(self):
        """测试协调请求"""
        print("\n=== 测试协调请求 ===")
        
        # 初始化系统
        initialize_director_system()
        time.sleep(1)
        
        integration = get_director_integration()
        self.assertIsNotNone(integration)
        
        # 测试协调请求
        result = integration.request_coordination(
            DirectorLayer.TIANDAO,
            "world_event_coordination",
            {"event_type": "celestial_anomaly", "severity": "major"}
        )
        
        self.assertIsNotNone(result, "协调请求应该返回结果")
        
        # 检查协调请求队列
        requests = integration.db.communication_state["coordination_requests"]
        self.assertGreater(len(requests), 0, "协调请求队列应该有请求")
        
        last_request = requests[-1]
        self.assertEqual(last_request["requesting_layer"], "tiandao")
        self.assertEqual(last_request["coordination_type"], "world_event_coordination")
        
        print(f"协调请求: {last_request}")
    
    def test_shared_context_management(self):
        """测试共享上下文管理"""
        print("\n=== 测试共享上下文管理 ===")
        
        # 初始化系统
        initialize_director_system()
        time.sleep(1)
        
        integration = get_director_integration()
        self.assertIsNotNone(integration)
        
        # 测试更新共享上下文
        integration.update_shared_context(
            DirectorLayer.TIANDAO,
            "world_spiritual_energy",
            {"level": "high", "trend": "rising"}
        )
        
        integration.update_shared_context(
            DirectorLayer.DILING,
            "regional_activity",
            {"sect_count": 5, "conflict_level": "low"}
        )
        
        # 获取共享上下文
        context = integration.get_shared_context()
        self.assertIsInstance(context, dict, "共享上下文应该是字典")
        
        # 检查上下文内容
        self.assertIn("tiandao", context, "应该包含天道导演上下文")
        self.assertIn("diling", context, "应该包含地灵导演上下文")
        
        tiandao_context = context["tiandao"]
        self.assertIn("world_spiritual_energy", tiandao_context)
        
        print(f"共享上下文: {context}")


class ThreeLayerDirectorCommandTest(CommandTest):
    """三层AI导演命令测试"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        # 初始化导演系统
        initialize_director_system()
        time.sleep(1)
    
    def tearDown(self):
        """测试清理"""
        # 清理创建的脚本
        scripts_to_clean = [
            "director_integration_script",
            "tiandao_director_script", 
            "diling_director_script",
            "qiling_director_script",
            "director_initialization_script"
        ]
        
        for script_key in scripts_to_clean:
            scripts = search.search_script(script_key)
            for script in scripts:
                script.delete()
        
        super().tearDown()
    
    def test_director_status_command(self):
        """测试导演状态命令"""
        print("\n=== 测试导演状态命令 ===")
        
        # 测试查看所有导演状态
        self.call(CmdDirectorStatus(), "", "三层AI导演系统状态")
        
        # 测试查看特定导演状态
        self.call(CmdDirectorStatus(), "tiandao", "TIANDAO导演详细状态")
        self.call(CmdDirectorStatus(), "diling", "DILING导演详细状态")
        self.call(CmdDirectorStatus(), "qiling", "QILING导演详细状态")
    
    def test_director_control_command(self):
        """测试导演控制命令"""
        print("\n=== 测试导演控制命令 ===")
        
        # 测试启动导演
        self.call(CmdDirectorControl(), "start", "已启动")
        
        # 测试停止特定导演
        self.call(CmdDirectorControl(), "stop tiandao", "TIANDAO导演已停止")
        
        # 测试重启导演
        self.call(CmdDirectorControl(), "restart tiandao", "TIANDAO导演已重启")
        
        # 测试强制决策
        self.call(CmdDirectorControl(), "force_decision tiandao", "已强制TIANDAO导演做决策")
    
    def test_director_messages_command(self):
        """测试导演消息命令"""
        print("\n=== 测试导演消息命令 ===")
        
        # 发送测试消息
        self.call(CmdDirectorMessages(), "send tiandao diling test_message", "测试消息已发送")
        
        # 查看消息队列
        self.call(CmdDirectorMessages(), "list", "导演间消息队列")
        
        # 清空消息队列
        self.call(CmdDirectorMessages(), "clear", "消息队列已清空")
    
    def test_director_coordination_command(self):
        """测试导演协调命令"""
        print("\n=== 测试导演协调命令 ===")
        
        # 创建协调请求
        self.call(CmdDirectorCoordination(), "request tiandao world_event", "协调请求已创建")
        
        # 查看协调请求
        self.call(CmdDirectorCoordination(), "list", "导演协调请求")
        
        # 清空协调请求
        self.call(CmdDirectorCoordination(), "clear", "协调请求队列已清空")
    
    def test_director_debug_command(self):
        """测试导演调试命令"""
        print("\n=== 测试导演调试命令 ===")
        
        # 查看共享上下文
        self.call(CmdDirectorDebug(), "context", "导演共享上下文")
        
        # 查看性能详情
        self.call(CmdDirectorDebug(), "performance", "导演系统性能详情")
        
        # 重置导演状态
        self.call(CmdDirectorDebug(), "reset tiandao", "TIANDAO导演状态已重置")
        
        # 测试初始化命令
        self.call(CmdDirectorDebug(), "init", "导演系统")


def run_performance_test():
    """运行性能测试"""
    print("\n=== 运行性能测试 ===")
    
    # 初始化系统
    initialize_director_system()
    time.sleep(1)
    
    integration = get_director_integration()
    if not integration:
        print("集成系统未找到，跳过性能测试")
        return
    
    # 测试消息发送性能
    start_time = time.time()
    message_count = 100
    
    for i in range(message_count):
        integration.send_message(
            DirectorLayer.TIANDAO,
            DirectorLayer.DILING,
            "performance_test",
            {"index": i, "timestamp": time.time()},
            "normal"
        )
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"发送{message_count}条消息耗时: {duration:.3f}秒")
    print(f"平均每条消息: {duration/message_count*1000:.3f}毫秒")
    
    # 测试协调请求性能
    start_time = time.time()
    coordination_count = 50
    
    for i in range(coordination_count):
        integration.request_coordination(
            DirectorLayer.DILING,
            "performance_test",
            {"index": i, "timestamp": time.time()}
        )
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"发送{coordination_count}个协调请求耗时: {duration:.3f}秒")
    print(f"平均每个请求: {duration/coordination_count*1000:.3f}毫秒")
    
    # 显示最终性能指标
    metrics = integration.get_performance_metrics()
    print(f"最终性能指标: {metrics}")


if __name__ == "__main__":
    print("开始三层AI导演系统测试...")
    
    # 运行基础测试
    import unittest
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(ThreeLayerDirectorTest('test_director_system_initialization'))
    suite.addTest(ThreeLayerDirectorTest('test_director_integration_system'))
    suite.addTest(ThreeLayerDirectorTest('test_inter_director_communication'))
    suite.addTest(ThreeLayerDirectorTest('test_coordination_requests'))
    suite.addTest(ThreeLayerDirectorTest('test_shared_context_management'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 运行性能测试
    if result.wasSuccessful():
        run_performance_test()
    
    print("三层AI导演系统测试完成")
