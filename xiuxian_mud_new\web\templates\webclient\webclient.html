{% extends "webclient/base.html" %}
{% load static %}

{% comment %}
仙侠MUD Web客户端主界面模板

设计理念：
- 基于Evennia原生webclient扩展
- 仙侠主题视觉设计
- 响应式布局支持
- 组件化模板结构

技术特点：
- Django模板继承
- 静态资源管理
- WebSocket集成
- 移动端适配
{% endcomment %}

{% block title %}仙侠MUD - 修仙之路{% endblock %}

{% block head %}
    {{ block.super }}
    
    <!-- 仙侠主题样式 -->
    <link rel="stylesheet" type="text/css" href="{% static 'webclient/css/xiuxian.css' %}" />
    <link rel="stylesheet" type="text/css" href="{% static 'webclient/css/xiuxian-mobile.css' %}" />
    <link rel="stylesheet" type="text/css" href="{% static 'webclient/css/xiuxian-components.css' %}" />
    
    <!-- 移动端视口设置 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#1a1a2e">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="{% static 'webclient/js/xiuxian-client.js' %}" as="script">
    <link rel="preload" href="{% static 'webclient/js/xiuxian-mobile.js' %}" as="script">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="{% static 'webclient/manifest.json' %}">
    <link rel="apple-touch-icon" href="{% static 'webclient/images/icon-192.png' %}">
    
    <!-- 自定义CSS变量 -->
    <style>
        :root {
            --server-name: "{{ settings.SERVERNAME|default:'仙侠MUD' }}";
            --player-name: "{{ account.username|default:'访客' }}";
        }
    </style>
{% endblock %}

{% block body %}
<div id="webclient" class="webclient-main">
    <!-- 连接状态指示器 -->
    <div id="connection-status" class="connection-status disconnected">
        <span id="connection-text">连接中...</span>
    </div>
    
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobile-menu-btn" aria-label="打开菜单">
        ☰
    </button>
    
    <!-- 主界面标题栏 -->
    <header class="webclient-header">
        <h1>{{ settings.SERVERNAME|default:"仙侠MUD" }} - 修仙之路</h1>
        <div class="webclient-header__info">
            {% if account %}
                <span class="player-name">{{ account.username }}</span>
                {% if account.character %}
                    <span class="character-realm">{{ account.character.db.realm|default:"凡人" }}</span>
                {% endif %}
            {% else %}
                <span class="guest-info">访客模式</span>
            {% endif %}
        </div>
    </header>
    
    <!-- 主内容区域 -->
    <main class="webclient-content">
        <!-- 左侧边栏 (桌面端) -->
        <aside class="webclient-sidebar" id="webclient-sidebar">
            <!-- 角色状态面板 -->
            {% include "webclient/partials/character-status.html" %}
            
            <!-- 快捷操作面板 -->
            {% include "webclient/partials/quick-actions.html" %}
            
            <!-- AI导演面板 -->
            {% include "webclient/partials/ai-director-panel.html" %}
            
            <!-- 在线玩家面板 -->
            {% include "webclient/partials/online-players.html" %}
        </aside>
        
        <!-- 中央游戏区域 -->
        <section class="webclient-game-area">
            <!-- 消息显示区域 -->
            <div id="messagewindow" class="msg-container">
                <div id="messages" class="messages-content">
                    <!-- 欢迎消息 -->
                    <div class="msg msg-system fade-in">
                        <strong>🎭 欢迎来到{{ settings.SERVERNAME|default:"仙侠MUD" }}！</strong>
                    </div>
                    <div class="msg msg-system fade-in">
                        踏上修仙之路，追求长生不老的至高境界...
                    </div>
                    {% if not account %}
                    <div class="msg msg-channel fade-in">
                        请输入 <strong>connect 用户名 密码</strong> 登录，或 <strong>create 用户名 密码</strong> 创建新账号
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div id="inputcontainer" class="input-container">
                <div class="input-wrapper">
                    <input type="text" 
                           id="inputfield" 
                           class="input-field" 
                           placeholder="输入命令或对话..."
                           autocomplete="off"
                           autocapitalize="off"
                           spellcheck="false"
                           maxlength="1000">
                    <button id="input-send" class="btn input-send-btn" type="button">
                        发送
                    </button>
                </div>
                
                <!-- 命令提示 -->
                <div id="command-hints" class="command-hints" style="display: none;">
                    <div class="command-hints__content"></div>
                </div>
            </div>
        </section>
        
        <!-- 右侧边栏 (桌面端) -->
        <aside class="webclient-info-panel" id="webclient-info-panel">
            <!-- 聊天频道面板 -->
            {% include "webclient/partials/chat-channels.html" %}
            
            <!-- 修仙进度面板 -->
            {% include "webclient/partials/cultivation-progress.html" %}
        </aside>
    </main>
    
    <!-- 移动端快捷操作栏 -->
    <div class="mobile-quick-actions" id="mobile-quick-actions">
        <button class="btn" data-command="look">观察</button>
        <button class="btn" data-command="inventory">背包</button>
        <button class="btn" data-command="status">状态</button>
        <button class="btn" data-command="help">帮助</button>
    </div>
    
    <!-- 移动端侧边栏 -->
    <nav class="mobile-sidebar" id="mobile-sidebar">
        <div class="mobile-sidebar__header">
            <h3>游戏菜单</h3>
            <button class="mobile-sidebar__close" id="mobile-sidebar-close">×</button>
        </div>
        <div class="mobile-sidebar__content">
            {% include "webclient/partials/character-status.html" %}
            {% include "webclient/partials/quick-actions.html" %}
            {% include "webclient/partials/ai-director-panel.html" %}
        </div>
    </nav>
    
    <!-- 移动端侧边栏遮罩 -->
    <div class="mobile-sidebar-overlay" id="mobile-sidebar-overlay"></div>
</div>

<!-- 下拉刷新提示 -->
<div class="pull-to-refresh" id="pull-to-refresh">
    <span>下拉刷新消息</span>
</div>

<!-- 模态对话框容器 -->
<div id="modal-container" class="modal-container" style="display: none;">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title"></h4>
            <button class="modal-close">×</button>
        </div>
        <div class="modal-body"></div>
        <div class="modal-footer">
            <button class="btn btn-secondary modal-cancel">取消</button>
            <button class="btn modal-confirm">确认</button>
        </div>
    </div>
</div>

<!-- 加载指示器 -->
<div id="loading-indicator" class="loading-indicator" style="display: none;">
    <div class="loading-spinner"></div>
    <span class="loading-text">加载中...</span>
</div>

<!-- 通知容器 -->
<div id="notification-container" class="notification-container"></div>

{% endblock %}

{% block javascript %}
    {{ block.super }}
    
    <!-- 仙侠主题JavaScript -->
    <script src="{% static 'webclient/js/xiuxian-client.js' %}" defer></script>
    <script src="{% static 'webclient/js/xiuxian-mobile.js' %}" defer></script>
    
    <!-- 初始化脚本 -->
    <script>
        // 全局配置
        window.XiuxianMUD = {
            settings: {
                serverName: "{{ settings.SERVERNAME|default:'仙侠MUD' }}",
                playerName: "{{ account.username|default:'' }}",
                isAuthenticated: {% if account %}true{% else %}false{% endif %},
                enableAI: {% if settings.AI_ENABLED %}true{% else %}false{% endif %},
                enableMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
                debug: {% if settings.DEBUG %}true{% else %}false{% endif %}
            },
            
            // WebSocket配置
            websocket: {
                url: "{{ websocket_url|default:'ws://localhost:4001/ws' }}",
                protocols: ['evennia'],
                reconnectInterval: 3000,
                maxReconnectAttempts: 10
            },
            
            // UI配置
            ui: {
                theme: 'xiuxian',
                animations: true,
                sounds: false,
                notifications: true,
                autoScroll: true,
                commandHistory: true,
                maxHistorySize: 100
            }
        };
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (window.XiuxianClient) {
                window.XiuxianClient.init();
            }
            
            if (window.XiuxianMobile && window.XiuxianMUD.settings.enableMobile) {
                window.XiuxianMobile.init();
            }
            
            // 设置连接状态
            const connectionStatus = document.getElementById('connection-status');
            if (connectionStatus) {
                connectionStatus.textContent = '准备连接...';
                connectionStatus.className = 'connection-status connecting';
            }
        });
        
        // 页面卸载前清理
        window.addEventListener('beforeunload', function() {
            if (window.XiuxianClient) {
                window.XiuxianClient.cleanup();
            }
        });
        
        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('页面错误:', event.error);
            if (window.XiuxianClient && window.XiuxianClient.showNotification) {
                window.XiuxianClient.showNotification('发生错误，请刷新页面', 'error');
            }
        });
    </script>
{% endblock %}

{% block extra_css %}
    <style>
        /* 页面特定样式 */
        .webclient-content {
            display: grid;
            grid-template-columns: 300px 1fr 250px;
            grid-template-rows: 1fr;
            gap: 10px;
            height: calc(100vh - 60px);
            padding: 10px;
        }
        
        .webclient-game-area {
            display: flex;
            flex-direction: column;
            min-height: 0;
        }
        
        .webclient-sidebar,
        .webclient-info-panel {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        /* 移动端布局 */
        @media (max-width: 768px) {
            .webclient-content {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
                height: calc(100vh - 50px);
                padding: 5px;
            }
            
            .webclient-sidebar,
            .webclient-info-panel {
                display: none;
            }
            
            .webclient-game-area {
                grid-column: 1;
                grid-row: 1;
            }
            
            .mobile-quick-actions {
                grid-column: 1;
                grid-row: 2;
            }
        }
        
        /* 加载动画 */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--xiuxian-bg-secondary);
            border: 1px solid var(--xiuxian-border);
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 2000;
            box-shadow: var(--xiuxian-shadow-lg);
        }
        
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--xiuxian-border);
            border-top: 2px solid var(--xiuxian-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 通知样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1500;
            max-width: 300px;
        }
        
        .notification {
            background: var(--xiuxian-bg-secondary);
            border: 1px solid var(--xiuxian-border);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 10px;
            box-shadow: var(--xiuxian-shadow-md);
            animation: slideInRight 0.3s ease;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .notification.success {
            border-left: 4px solid var(--xiuxian-success);
        }
        
        .notification.error {
            border-left: 4px solid var(--xiuxian-error);
        }
        
        .notification.warning {
            border-left: 4px solid var(--xiuxian-warning);
        }
        
        .notification.info {
            border-left: 4px solid var(--xiuxian-info);
        }
    </style>
{% endblock %}
