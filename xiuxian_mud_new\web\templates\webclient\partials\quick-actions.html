{% comment %}
快捷操作组件模板

功能：
- 常用命令按钮化
- 快捷键支持
- 自定义命令
- 移动端友好

操作分类：
- 基础操作：观察、移动、背包等
- 修仙操作：修炼、炼丹、炼器等
- 社交操作：聊天、组队、门派等
- 系统操作：帮助、设置、退出等
{% endcomment %}

<div class="quick-actions" id="quick-actions">
    <div class="quick-actions__title">
        快捷操作
        <button class="quick-actions__toggle" id="quick-actions-toggle" title="展开/收起">
            <span class="toggle-icon">−</span>
        </button>
    </div>
    
    <div class="quick-actions__content" id="quick-actions-content">
        <!-- 基础操作 -->
        <div class="quick-actions__section">
            <div class="quick-actions__section-title">基础</div>
            <div class="quick-actions__grid">
                <button class="quick-actions__btn" data-command="look" data-key="l" title="观察周围环境 (L)">
                    <span class="quick-actions__btn-icon">👁️</span>
                    <span class="quick-actions__btn-text">观察</span>
                </button>
                
                <button class="quick-actions__btn" data-command="inventory" data-key="i" title="查看背包 (I)">
                    <span class="quick-actions__btn-icon">🎒</span>
                    <span class="quick-actions__btn-text">背包</span>
                </button>
                
                <button class="quick-actions__btn" data-command="status" data-key="st" title="查看状态">
                    <span class="quick-actions__btn-icon">📊</span>
                    <span class="quick-actions__btn-text">状态</span>
                </button>
                
                <button class="quick-actions__btn" data-command="who" data-key="w" title="在线玩家 (W)">
                    <span class="quick-actions__btn-icon">👥</span>
                    <span class="quick-actions__btn-text">在线</span>
                </button>
                
                <button class="quick-actions__btn" data-command="time" data-key="t" title="查看时间 (T)">
                    <span class="quick-actions__btn-icon">🕐</span>
                    <span class="quick-actions__btn-text">时间</span>
                </button>
                
                <button class="quick-actions__btn" data-command="score" data-key="sc" title="详细信息">
                    <span class="quick-actions__btn-icon">📋</span>
                    <span class="quick-actions__btn-text">详情</span>
                </button>
            </div>
        </div>
        
        <!-- 修仙操作 -->
        <div class="quick-actions__section">
            <div class="quick-actions__section-title">修仙</div>
            <div class="quick-actions__grid">
                <button class="quick-actions__btn" data-command="meditate" data-key="med" title="打坐修炼">
                    <span class="quick-actions__btn-icon">🧘</span>
                    <span class="quick-actions__btn-text">修炼</span>
                </button>
                
                <button class="quick-actions__btn" data-command="alchemy" data-key="alc" title="炼丹">
                    <span class="quick-actions__btn-icon">⚗️</span>
                    <span class="quick-actions__btn-text">炼丹</span>
                </button>
                
                <button class="quick-actions__btn" data-command="forge" data-key="for" title="炼器">
                    <span class="quick-actions__btn-icon">🔨</span>
                    <span class="quick-actions__btn-text">炼器</span>
                </button>
                
                <button class="quick-actions__btn" data-command="skills" data-key="sk" title="查看技能">
                    <span class="quick-actions__btn-icon">📜</span>
                    <span class="quick-actions__btn-text">技能</span>
                </button>
                
                <button class="quick-actions__btn" data-command="spells" data-key="sp" title="法术列表">
                    <span class="quick-actions__btn-icon">✨</span>
                    <span class="quick-actions__btn-text">法术</span>
                </button>
                
                <button class="quick-actions__btn" data-command="breakthrough" data-key="bt" title="突破境界">
                    <span class="quick-actions__btn-icon">⚡</span>
                    <span class="quick-actions__btn-text">突破</span>
                </button>
            </div>
        </div>
        
        <!-- 社交操作 -->
        <div class="quick-actions__section">
            <div class="quick-actions__section-title">社交</div>
            <div class="quick-actions__grid">
                <button class="quick-actions__btn" data-command="chat" data-key="'" title="聊天频道 (')">
                    <span class="quick-actions__btn-icon">💬</span>
                    <span class="quick-actions__btn-text">聊天</span>
                </button>
                
                <button class="quick-actions__btn" data-command="tell" data-key="tel" title="私聊">
                    <span class="quick-actions__btn-icon">📞</span>
                    <span class="quick-actions__btn-text">私聊</span>
                </button>
                
                <button class="quick-actions__btn" data-command="sect" data-key="sect" title="门派频道">
                    <span class="quick-actions__btn-icon">🏛️</span>
                    <span class="quick-actions__btn-text">门派</span>
                </button>
                
                <button class="quick-actions__btn" data-command="team" data-key="team" title="队伍管理">
                    <span class="quick-actions__btn-icon">👫</span>
                    <span class="quick-actions__btn-text">队伍</span>
                </button>
                
                <button class="quick-actions__btn" data-command="friend" data-key="fr" title="好友列表">
                    <span class="quick-actions__btn-icon">❤️</span>
                    <span class="quick-actions__btn-text">好友</span>
                </button>
                
                <button class="quick-actions__btn" data-command="mail" data-key="mail" title="查看邮件">
                    <span class="quick-actions__btn-icon">📧</span>
                    <span class="quick-actions__btn-text">邮件</span>
                </button>
            </div>
        </div>
        
        <!-- 系统操作 -->
        <div class="quick-actions__section">
            <div class="quick-actions__section-title">系统</div>
            <div class="quick-actions__grid">
                <button class="quick-actions__btn" data-command="help" data-key="h" title="帮助信息 (H)">
                    <span class="quick-actions__btn-icon">❓</span>
                    <span class="quick-actions__btn-text">帮助</span>
                </button>
                
                <button class="quick-actions__btn" data-command="commands" data-key="cmd" title="命令列表">
                    <span class="quick-actions__btn-icon">📝</span>
                    <span class="quick-actions__btn-text">命令</span>
                </button>
                
                <button class="quick-actions__btn" data-command="settings" data-key="set" title="设置">
                    <span class="quick-actions__btn-icon">⚙️</span>
                    <span class="quick-actions__btn-text">设置</span>
                </button>
                
                <button class="quick-actions__btn" data-command="save" data-key="save" title="保存进度">
                    <span class="quick-actions__btn-icon">💾</span>
                    <span class="quick-actions__btn-text">保存</span>
                </button>
                
                <button class="quick-actions__btn" data-command="quit" data-key="q" title="退出游戏 (Q)">
                    <span class="quick-actions__btn-icon">🚪</span>
                    <span class="quick-actions__btn-text">退出</span>
                </button>
                
                <button class="quick-actions__btn" data-command="ai_director" data-key="ai" title="AI导演">
                    <span class="quick-actions__btn-icon">🎭</span>
                    <span class="quick-actions__btn-text">AI导演</span>
                </button>
            </div>
        </div>
        
        <!-- 自定义操作 -->
        <div class="quick-actions__section quick-actions__section--custom" id="custom-actions" style="display: none;">
            <div class="quick-actions__section-title">
                自定义
                <button class="quick-actions__add-btn" id="add-custom-action" title="添加自定义操作">+</button>
            </div>
            <div class="quick-actions__grid" id="custom-actions-grid">
                <!-- 自定义按钮将动态添加到这里 -->
            </div>
        </div>
    </div>
</div>

<!-- 自定义操作对话框 -->
<div class="custom-action-dialog" id="custom-action-dialog" style="display: none;">
    <div class="custom-action-dialog__backdrop"></div>
    <div class="custom-action-dialog__content">
        <div class="custom-action-dialog__header">
            <h4>添加自定义操作</h4>
            <button class="custom-action-dialog__close">×</button>
        </div>
        <div class="custom-action-dialog__body">
            <div class="form-group">
                <label for="custom-action-name">操作名称：</label>
                <input type="text" id="custom-action-name" placeholder="例如：回城" maxlength="10">
            </div>
            <div class="form-group">
                <label for="custom-action-command">命令：</label>
                <input type="text" id="custom-action-command" placeholder="例如：recall" maxlength="50">
            </div>
            <div class="form-group">
                <label for="custom-action-icon">图标：</label>
                <input type="text" id="custom-action-icon" placeholder="例如：🏠" maxlength="2">
            </div>
            <div class="form-group">
                <label for="custom-action-key">快捷键：</label>
                <input type="text" id="custom-action-key" placeholder="例如：rec" maxlength="10">
            </div>
        </div>
        <div class="custom-action-dialog__footer">
            <button class="btn btn-secondary" id="custom-action-cancel">取消</button>
            <button class="btn" id="custom-action-save">保存</button>
        </div>
    </div>
</div>

<script>
(function() {
    'use strict';
    
    // 快捷操作管理器
    window.QuickActions = {
        // 初始化
        init: function() {
            this.bindEvents();
            this.loadCustomActions();
            this.setupKeyboardShortcuts();
        },
        
        // 绑定事件
        bindEvents: function() {
            const self = this;
            
            // 快捷按钮点击事件
            document.addEventListener('click', function(e) {
                if (e.target.closest('.quick-actions__btn')) {
                    const btn = e.target.closest('.quick-actions__btn');
                    const command = btn.dataset.command;
                    if (command) {
                        self.executeCommand(command);
                    }
                }
            });
            
            // 展开/收起切换
            const toggleBtn = document.getElementById('quick-actions-toggle');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    self.togglePanel();
                });
            }
            
            // 添加自定义操作
            const addBtn = document.getElementById('add-custom-action');
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    self.showCustomActionDialog();
                });
            }
            
            // 自定义操作对话框事件
            this.bindCustomActionDialog();
        },
        
        // 执行命令
        executeCommand: function(command) {
            const inputField = document.getElementById('inputfield');
            if (inputField) {
                // 特殊命令处理
                if (command === 'chat') {
                    inputField.value = "'";
                    inputField.focus();
                    return;
                } else if (command === 'tell') {
                    inputField.value = 'tell ';
                    inputField.focus();
                    return;
                }
                
                // 普通命令直接发送
                inputField.value = command;
                
                // 触发发送事件
                const sendEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13
                });
                inputField.dispatchEvent(sendEvent);
                
                // 清空输入框
                setTimeout(function() {
                    inputField.value = '';
                }, 100);
            }
        },
        
        // 切换面板展开状态
        togglePanel: function() {
            const content = document.getElementById('quick-actions-content');
            const toggleIcon = document.querySelector('.toggle-icon');
            
            if (content && toggleIcon) {
                const isCollapsed = content.style.display === 'none';
                content.style.display = isCollapsed ? 'block' : 'none';
                toggleIcon.textContent = isCollapsed ? '−' : '+';
                
                // 保存状态
                localStorage.setItem('quickActionsCollapsed', !isCollapsed);
            }
        },
        
        // 设置键盘快捷键
        setupKeyboardShortcuts: function() {
            const self = this;
            
            document.addEventListener('keydown', function(e) {
                // 如果输入框有焦点，不处理快捷键
                if (document.activeElement.tagName === 'INPUT' || 
                    document.activeElement.tagName === 'TEXTAREA') {
                    return;
                }
                
                // 检查是否按下了Ctrl或Alt键
                if (e.ctrlKey || e.altKey) {
                    const key = e.key.toLowerCase();
                    const btn = document.querySelector(`[data-key="${key}"]`);
                    if (btn) {
                        e.preventDefault();
                        const command = btn.dataset.command;
                        if (command) {
                            self.executeCommand(command);
                            
                            // 添加视觉反馈
                            btn.classList.add('quick-actions__btn--activated');
                            setTimeout(function() {
                                btn.classList.remove('quick-actions__btn--activated');
                            }, 200);
                        }
                    }
                }
            });
        },
        
        // 加载自定义操作
        loadCustomActions: function() {
            const customActions = JSON.parse(localStorage.getItem('customActions') || '[]');
            const customSection = document.getElementById('custom-actions');
            const customGrid = document.getElementById('custom-actions-grid');
            
            if (customActions.length > 0) {
                customSection.style.display = 'block';
                customGrid.innerHTML = '';
                
                customActions.forEach(function(action, index) {
                    const btn = document.createElement('button');
                    btn.className = 'quick-actions__btn quick-actions__btn--custom';
                    btn.dataset.command = action.command;
                    btn.dataset.key = action.key;
                    btn.title = action.command + (action.key ? ' (' + action.key + ')' : '');
                    btn.innerHTML = `
                        <span class="quick-actions__btn-icon">${action.icon}</span>
                        <span class="quick-actions__btn-text">${action.name}</span>
                        <button class="quick-actions__btn-remove" data-index="${index}">×</button>
                    `;
                    customGrid.appendChild(btn);
                });
                
                // 绑定删除事件
                customGrid.addEventListener('click', function(e) {
                    if (e.target.classList.contains('quick-actions__btn-remove')) {
                        e.stopPropagation();
                        const index = parseInt(e.target.dataset.index);
                        QuickActions.removeCustomAction(index);
                    }
                });
            }
        },
        
        // 显示自定义操作对话框
        showCustomActionDialog: function() {
            const dialog = document.getElementById('custom-action-dialog');
            if (dialog) {
                dialog.style.display = 'block';
                document.getElementById('custom-action-name').focus();
            }
        },
        
        // 绑定自定义操作对话框事件
        bindCustomActionDialog: function() {
            const self = this;
            const dialog = document.getElementById('custom-action-dialog');
            
            if (!dialog) return;
            
            // 关闭对话框
            const closeBtn = dialog.querySelector('.custom-action-dialog__close');
            const cancelBtn = document.getElementById('custom-action-cancel');
            const backdrop = dialog.querySelector('.custom-action-dialog__backdrop');
            
            [closeBtn, cancelBtn, backdrop].forEach(function(element) {
                if (element) {
                    element.addEventListener('click', function() {
                        dialog.style.display = 'none';
                        self.clearCustomActionForm();
                    });
                }
            });
            
            // 保存自定义操作
            const saveBtn = document.getElementById('custom-action-save');
            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    self.saveCustomAction();
                });
            }
            
            // 回车键保存
            dialog.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    self.saveCustomAction();
                }
            });
        },
        
        // 保存自定义操作
        saveCustomAction: function() {
            const name = document.getElementById('custom-action-name').value.trim();
            const command = document.getElementById('custom-action-command').value.trim();
            const icon = document.getElementById('custom-action-icon').value.trim() || '⚡';
            const key = document.getElementById('custom-action-key').value.trim();
            
            if (!name || !command) {
                alert('请填写操作名称和命令');
                return;
            }
            
            const customActions = JSON.parse(localStorage.getItem('customActions') || '[]');
            customActions.push({
                name: name,
                command: command,
                icon: icon,
                key: key
            });
            
            localStorage.setItem('customActions', JSON.stringify(customActions));
            
            // 关闭对话框并重新加载
            document.getElementById('custom-action-dialog').style.display = 'none';
            this.clearCustomActionForm();
            this.loadCustomActions();
        },
        
        // 删除自定义操作
        removeCustomAction: function(index) {
            if (confirm('确定要删除这个自定义操作吗？')) {
                const customActions = JSON.parse(localStorage.getItem('customActions') || '[]');
                customActions.splice(index, 1);
                localStorage.setItem('customActions', JSON.stringify(customActions));
                this.loadCustomActions();
            }
        },
        
        // 清空自定义操作表单
        clearCustomActionForm: function() {
            document.getElementById('custom-action-name').value = '';
            document.getElementById('custom-action-command').value = '';
            document.getElementById('custom-action-icon').value = '';
            document.getElementById('custom-action-key').value = '';
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            QuickActions.init();
        });
    } else {
        QuickActions.init();
    }
})();
</script>

<style>
/* 快捷操作组件特定样式 */
.quick-actions__section {
    margin-bottom: 16px;
}

.quick-actions__section-title {
    font-size: 0.9em;
    font-weight: bold;
    color: var(--xiuxian-text-secondary);
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--xiuxian-divider);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-actions__toggle {
    background: none;
    border: none;
    color: var(--xiuxian-text-secondary);
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    transition: var(--xiuxian-transition-fast);
}

.quick-actions__toggle:hover {
    background: var(--xiuxian-border);
    color: var(--xiuxian-text-primary);
}

.quick-actions__add-btn {
    background: var(--xiuxian-primary);
    color: var(--xiuxian-bg-primary);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--xiuxian-transition-fast);
}

.quick-actions__add-btn:hover {
    background: var(--xiuxian-secondary);
    transform: scale(1.1);
}

/* 自定义按钮样式 */
.quick-actions__btn--custom {
    position: relative;
}

.quick-actions__btn-remove {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--xiuxian-error);
    color: white;
    border: none;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    cursor: pointer;
    display: none;
}

.quick-actions__btn--custom:hover .quick-actions__btn-remove {
    display: block;
}

/* 激活状态 */
.quick-actions__btn--activated {
    transform: scale(0.95);
    background: var(--xiuxian-secondary) !important;
    box-shadow: var(--xiuxian-shadow-glow) !important;
}

/* 自定义操作对话框 */
.custom-action-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
}

.custom-action-dialog__backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.custom-action-dialog__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--xiuxian-bg-primary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 12px;
    box-shadow: var(--xiuxian-shadow-lg);
    width: 90%;
    max-width: 400px;
}

.custom-action-dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--xiuxian-divider);
}

.custom-action-dialog__header h4 {
    margin: 0;
    color: var(--xiuxian-primary);
}

.custom-action-dialog__close {
    background: none;
    border: none;
    color: var(--xiuxian-text-secondary);
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-action-dialog__body {
    padding: 20px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    color: var(--xiuxian-text-secondary);
    font-size: 0.9em;
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    background: var(--xiuxian-bg-tertiary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 6px;
    color: var(--xiuxian-text-primary);
    font-size: 14px;
}

.form-group input:focus {
    border-color: var(--xiuxian-primary);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
    outline: none;
}

.custom-action-dialog__footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid var(--xiuxian-divider);
}
</style>
