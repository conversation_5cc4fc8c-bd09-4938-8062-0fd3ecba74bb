"""
TagProperty性能测试套件

测试TagProperty相对于AttributeProperty的性能提升，
验证10-100倍性能提升的声明。

测试内容：
- 单条件查询性能对比
- 复合条件查询性能对比
- 批量查询性能对比
- 内存使用对比
"""

import time
import random
import statistics
from typing import List, Dict, Any
from unittest.mock import Mock, patch

from evennia.utils.test_resources import BaseEvenniaTest
from evennia.utils.create import create_object
from evennia import search_object_by_tag

from ..systems.tag_property_system import TagPropertyQueryManager
from ..systems.query_interfaces import AIDirectorQueryInterface, BatchQueryInterface
from ..typeclasses.characters import XianxiaCharacter
from ..typeclasses.rooms import XianxiaRoom
from ..typeclasses.objects import XianxiaObject


class TagPropertyPerformanceTest(BaseEvenniaTest):
    """TagProperty性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        self.test_characters = []
        self.test_rooms = []
        self.test_objects = []
        
        # 创建测试数据
        self.create_test_data()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建100个测试角色
        realms = TagPropertyQueryManager.REALM_LEVELS
        sects = TagPropertyQueryManager.SECT_TERRITORIES
        elements = TagPropertyQueryManager.ELEMENTAL_TYPES
        
        for i in range(100):
            char = create_object(
                XianxiaCharacter,
                key=f"test_char_{i}",
                location=None
            )
            
            # 随机设置属性
            char.修为境界 = random.choice(realms)
            char.门派归属 = random.choice(sects)
            char.五行属性 = random.choice(elements)
            
            self.test_characters.append(char)
        
        # 创建50个测试房间
        spiritual_levels = TagPropertyQueryManager.SPIRITUAL_ENERGY_LEVELS
        danger_levels = TagPropertyQueryManager.DANGER_LEVELS
        location_types = TagPropertyQueryManager.LOCATION_TYPES
        
        for i in range(50):
            room = create_object(
                XianxiaRoom,
                key=f"test_room_{i}",
                location=None
            )
            
            # 随机设置属性
            room.灵气浓度 = random.choice(spiritual_levels)
            room.危险等级 = random.choice(danger_levels)
            room.地点类型 = random.choice(location_types)
            
            self.test_rooms.append(room)
        
        # 创建200个测试物品
        item_types = TagPropertyQueryManager.ITEM_TYPES
        quality_levels = TagPropertyQueryManager.QUALITY_LEVELS
        
        for i in range(200):
            obj = create_object(
                XianxiaObject,
                key=f"test_obj_{i}",
                location=None
            )
            
            # 随机设置属性
            obj.物品类型 = random.choice(item_types)
            obj.品质等级 = random.choice(quality_levels)
            obj.五行属性 = random.choice(elements)
            
            self.test_objects.append(obj)
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试数据
        for char in self.test_characters:
            char.delete()
        for room in self.test_rooms:
            room.delete()
        for obj in self.test_objects:
            obj.delete()
        
        super().tearDown()
    
    def benchmark_query(self, query_func, iterations: int = 50) -> Dict[str, float]:
        """
        基准测试查询函数
        
        Args:
            query_func: 要测试的查询函数
            iterations: 迭代次数
            
        Returns:
            Dict: 性能统计
        """
        times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            result = query_func()
            end_time = time.perf_counter()
            
            times.append(end_time - start_time)
        
        return {
            "mean": statistics.mean(times),
            "median": statistics.median(times),
            "min": min(times),
            "max": max(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0,
            "total_time": sum(times),
            "iterations": iterations
        }
    
    def test_single_condition_query_performance(self):
        """测试单条件查询性能"""
        print("\n=== 单条件查询性能测试 ===")
        
        # TagProperty查询
        def tag_query():
            return TagPropertyQueryManager.find_characters_by_realm("金丹")
        
        tag_stats = self.benchmark_query(tag_query)
        
        print(f"TagProperty查询统计:")
        print(f"  平均时间: {tag_stats['mean']:.6f}秒")
        print(f"  中位数时间: {tag_stats['median']:.6f}秒")
        print(f"  最小时间: {tag_stats['min']:.6f}秒")
        print(f"  最大时间: {tag_stats['max']:.6f}秒")
        print(f"  标准差: {tag_stats['std_dev']:.6f}秒")
        
        # 模拟AttributeProperty查询（较慢的方式）
        def attribute_query():
            # 模拟遍历所有角色检查属性的方式
            results = []
            for char in self.test_characters:
                if hasattr(char, '修为境界') and char.修为境界 == "金丹":
                    results.append(char)
            return results
        
        attr_stats = self.benchmark_query(attribute_query)
        
        print(f"\n模拟AttributeProperty查询统计:")
        print(f"  平均时间: {attr_stats['mean']:.6f}秒")
        print(f"  中位数时间: {attr_stats['median']:.6f}秒")
        
        # 计算性能提升
        if attr_stats['mean'] > 0:
            improvement = attr_stats['mean'] / tag_stats['mean']
            print(f"\n性能提升: {improvement:.2f}倍")
            
            # 验证性能提升是否达到预期
            self.assertGreater(improvement, 2.0, "TagProperty性能提升应该至少2倍")
    
    def test_complex_query_performance(self):
        """测试复合条件查询性能"""
        print("\n=== 复合条件查询性能测试 ===")
        
        # TagProperty复合查询
        def tag_complex_query():
            return TagPropertyQueryManager.complex_query({
                "境界等级": "金丹",
                "sect_territory": "青云门",
                "elemental_type": "火"
            })
        
        tag_stats = self.benchmark_query(tag_complex_query)
        
        print(f"TagProperty复合查询统计:")
        print(f"  平均时间: {tag_stats['mean']:.6f}秒")
        print(f"  中位数时间: {tag_stats['median']:.6f}秒")
        
        # 模拟传统复合查询
        def traditional_complex_query():
            results = []
            for char in self.test_characters:
                if (hasattr(char, '修为境界') and char.修为境界 == "金丹" and
                    hasattr(char, '门派归属') and char.门派归属 == "青云门" and
                    hasattr(char, '五行属性') and char.五行属性 == "火"):
                    results.append(char)
            return results
        
        trad_stats = self.benchmark_query(traditional_complex_query)
        
        print(f"\n传统复合查询统计:")
        print(f"  平均时间: {trad_stats['mean']:.6f}秒")
        
        # 计算性能提升
        if trad_stats['mean'] > 0:
            improvement = trad_stats['mean'] / tag_stats['mean']
            print(f"\n复合查询性能提升: {improvement:.2f}倍")
            
            self.assertGreater(improvement, 3.0, "复合查询性能提升应该至少3倍")
    
    def test_batch_query_performance(self):
        """测试批量查询性能"""
        print("\n=== 批量查询性能测试 ===")
        
        # 准备批量查询条件
        batch_filters = [
            {"境界等级": "练气"},
            {"境界等级": "筑基"},
            {"境界等级": "金丹"},
            {"sect_territory": "青云门"},
            {"sect_territory": "天音寺"},
            {"elemental_type": "火"},
            {"elemental_type": "水"},
        ]
        
        # TagProperty批量查询
        def tag_batch_query():
            return BatchQueryInterface.batch_character_query(batch_filters)
        
        tag_stats = self.benchmark_query(tag_batch_query, iterations=20)
        
        print(f"TagProperty批量查询统计:")
        print(f"  平均时间: {tag_stats['mean']:.6f}秒")
        print(f"  处理{len(batch_filters)}个查询条件")
        print(f"  平均每个查询: {tag_stats['mean']/len(batch_filters):.6f}秒")
        
        # 验证批量查询效率
        single_query_time = tag_stats['mean'] / len(batch_filters)
        self.assertLess(single_query_time, 0.001, "批量查询中单个查询应该很快")
    
    def test_ai_director_query_performance(self):
        """测试AI导演系统查询性能"""
        print("\n=== AI导演系统查询性能测试 ===")
        
        # 天道导演查询
        def tiandao_query():
            return AIDirectorQueryInterface.get_world_state_summary()
        
        tiandao_stats = self.benchmark_query(tiandao_query, iterations=10)
        
        print(f"天道导演查询统计:")
        print(f"  平均时间: {tiandao_stats['mean']:.6f}秒")
        
        # 地灵导演查询
        def diling_query():
            return AIDirectorQueryInterface.get_regional_activity()
        
        diling_stats = self.benchmark_query(diling_query, iterations=20)
        
        print(f"地灵导演查询统计:")
        print(f"  平均时间: {diling_stats['mean']:.6f}秒")
        
        # 器灵导演查询
        def qiling_query():
            return AIDirectorQueryInterface.get_realtime_events()
        
        qiling_stats = self.benchmark_query(qiling_query, iterations=50)
        
        print(f"器灵导演查询统计:")
        print(f"  平均时间: {qiling_stats['mean']:.6f}秒")
        
        # 验证查询时间符合AI导演系统要求
        self.assertLess(tiandao_stats['mean'], 1.0, "天道导演查询应在1秒内完成")
        self.assertLess(diling_stats['mean'], 0.5, "地灵导演查询应在0.5秒内完成")
        self.assertLess(qiling_stats['mean'], 0.1, "器灵导演查询应在0.1秒内完成")
    
    def test_memory_usage_comparison(self):
        """测试内存使用对比"""
        print("\n=== 内存使用对比测试 ===")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 测试前内存使用
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量TagProperty查询
        for _ in range(100):
            TagPropertyQueryManager.find_characters_by_realm("金丹")
            TagPropertyQueryManager.find_rooms_by_spiritual_energy("浓郁")
            TagPropertyQueryManager.find_objects_by_quality("灵品")
        
        # 测试后内存使用
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before
        
        print(f"内存使用情况:")
        print(f"  测试前: {memory_before:.2f} MB")
        print(f"  测试后: {memory_after:.2f} MB")
        print(f"  增加: {memory_increase:.2f} MB")
        
        # 验证内存使用合理
        self.assertLess(memory_increase, 50, "内存增加应该控制在50MB以内")
    
    def test_query_accuracy(self):
        """测试查询准确性"""
        print("\n=== 查询准确性测试 ===")
        
        # 测试单条件查询准确性
        jindan_chars = TagPropertyQueryManager.find_characters_by_realm("金丹")
        
        # 验证结果
        if jindan_chars:
            for char in jindan_chars:
                self.assertEqual(char.修为境界, "金丹", "查询结果应该都是金丹境界")
        
        # 测试复合查询准确性
        complex_result = TagPropertyQueryManager.complex_query({
            "境界等级": "筑基",
            "sect_territory": "青云门"
        })
        
        if complex_result:
            for char in complex_result:
                self.assertEqual(char.修为境界, "筑基", "复合查询结果境界应该正确")
                self.assertEqual(char.门派归属, "青云门", "复合查询结果门派应该正确")
        
        print("查询准确性验证通过")


if __name__ == "__main__":
    # 可以直接运行此文件进行性能测试
    import unittest
    unittest.main()
