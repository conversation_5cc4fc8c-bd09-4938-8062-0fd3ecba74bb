"""
仙侠战斗菜单系统

基于Evennia EvMenu的仙侠战斗菜单界面。
提供直观的技能选择、目标选择和战斗操作界面。
"""

from evennia import EvMenu
from evennia.utils import logger
from .xiuxian_skill_system import get_skill_system
from .wuxing_calculator import get_wuxing_calculator
import time


def xiuxian_combat_menu_start(caller, raw_string, **kwargs):
    """
    仙侠战斗主菜单
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    # 检查是否在战斗中
    if not hasattr(caller, 'ndb') or not caller.ndb.combat_handler:
        text = "|r你不在战斗中。|n"
        options = {"key": "_default", "goto": "end_menu"}
        return text, options
    
    # 获取角色状态
    hp = getattr(caller, 'hp', 100)
    max_hp = getattr(caller, 'max_hp', 100)
    sp = getattr(caller, 'db', {}).get('spiritual_power', 100)
    max_sp = getattr(caller, 'db', {}).get('max_spiritual_power', 100)
    realm = getattr(caller, '修为境界', '练气')
    element = getattr(caller, '五行属性', '土')
    
    text = f"""
|c=== 仙侠战斗菜单 ===|n

|w角色状态:|n
生命值: |r{hp}/{max_hp}|n
灵力: |b{sp}/{max_sp}|n
境界: |y{realm}|n
五行: |g{element}|n

|w选择你的行动:|n
"""
    
    options = [
        {
            "key": ("1", "attack", "攻击"),
            "desc": "1) 基础攻击",
            "goto": "select_attack_target"
        },
        {
            "key": ("2", "skill", "技能"),
            "desc": "2) 使用技能",
            "goto": "select_skill"
        },
        {
            "key": ("3", "defend", "防御"),
            "desc": "3) 防御姿态",
            "goto": "confirm_defend"
        },
        {
            "key": ("4", "status", "状态"),
            "desc": "4) 查看详细状态",
            "goto": "show_detailed_status"
        },
        {
            "key": ("5", "help", "帮助"),
            "desc": "5) 战斗帮助",
            "goto": "show_combat_help"
        },
        {
            "key": ("q", "quit", "退出"),
            "desc": "q) 退出菜单",
            "goto": "end_menu"
        }
    ]
    
    return text, options


def select_attack_target(caller, raw_string, **kwargs):
    """
    选择攻击目标
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    # 获取可攻击的目标
    combat_handler = caller.ndb.combat_handler
    if not combat_handler:
        text = "|r战斗已结束。|n"
        options = {"key": "_default", "goto": "end_menu"}
        return text, options
    
    # 获取敌对目标
    enemies = []
    for combatant in combat_handler.db.combatants:
        if combatant != caller and getattr(combatant, 'hp', 0) > 0:
            enemies.append(combatant)
    
    if not enemies:
        text = "|r没有可攻击的目标。|n"
        options = {"key": "_default", "goto": "xiuxian_combat_menu_start"}
        return text, options
    
    text = f"""
|c=== 选择攻击目标 ===|n

|w可攻击的目标:|n
"""
    
    options = []
    for i, enemy in enumerate(enemies, 1):
        enemy_hp = getattr(enemy, 'hp', 100)
        enemy_max_hp = getattr(enemy, 'max_hp', 100)
        enemy_realm = getattr(enemy, '修为境界', '练气')
        enemy_element = getattr(enemy, '五行属性', '土')
        
        text += f"{i}) |w{enemy.key}|n - 生命: |r{enemy_hp}/{enemy_max_hp}|n | 境界: |y{enemy_realm}|n | 五行: |g{enemy_element}|n\n"
        
        options.append({
            "key": str(i),
            "desc": f"{i}) 攻击 {enemy.key}",
            "goto": ("confirm_attack", {"target": enemy})
        })
    
    options.append({
        "key": ("b", "back", "返回"),
        "desc": "b) 返回主菜单",
        "goto": "xiuxian_combat_menu_start"
    })
    
    return text, options


def confirm_attack(caller, raw_string, target=None, **kwargs):
    """
    确认攻击
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        target: 攻击目标
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    if not target:
        text = "|r无效的目标。|n"
        options = {"key": "_default", "goto": "select_attack_target"}
        return text, options
    
    # 计算五行相克预览
    wuxing_calc = get_wuxing_calculator()
    caller_element = getattr(caller, '五行属性', '土')
    target_element = getattr(target, '五行属性', '土')
    
    wuxing_bonus = wuxing_calc.calculate_elemental_bonus(caller_element, target_element)
    
    # 预估伤害
    base_damage = getattr(caller, 'attack', 20)
    if hasattr(caller, 'get_realm_combat_bonus'):
        realm_bonus = caller.get_realm_combat_bonus()
        base_damage *= realm_bonus
    
    estimated_damage = int(base_damage * wuxing_bonus)
    
    text = f"""
|c=== 确认攻击 ===|n

|w目标:|n {target.key}
|w你的五行:|n {caller_element}
|w目标五行:|n {target_element}
|w五行相克:|n {wuxing_bonus:.1f}x
|w预估伤害:|n {estimated_damage}

确认攻击吗？
"""
    
    options = [
        {
            "key": ("y", "yes", "确认"),
            "desc": "y) 确认攻击",
            "goto": ("execute_attack", {"target": target})
        },
        {
            "key": ("n", "no", "取消"),
            "desc": "n) 取消攻击",
            "goto": "select_attack_target"
        }
    ]
    
    return text, options


def execute_attack(caller, raw_string, target=None, **kwargs):
    """
    执行攻击
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        target: 攻击目标
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    if not target:
        text = "|r攻击失败：无效目标。|n"
        options = {"key": "_default", "goto": "xiuxian_combat_menu_start"}
        return text, options
    
    # 执行基础攻击
    combat_handler = caller.ndb.combat_handler
    if combat_handler and hasattr(combat_handler, 'process_xiuxian_action'):
        action_dict = {
            "action": "basic_attack",
            "target": target
        }
        
        result = combat_handler.process_xiuxian_action(caller, action_dict)
        
        if result.get("success", False):
            effects = result.get("effects", [])
            text = f"|g攻击成功！|n\n"
            
            for effect in effects:
                if effect.get("type") == "damage":
                    actual_damage = effect.get("actual_damage", 0)
                    wuxing_bonus = effect.get("wuxing_bonus", 1.0)
                    
                    text += f"对 {target.key} 造成 {actual_damage} 点伤害"
                    
                    if wuxing_bonus > 1.0:
                        text += " |r(五行相克加成)|n"
                    elif wuxing_bonus < 1.0:
                        text += " |y(五行被克减免)|n"
                    else:
                        text += ""
                    
                    text += f"\n{target.key} 剩余生命值: {getattr(target, 'hp', 0)}"
        else:
            text = f"|r攻击失败：{result.get('message', '未知错误')}|n"
    else:
        text = "|r攻击失败：战斗系统错误。|n"
    
    options = [
        {
            "key": "_default",
            "desc": "按任意键继续",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def select_skill(caller, raw_string, **kwargs):
    """
    选择技能
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    skill_system = get_skill_system()
    
    # 加载技能数据
    skill_data = skill_system.load_skill_data(caller)
    
    if not skill_data:
        text = "|r你还没有学会任何技能。|n"
        options = {"key": "_default", "goto": "xiuxian_combat_menu_start"}
        return text, options
    
    text = f"""
|c=== 选择技能 ===|n

|w可用技能:|n
"""
    
    options = []
    available_skills = []
    
    for skill_name, skill_info in skill_data.items():
        # 检查技能是否可用
        cooldown_check = skill_system.check_skill_cooldown(caller, skill_name)
        
        if cooldown_check["ready"]:
            available_skills.append((skill_name, skill_info))
    
    if not available_skills:
        text += "|r当前没有可用的技能。|n"
        options = [{"key": "_default", "goto": "xiuxian_combat_menu_start"}]
        return text, options
    
    for i, (skill_name, skill_info) in enumerate(available_skills, 1):
        skill_template = skill_info["template"]
        skill_type = skill_template.get("type", "未知")
        element = skill_template.get("element", "无")
        mp_cost = skill_template.get("mp_cost", 0)
        
        text += f"{i}) |w{skill_name}|n - {skill_type} | {element}系 | 消耗: {mp_cost}\n"
        text += f"   {skill_template.get('description', '无描述')}\n"
        
        options.append({
            "key": str(i),
            "desc": f"{i}) 使用 {skill_name}",
            "goto": ("select_skill_target", {"skill_name": skill_name})
        })
    
    options.append({
        "key": ("b", "back", "返回"),
        "desc": "b) 返回主菜单",
        "goto": "xiuxian_combat_menu_start"
    })
    
    return text, options


def select_skill_target(caller, raw_string, skill_name=None, **kwargs):
    """
    选择技能目标
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        skill_name: 技能名称
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    if not skill_name:
        text = "|r无效的技能。|n"
        options = {"key": "_default", "goto": "select_skill"}
        return text, options
    
    skill_system = get_skill_system()
    skill_data = skill_system.load_skill_data(caller)
    
    if skill_name not in skill_data:
        text = "|r技能不存在。|n"
        options = {"key": "_default", "goto": "select_skill"}
        return text, options
    
    skill_info = skill_data[skill_name]
    skill_template = skill_info["template"]
    skill_type = skill_template.get("type", "未知")
    
    text = f"""
|c=== 选择技能目标 ===|n

|w技能:|n {skill_name} ({skill_type})
|w描述:|n {skill_template.get('description', '无描述')}

|w选择目标:|n
"""
    
    options = []
    
    # 根据技能类型确定可选目标
    if skill_type in ["攻击", "减益"]:
        # 攻击性技能，选择敌人
        combat_handler = caller.ndb.combat_handler
        if combat_handler:
            enemies = [c for c in combat_handler.db.combatants if c != caller and getattr(c, 'hp', 0) > 0]
            
            for i, enemy in enumerate(enemies, 1):
                text += f"{i}) {enemy.key}\n"
                options.append({
                    "key": str(i),
                    "desc": f"{i}) 对 {enemy.key} 使用",
                    "goto": ("execute_skill", {"skill_name": skill_name, "target": enemy})
                })
    
    elif skill_type in ["治疗", "增益"]:
        # 辅助性技能，选择友方或自己
        text += f"1) {caller.key} (自己)\n"
        options.append({
            "key": "1",
            "desc": "1) 对自己使用",
            "goto": ("execute_skill", {"skill_name": skill_name, "target": caller})
        })
        
        # 可以添加队友选择逻辑
    
    else:
        # 其他技能，默认对自己使用
        text += f"1) {caller.key} (自己)\n"
        options.append({
            "key": "1",
            "desc": "1) 使用技能",
            "goto": ("execute_skill", {"skill_name": skill_name, "target": caller})
        })
    
    options.append({
        "key": ("b", "back", "返回"),
        "desc": "b) 返回技能选择",
        "goto": "select_skill"
    })
    
    return text, options


def execute_skill(caller, raw_string, skill_name=None, target=None, **kwargs):
    """
    执行技能
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        skill_name: 技能名称
        target: 目标
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    if not skill_name or not target:
        text = "|r技能使用失败：参数错误。|n"
        options = {"key": "_default", "goto": "xiuxian_combat_menu_start"}
        return text, options
    
    # 使用技能
    if hasattr(caller, 'use_xiuxian_skill'):
        result = caller.use_xiuxian_skill(skill_name, target)
        
        if result.get("success", False):
            text = f"|g技能使用成功！|n\n"
            text += f"你使用了 {skill_name}"
            
            if target != caller:
                text += f" 对 {target.key}"
            
            text += "\n\n"
            
            # 显示效果
            effects = result.get("effects", [])
            for effect in effects:
                effect_type = effect.get("type")
                value = effect.get("value", 0)
                
                if effect_type == "damage":
                    wuxing_bonus = effect.get("wuxing_bonus", 1.0)
                    text += f"造成 {value} 点伤害"
                    
                    if wuxing_bonus > 1.0:
                        text += " |r(五行相克加成)|n"
                    elif wuxing_bonus < 1.0:
                        text += " |y(五行被克减免)|n"
                    
                    text += f"\n{target.key} 剩余生命值: {getattr(target, 'hp', 0)}\n"
                
                elif effect_type == "heal":
                    text += f"恢复 {value} 点生命值\n"
                    text += f"{target.key} 当前生命值: {getattr(target, 'hp', 0)}\n"
                
                elif effect_type == "defense":
                    duration = effect.get("duration", 1)
                    text += f"防御力提升 {value} 点，持续 {duration} 回合\n"
        
        else:
            text = f"|r技能使用失败：{result.get('message', '未知错误')}|n"
    
    else:
        text = "|r技能使用失败：角色不支持仙侠技能。|n"
    
    options = [
        {
            "key": "_default",
            "desc": "按任意键继续",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def confirm_defend(caller, raw_string, **kwargs):
    """
    确认防御
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    text = f"""
|c=== 防御姿态 ===|n

进入防御姿态将：
- 提升防御力 10 点
- 持续 1 回合
- 跳过本回合攻击

确认进入防御姿态吗？
"""
    
    options = [
        {
            "key": ("y", "yes", "确认"),
            "desc": "y) 确认防御",
            "goto": "execute_defend"
        },
        {
            "key": ("n", "no", "取消"),
            "desc": "n) 取消",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def execute_defend(caller, raw_string, **kwargs):
    """
    执行防御
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    # 执行防御行动
    combat_handler = caller.ndb.combat_handler
    if combat_handler and hasattr(combat_handler, 'process_xiuxian_action'):
        action_dict = {
            "action": "defend"
        }
        
        result = combat_handler.process_xiuxian_action(caller, action_dict)
        
        if result.get("success", False):
            text = "|g进入防御姿态！|n\n"
            text += "你的防御力暂时提升了。"
        else:
            text = f"|r防御失败：{result.get('message', '未知错误')}|n"
    else:
        text = "|r防御失败：战斗系统错误。|n"
    
    options = [
        {
            "key": "_default",
            "desc": "按任意键继续",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def show_detailed_status(caller, raw_string, **kwargs):
    """
    显示详细状态
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    # 基础状态
    hp = getattr(caller, 'hp', 100)
    max_hp = getattr(caller, 'max_hp', 100)
    sp = getattr(caller, 'db', {}).get('spiritual_power', 100)
    max_sp = getattr(caller, 'db', {}).get('max_spiritual_power', 100)
    realm = getattr(caller, '修为境界', '练气')
    element = getattr(caller, '五行属性', '土')
    
    text = f"""
|c=== 详细状态 ===|n

|w基础属性:|n
生命值: |r{hp}/{max_hp}|n ({hp/max_hp*100:.1f}%)
灵力: |b{sp}/{max_sp}|n ({sp/max_sp*100:.1f}%)
境界: |y{realm}|n
五行: |g{element}|n

|w战斗属性:|n
攻击力: {getattr(caller, 'attack', 20)}
防御力: {getattr(caller, 'defense', 10)}
"""
    
    # 境界加成
    if hasattr(caller, 'get_realm_combat_bonus'):
        combat_bonus = caller.get_realm_combat_bonus()
        text += f"境界加成: {combat_bonus:.1f}x\n"
    
    # 增益效果
    if hasattr(caller, 'db') and 'defense_buffs' in caller.db:
        active_buffs = []
        current_time = time.time()
        
        for buff in caller.db.defense_buffs:
            if buff.get('expires', 0) > current_time:
                remaining = buff['expires'] - current_time
                active_buffs.append(f"防御+{buff['value']} ({remaining:.1f}s)")
        
        if active_buffs:
            text += f"\n|w增益效果:|n\n"
            for buff in active_buffs:
                text += f"  {buff}\n"
    
    options = [
        {
            "key": "_default",
            "desc": "按任意键返回",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def show_combat_help(caller, raw_string, **kwargs):
    """
    显示战斗帮助
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        tuple: (菜单文本, 选项字典)
    """
    text = f"""
|c=== 仙侠战斗帮助 ===|n

|w基础概念:|n
- |y境界|n: 影响战斗力，高境界对低境界有压制效果
- |g五行|n: 金克木，木克土，土克水，水克火，火克金
- |b灵力|n: 使用技能消耗的资源

|w战斗行动:|n
- |w攻击|n: 基础物理攻击，受五行相克影响
- |w技能|n: 消耗灵力的特殊能力，效果多样
- |w防御|n: 提升防御力，减少受到的伤害

|w五行相克:|n
- 相克时伤害增加 20-50%
- 被克时伤害减少 20-50%
- 平衡时无加成或减免

|w技能类型:|n
- |r攻击技能|n: 造成伤害
- |g治疗技能|n: 恢复生命值
- |b增益技能|n: 提升属性
- |y减益技能|n: 削弱敌人
"""
    
    options = [
        {
            "key": "_default",
            "desc": "按任意键返回",
            "goto": "xiuxian_combat_menu_start"
        }
    ]
    
    return text, options


def end_menu(caller, raw_string, **kwargs):
    """
    结束菜单
    
    Args:
        caller: 调用者
        raw_string: 原始输入
        **kwargs: 其他参数
        
    Returns:
        None
    """
    caller.msg("退出战斗菜单。")
    return None, None


# 菜单节点映射
XIUXIAN_COMBAT_MENU_NODES = {
    "xiuxian_combat_menu_start": xiuxian_combat_menu_start,
    "select_attack_target": select_attack_target,
    "confirm_attack": confirm_attack,
    "execute_attack": execute_attack,
    "select_skill": select_skill,
    "select_skill_target": select_skill_target,
    "execute_skill": execute_skill,
    "confirm_defend": confirm_defend,
    "execute_defend": execute_defend,
    "show_detailed_status": show_detailed_status,
    "show_combat_help": show_combat_help,
    "end_menu": end_menu
}


def start_xiuxian_combat_menu(caller):
    """
    启动仙侠战斗菜单
    
    Args:
        caller: 调用者
    """
    EvMenu(
        caller,
        XIUXIAN_COMBAT_MENU_NODES,
        startnode="xiuxian_combat_menu_start",
        cmdset_mergetype="Replace",
        cmdset_priority=1,
        auto_quit=True,
        auto_look=False,
        auto_help=False
    )
