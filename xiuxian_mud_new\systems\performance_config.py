"""
性能优化配置管理系统

提供统一的性能优化配置管理，支持：
- 运行时配置调整
- 性能参数优化
- 配置热重载
- 环境适配配置
- 性能调优建议

配置分类：
- TagProperty缓存配置
- 事件总线优化配置
- Handler内存管理配置
- AI导演性能配置
- 系统监控配置
"""

import json
import os
import time
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from threading import RLock

from evennia.utils.logger import log_info, log_err


@dataclass
class TagPropertyCacheConfig:
    """TagProperty缓存配置"""
    l1_cache_size: int = 1000
    l2_cache_size: int = 5000
    default_ttl: int = 300
    query_pattern_learning: bool = True
    predictive_caching: bool = True
    cache_hit_threshold: float = 0.8
    auto_optimization: bool = True
    

@dataclass
class EventBusOptimizationConfig:
    """事件总线优化配置"""
    max_workers: int = 4
    enable_compression: bool = True
    enable_parallel_processing: bool = True
    adaptive_batch_processing: bool = True
    compression_threshold: int = 5
    batch_size_min: int = 1
    batch_size_max: int = 50
    load_balancing: bool = True
    

@dataclass
class HandlerMemoryConfig:
    """Handler内存管理配置"""
    enable_intelligent_optimization: bool = True
    memory_pressure_threshold: float = 0.8
    prediction_enabled: bool = True
    pattern_learning_samples: int = 100
    optimization_interval: int = 60
    memory_spike_threshold: float = 2.0
    low_usage_threshold: float = 0.1
    

@dataclass
class AIDirectorOptimizationConfig:
    """AI导演优化配置"""
    enable_adaptive_scheduling: bool = True
    context_caching: bool = True
    parallel_decision_threshold: int = 30
    cache_ttl: int = 300
    load_threshold_high: float = 0.8
    load_threshold_low: float = 0.3
    performance_threshold: float = 0.1
    max_workers: int = 3
    

@dataclass
class PerformanceMonitorConfig:
    """性能监控配置"""
    monitoring_enabled: bool = True
    collection_interval: int = 30
    metrics_retention_hours: int = 24
    trend_analysis_enabled: bool = True
    auto_optimization_enabled: bool = True
    alert_thresholds: Dict[str, float] = None
    report_generation: bool = True
    

@dataclass
class SystemPerformanceConfig:
    """系统性能总配置"""
    tagproperty_cache: TagPropertyCacheConfig
    event_bus_optimization: EventBusOptimizationConfig
    handler_memory: HandlerMemoryConfig
    ai_director_optimization: AIDirectorOptimizationConfig
    performance_monitor: PerformanceMonitorConfig
    
    def __post_init__(self):
        if self.performance_monitor.alert_thresholds is None:
            self.performance_monitor.alert_thresholds = {
                "cpu_usage": 80.0,
                "memory_usage": 85.0,
                "query_time": 0.1,
                "event_queue_size": 1000
            }


class PerformanceConfigManager:
    """
    性能配置管理器
    
    提供配置的加载、保存、热重载和运行时调整功能
    """
    
    def __init__(self, config_file: str = "performance_config.json"):
        self.config_file = Path(config_file)
        self.config: Optional[SystemPerformanceConfig] = None
        self.config_watchers: List[Callable[[SystemPerformanceConfig], None]] = []
        self.lock = RLock()
        
        # 配置变更历史
        self.config_history: List[Dict[str, Any]] = []
        
        # 加载配置
        self.load_config()
        
        # 启动配置监控
        self._start_config_monitoring()
        
    def load_config(self) -> SystemPerformanceConfig:
        """加载配置"""
        with self.lock:
            try:
                if self.config_file.exists():
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 从JSON数据构建配置对象
                    self.config = self._dict_to_config(config_data)
                    log_info(f"性能配置已从 {self.config_file} 加载")
                else:
                    # 使用默认配置
                    self.config = self._create_default_config()
                    self.save_config()
                    log_info("使用默认性能配置")
                    
            except Exception as e:
                log_err(f"加载性能配置失败: {e}")
                self.config = self._create_default_config()
                
            return self.config
            
    def save_config(self):
        """保存配置"""
        with self.lock:
            try:
                config_data = self._config_to_dict(self.config)
                
                # 确保目录存在
                self.config_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                log_info(f"性能配置已保存到 {self.config_file}")
                
                # 记录配置变更
                self.config_history.append({
                    "timestamp": time.time(),
                    "action": "save",
                    "config": config_data.copy()
                })
                
                # 限制历史记录数量
                if len(self.config_history) > 50:
                    self.config_history = self.config_history[-50:]
                    
            except Exception as e:
                log_err(f"保存性能配置失败: {e}")
                
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        with self.lock:
            try:
                # 应用更新
                self._apply_config_updates(self.config, updates)
                
                # 保存配置
                self.save_config()
                
                # 通知配置观察者
                self._notify_config_watchers()
                
                log_info(f"性能配置已更新: {updates}")
                
            except Exception as e:
                log_err(f"更新性能配置失败: {e}")
                
    def get_config(self) -> SystemPerformanceConfig:
        """获取当前配置"""
        with self.lock:
            return self.config
            
    def add_config_watcher(self, watcher: Callable[[SystemPerformanceConfig], None]):
        """添加配置观察者"""
        with self.lock:
            self.config_watchers.append(watcher)
            
    def remove_config_watcher(self, watcher: Callable[[SystemPerformanceConfig], None]):
        """移除配置观察者"""
        with self.lock:
            if watcher in self.config_watchers:
                self.config_watchers.remove(watcher)
                
    def reload_config(self):
        """重新加载配置"""
        with self.lock:
            old_config = self.config
            self.load_config()
            
            if old_config != self.config:
                self._notify_config_watchers()
                log_info("性能配置已重新加载")
                
    def get_config_history(self) -> List[Dict[str, Any]]:
        """获取配置变更历史"""
        with self.lock:
            return self.config_history.copy()
            
    def optimize_config_for_environment(self, environment: str = "production"):
        """为特定环境优化配置"""
        with self.lock:
            if environment == "production":
                # 生产环境优化
                updates = {
                    "tagproperty_cache.l1_cache_size": 2000,
                    "tagproperty_cache.l2_cache_size": 10000,
                    "event_bus_optimization.max_workers": 6,
                    "handler_memory.optimization_interval": 30,
                    "ai_director_optimization.max_workers": 4,
                    "performance_monitor.collection_interval": 15
                }
            elif environment == "development":
                # 开发环境优化
                updates = {
                    "tagproperty_cache.l1_cache_size": 500,
                    "tagproperty_cache.l2_cache_size": 2000,
                    "event_bus_optimization.max_workers": 2,
                    "handler_memory.optimization_interval": 120,
                    "ai_director_optimization.max_workers": 2,
                    "performance_monitor.collection_interval": 60
                }
            elif environment == "testing":
                # 测试环境优化
                updates = {
                    "tagproperty_cache.l1_cache_size": 100,
                    "tagproperty_cache.l2_cache_size": 500,
                    "event_bus_optimization.max_workers": 1,
                    "handler_memory.optimization_interval": 300,
                    "ai_director_optimization.max_workers": 1,
                    "performance_monitor.collection_interval": 120
                }
            else:
                log_err(f"未知环境类型: {environment}")
                return
                
            self.update_config(updates)
            log_info(f"配置已针对 {environment} 环境优化")
            
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """获取配置优化建议"""
        recommendations = []
        
        with self.lock:
            config = self.config
            
            # TagProperty缓存建议
            if config.tagproperty_cache.l1_cache_size < 1000:
                recommendations.append({
                    "type": "tagproperty_cache",
                    "priority": "medium",
                    "description": "建议增加L1缓存大小以提升查询性能",
                    "suggested_value": 1000,
                    "current_value": config.tagproperty_cache.l1_cache_size
                })
                
            # 事件总线建议
            if not config.event_bus_optimization.enable_compression:
                recommendations.append({
                    "type": "event_bus",
                    "priority": "high",
                    "description": "建议启用事件压缩以减少处理开销",
                    "suggested_value": True,
                    "current_value": False
                })
                
            # Handler内存建议
            if not config.handler_memory.enable_intelligent_optimization:
                recommendations.append({
                    "type": "handler_memory",
                    "priority": "high",
                    "description": "建议启用智能内存优化",
                    "suggested_value": True,
                    "current_value": False
                })
                
            # AI导演建议
            if not config.ai_director_optimization.enable_adaptive_scheduling:
                recommendations.append({
                    "type": "ai_director",
                    "priority": "medium",
                    "description": "建议启用自适应调度以优化决策性能",
                    "suggested_value": True,
                    "current_value": False
                })
                
        return recommendations
        
    def _create_default_config(self) -> SystemPerformanceConfig:
        """创建默认配置"""
        return SystemPerformanceConfig(
            tagproperty_cache=TagPropertyCacheConfig(),
            event_bus_optimization=EventBusOptimizationConfig(),
            handler_memory=HandlerMemoryConfig(),
            ai_director_optimization=AIDirectorOptimizationConfig(),
            performance_monitor=PerformanceMonitorConfig()
        )
        
    def _dict_to_config(self, config_data: Dict[str, Any]) -> SystemPerformanceConfig:
        """从字典构建配置对象"""
        return SystemPerformanceConfig(
            tagproperty_cache=TagPropertyCacheConfig(**config_data.get("tagproperty_cache", {})),
            event_bus_optimization=EventBusOptimizationConfig(**config_data.get("event_bus_optimization", {})),
            handler_memory=HandlerMemoryConfig(**config_data.get("handler_memory", {})),
            ai_director_optimization=AIDirectorOptimizationConfig(**config_data.get("ai_director_optimization", {})),
            performance_monitor=PerformanceMonitorConfig(**config_data.get("performance_monitor", {}))
        )
        
    def _config_to_dict(self, config: SystemPerformanceConfig) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        return {
            "tagproperty_cache": asdict(config.tagproperty_cache),
            "event_bus_optimization": asdict(config.event_bus_optimization),
            "handler_memory": asdict(config.handler_memory),
            "ai_director_optimization": asdict(config.ai_director_optimization),
            "performance_monitor": asdict(config.performance_monitor)
        }
        
    def _apply_config_updates(self, config: SystemPerformanceConfig, updates: Dict[str, Any]):
        """应用配置更新"""
        for key, value in updates.items():
            if '.' in key:
                # 嵌套属性更新
                parts = key.split('.')
                obj = config
                for part in parts[:-1]:
                    obj = getattr(obj, part)
                setattr(obj, parts[-1], value)
            else:
                # 顶级属性更新
                setattr(config, key, value)
                
    def _notify_config_watchers(self):
        """通知配置观察者"""
        for watcher in self.config_watchers:
            try:
                watcher(self.config)
            except Exception as e:
                log_err(f"配置观察者通知失败: {e}")
                
    def _start_config_monitoring(self):
        """启动配置文件监控"""
        def monitor_config():
            last_modified = 0
            while True:
                try:
                    if self.config_file.exists():
                        current_modified = self.config_file.stat().st_mtime
                        if current_modified > last_modified:
                            last_modified = current_modified
                            if last_modified > 0:  # 跳过初始加载
                                self.reload_config()
                    time.sleep(5)  # 每5秒检查一次
                except Exception as e:
                    log_err(f"配置监控错误: {e}")
                    time.sleep(10)
                    
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()


# 全局配置管理器实例
_global_config_manager: Optional[PerformanceConfigManager] = None


def get_performance_config_manager() -> PerformanceConfigManager:
    """获取全局性能配置管理器"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = PerformanceConfigManager()
    return _global_config_manager


def get_performance_config() -> SystemPerformanceConfig:
    """获取当前性能配置"""
    return get_performance_config_manager().get_config()


def update_performance_config(updates: Dict[str, Any]):
    """更新性能配置"""
    get_performance_config_manager().update_config(updates)


def optimize_for_environment(environment: str):
    """为环境优化配置"""
    get_performance_config_manager().optimize_config_for_environment(environment)
