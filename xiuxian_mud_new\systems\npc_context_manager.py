"""
NPC上下文管理系统

负责管理NPC的对话上下文和世界感知：
- 实时世界状态感知
- 玩家互动历史管理
- 情境适应对话机制
- 与事件总线集成
- 上下文信息注入
"""

import time
from typing import Dict, Any, List, Optional
from evennia.utils import logger, search


class NPCContextManager:
    """NPC上下文管理器"""
    
    def __init__(self):
        """初始化上下文管理器"""
        self.world_state_cache = {}
        self.cache_expiry = 60  # 缓存60秒
        self.last_world_update = 0
        logger.log_info("NPC上下文管理器初始化完成")
    
    def build_conversation_context(self, npc, player, message: str) -> Dict[str, Any]:
        """构建完整的对话上下文"""
        context = {
            "npc_info": self._get_npc_info(npc),
            "player_info": self._get_player_info(player),
            "conversation_history": self._get_conversation_history(npc, player),
            "current_message": message,
            "world_state": self._get_current_world_state(),
            "location_context": self._get_location_context(npc),
            "time_context": self._get_time_context(),
            "relationship_context": self._get_relationship_context(npc, player),
            "recent_events": self._get_recent_events(),
            "sect_status": self._get_sect_status(npc),
            "cultivation_context": self._get_cultivation_context(player)
        }
        
        return context
    
    def _get_npc_info(self, npc) -> Dict[str, Any]:
        """获取NPC基本信息"""
        return {
            "name": npc.key,
            "realm": getattr(npc, "修为境界", "练气"),
            "sect": getattr(npc, "门派归属", "无门派"),
            "element": getattr(npc, "五行属性", "土"),
            "role": getattr(npc, "角色类型", "普通弟子"),
            "location": npc.location.key if npc.location else "未知",
            "personality_traits": npc.db.npc_state.get("personality_traits", []),
            "specialties": npc.db.npc_state.get("specialties", []),
            "authority_level": npc.db.npc_state.get("authority_level", "low"),
            "total_conversations": npc.db.npc_state.get("total_conversations", 0),
            "last_interaction": npc.db.npc_state.get("last_interaction_time", 0)
        }
    
    def _get_player_info(self, player) -> Dict[str, Any]:
        """获取玩家基本信息"""
        player_info = {
            "name": player.key,
            "location": player.location.key if player.location else "未知"
        }
        
        # 尝试获取玩家的修炼信息
        try:
            if hasattr(player, "修为境界"):
                player_info["realm"] = getattr(player, "修为境界", "练气")
            if hasattr(player, "门派归属"):
                player_info["sect"] = getattr(player, "门派归属", "无门派")
            if hasattr(player, "五行属性"):
                player_info["element"] = getattr(player, "五行属性", "土")
        except Exception as e:
            logger.log_warn(f"获取玩家修炼信息失败: {e}")
        
        return player_info
    
    def _get_conversation_history(self, npc, player) -> List[Dict[str, Any]]:
        """获取对话历史"""
        history = npc.db.npc_state.get("conversation_history", {})
        player_history = history.get(player.key, [])
        
        # 返回最近5条对话记录
        return player_history[-5:] if player_history else []
    
    def _get_current_world_state(self) -> Dict[str, Any]:
        """获取当前世界状态"""
        current_time = time.time()
        
        # 检查缓存是否过期
        if (current_time - self.last_world_update) < self.cache_expiry and self.world_state_cache:
            return self.world_state_cache
        
        # 更新世界状态
        world_state = {
            "spiritual_tide": self._get_spiritual_tide(),
            "weather": self._get_weather_state(),
            "time_of_day": self._get_time_of_day(),
            "season": self._get_current_season(),
            "major_events": self._get_major_world_events(),
            "sect_relations": self._get_sect_relations(),
            "cultivation_opportunities": self._get_cultivation_opportunities()
        }
        
        # 更新缓存
        self.world_state_cache = world_state
        self.last_world_update = current_time
        
        return world_state
    
    def _get_location_context(self, npc) -> Dict[str, Any]:
        """获取位置上下文"""
        if not npc.location:
            return {"type": "unknown", "description": "未知位置"}
        
        location = npc.location
        context = {
            "name": location.key,
            "description": getattr(location, "desc", ""),
            "type": self._determine_location_type(location),
            "occupants": [obj.key for obj in location.contents if hasattr(obj, "key") and obj != npc],
            "special_features": self._get_location_features(location)
        }
        
        return context
    
    def _get_time_context(self) -> Dict[str, Any]:
        """获取时间上下文"""
        current_time = time.time()
        
        # 简化的时间系统
        hour = int((current_time / 3600) % 24)
        
        if 6 <= hour < 12:
            time_period = "上午"
            atmosphere = "朝气蓬勃"
        elif 12 <= hour < 18:
            time_period = "下午"
            atmosphere = "阳光明媚"
        elif 18 <= hour < 22:
            time_period = "傍晚"
            atmosphere = "夕阳西下"
        else:
            time_period = "夜晚"
            atmosphere = "夜深人静"
        
        return {
            "hour": hour,
            "period": time_period,
            "atmosphere": atmosphere,
            "suitable_activities": self._get_suitable_activities(time_period)
        }
    
    def _get_relationship_context(self, npc, player) -> Dict[str, Any]:
        """获取关系上下文"""
        relationship_data = npc.db.npc_state.get("relationship_status", {})
        player_relationship = relationship_data.get(player.key, {})
        
        # 计算关系等级
        interaction_count = len(npc.db.npc_state.get("conversation_history", {}).get(player.key, []))
        
        if interaction_count == 0:
            relationship_level = "初次见面"
            familiarity = 0.0
        elif interaction_count < 5:
            relationship_level = "初识"
            familiarity = 0.2
        elif interaction_count < 15:
            relationship_level = "熟悉"
            familiarity = 0.5
        elif interaction_count < 30:
            relationship_level = "友好"
            familiarity = 0.7
        else:
            relationship_level = "密友"
            familiarity = 0.9
        
        return {
            "level": relationship_level,
            "familiarity": familiarity,
            "interaction_count": interaction_count,
            "last_interaction": player_relationship.get("last_interaction", 0),
            "relationship_notes": player_relationship.get("notes", [])
        }
    
    def _get_recent_events(self) -> List[Dict[str, Any]]:
        """获取最近事件"""
        # 这里应该从事件总线获取最近的事件
        # 目前返回模拟数据
        return [
            {
                "type": "world_event",
                "description": "灵气潮汐平稳",
                "timestamp": time.time() - 3600,
                "importance": "low"
            },
            {
                "type": "sect_event", 
                "description": "门派日常运转正常",
                "timestamp": time.time() - 1800,
                "importance": "low"
            }
        ]
    
    def _get_sect_status(self, npc) -> Dict[str, Any]:
        """获取门派状态"""
        sect_name = getattr(npc, "门派归属", "无门派")
        
        if sect_name == "无门派":
            return {"name": "无门派", "status": "自由修士", "recent_news": []}
        
        # 模拟门派状态
        return {
            "name": sect_name,
            "status": "和谐发展",
            "recent_news": [f"{sect_name}近期发展良好"],
            "current_focus": "弟子修炼",
            "leadership": "稳定"
        }
    
    def _get_cultivation_context(self, player) -> Dict[str, Any]:
        """获取修炼上下文"""
        context = {
            "current_realm": getattr(player, "修为境界", "练气"),
            "cultivation_progress": "稳步提升",
            "recent_breakthroughs": [],
            "cultivation_challenges": [],
            "recommended_practices": []
        }
        
        # 基于玩家境界提供建议
        realm = context["current_realm"]
        if realm == "练气":
            context["recommended_practices"] = ["基础吐纳", "五行调和", "筑基准备"]
        elif realm == "筑基":
            context["recommended_practices"] = ["金丹凝聚", "灵力精炼", "心境修炼"]
        elif realm == "金丹":
            context["recommended_practices"] = ["元婴孕育", "神识扩展", "法则领悟"]
        
        return context
    
    def _get_spiritual_tide(self) -> str:
        """获取灵气潮汐状态"""
        # 简化的灵气潮汐系统
        hour = int((time.time() / 3600) % 24)
        
        if 6 <= hour < 10:
            return "上升"
        elif 10 <= hour < 14:
            return "高潮"
        elif 14 <= hour < 18:
            return "平稳"
        elif 18 <= hour < 22:
            return "下降"
        else:
            return "低潮"
    
    def _get_weather_state(self) -> str:
        """获取天气状态"""
        # 简化的天气系统
        weather_options = ["晴朗", "多云", "阴天", "小雨", "大雨", "雾天"]
        return weather_options[int(time.time() / 86400) % len(weather_options)]
    
    def _get_time_of_day(self) -> str:
        """获取一天中的时间"""
        hour = int((time.time() / 3600) % 24)
        
        if 5 <= hour < 8:
            return "黎明"
        elif 8 <= hour < 11:
            return "上午"
        elif 11 <= hour < 14:
            return "正午"
        elif 14 <= hour < 17:
            return "下午"
        elif 17 <= hour < 20:
            return "傍晚"
        elif 20 <= hour < 23:
            return "夜晚"
        else:
            return "深夜"
    
    def _get_current_season(self) -> str:
        """获取当前季节"""
        # 简化的季节系统
        day_of_year = int(time.time() / 86400) % 365
        
        if 80 <= day_of_year < 172:
            return "春"
        elif 172 <= day_of_year < 266:
            return "夏"
        elif 266 <= day_of_year < 355:
            return "秋"
        else:
            return "冬"
    
    def _get_major_world_events(self) -> List[str]:
        """获取重大世界事件"""
        # 这里应该从天道导演获取重大事件
        return ["世界平静", "各门派和谐发展"]
    
    def _get_sect_relations(self) -> Dict[str, str]:
        """获取门派关系"""
        return {
            "青云门-天音寺": "友好",
            "青云门-鬼王宗": "对立",
            "天音寺-焚香谷": "中立",
            "合欢派-鬼王宗": "合作"
        }
    
    def _get_cultivation_opportunities(self) -> List[str]:
        """获取修炼机会"""
        current_time = self._get_time_of_day()
        spiritual_tide = self._get_spiritual_tide()
        
        opportunities = []
        
        if spiritual_tide == "高潮":
            opportunities.append("灵气充沛，适合修炼")
        
        if current_time in ["黎明", "正午", "傍晚"]:
            opportunities.append("天地交汇时刻，适合感悟")
        
        return opportunities
    
    def _determine_location_type(self, location) -> str:
        """确定位置类型"""
        location_name = location.key.lower()
        
        if any(word in location_name for word in ["大殿", "殿", "堂"]):
            return "formal_hall"
        elif any(word in location_name for word in ["山", "峰", "岭"]):
            return "mountain"
        elif any(word in location_name for word in ["院", "园", "庭"]):
            return "courtyard"
        elif any(word in location_name for word in ["洞", "府", "室"]):
            return "chamber"
        else:
            return "general"
    
    def _get_location_features(self, location) -> List[str]:
        """获取位置特征"""
        features = []
        
        # 基于位置名称推断特征
        location_name = location.key.lower()
        
        if "修炼" in location_name:
            features.append("适合修炼")
        if "藏书" in location_name or "书" in location_name:
            features.append("知识宝库")
        if "炼丹" in location_name:
            features.append("炼丹设施")
        if "演武" in location_name or "练功" in location_name:
            features.append("武技训练")
        
        return features
    
    def _get_suitable_activities(self, time_period: str) -> List[str]:
        """获取适合的活动"""
        activities = {
            "上午": ["修炼", "学习", "讨论功法"],
            "下午": ["实战训练", "炼丹", "交流心得"],
            "傍晚": ["冥想", "总结", "休息"],
            "夜晚": ["静修", "感悟", "休息"]
        }
        
        return activities.get(time_period, ["休息"])
    
    def update_relationship(self, npc, player, interaction_result: str):
        """更新关系状态"""
        relationship_data = npc.db.npc_state.get("relationship_status", {})
        
        if player.key not in relationship_data:
            relationship_data[player.key] = {
                "first_meeting": time.time(),
                "last_interaction": time.time(),
                "notes": []
            }
        
        player_relationship = relationship_data[player.key]
        player_relationship["last_interaction"] = time.time()
        
        # 记录交互结果
        if interaction_result:
            player_relationship["notes"].append({
                "timestamp": time.time(),
                "result": interaction_result
            })
            
            # 限制记录数量
            if len(player_relationship["notes"]) > 10:
                player_relationship["notes"] = player_relationship["notes"][-5:]
        
        # 保存更新
        npc.db.npc_state["relationship_status"] = relationship_data
    
    def get_context_summary(self, context: Dict[str, Any]) -> str:
        """获取上下文摘要"""
        npc_info = context["npc_info"]
        player_info = context["player_info"]
        world_state = context["world_state"]
        relationship = context["relationship_context"]
        
        summary = f"""当前情况：
- NPC：{npc_info['name']}（{npc_info['role']}，{npc_info['realm']}）
- 玩家：{player_info['name']}
- 关系：{relationship['level']}（互动{relationship['interaction_count']}次）
- 位置：{npc_info['location']}
- 时间：{context['time_context']['period']}
- 灵气：{world_state['spiritual_tide']}
- 天气：{world_state['weather']}"""
        
        return summary
    
    def clear_cache(self):
        """清理缓存"""
        self.world_state_cache = {}
        self.last_world_update = 0
        logger.log_info("NPC上下文缓存已清理")
