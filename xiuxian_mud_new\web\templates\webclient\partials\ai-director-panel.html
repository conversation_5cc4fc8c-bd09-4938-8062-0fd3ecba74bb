{% comment %}
AI导演面板组件模板

功能：
- 三层AI导演状态实时显示（天道/地灵/器灵）
- 智能故事推进展示与交互
- 个性化智能提示系统
- 导演控制与设置面板
- WebSocket实时通信集成

技术集成：
- 与TiandaoDirector、<PERSON><PERSON><PERSON>ire<PERSON>、<PERSON>lingDirector完美集成
- 支持实时状态更新和事件推送
- 移动端友好的响应式设计
- 本地存储用户偏好设置
- 完整的错误处理和重连机制
{% endcomment %}

<div class="ai-director-panel" id="ai-director-panel">
    <!-- 面板头部 -->
    <div class="ai-director-panel__header">
        <div class="ai-director-panel__title-section">
            <span class="ai-director-panel__icon">🎭</span>
            <span class="ai-director-panel__title">AI导演</span>
            <span class="ai-director-panel__version">v2.0</span>
        </div>
        <div class="ai-director-panel__header-controls">
            <span class="ai-director-panel__status" id="ai-director-status" data-status="active">
                <span class="status-indicator"></span>
                <span class="status-text">运行中</span>
            </span>
            <button class="ai-director-panel__toggle" id="ai-director-toggle" title="展开/收起面板">
                <span class="toggle-icon">−</span>
            </button>
        </div>
    </div>

    <!-- 面板内容 -->
    <div class="ai-director-panel__content" id="ai-director-content">
        <!-- 三层导演状态指示器 -->
        <div class="ai-director-panel__directors">
            <div class="ai-director-panel__directors-title">导演状态</div>
            <div class="ai-director-indicators">
                <!-- 天道导演 -->
                <div class="ai-director-indicator" data-director="tiandao" id="tiandao-indicator">
                    <div class="ai-director-indicator__header">
                        <span class="ai-director-indicator__icon">🌌</span>
                        <span class="ai-director-indicator__name">天道</span>
                        <span class="ai-director-indicator__cycle">5分钟</span>
                    </div>
                    <div class="ai-director-indicator__status" id="tiandao-status" data-status="active">
                        <span class="status-dot"></span>
                        <span class="status-text">活跃</span>
                    </div>
                    <div class="ai-director-indicator__progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="tiandao-progress" data-progress="60"></div>
                        </div>
                        <span class="progress-text" id="tiandao-next">下次: 3分钟</span>
                    </div>
                    <div class="ai-director-indicator__last-action" id="tiandao-last">
                        最近: 天象异变，灵气波动
                    </div>
                </div>

                <!-- 地灵导演 -->
                <div class="ai-director-indicator" data-director="diling" id="diling-indicator">
                    <div class="ai-director-indicator__header">
                        <span class="ai-director-indicator__icon">🏔️</span>
                        <span class="ai-director-indicator__name">地灵</span>
                        <span class="ai-director-indicator__cycle">1分钟</span>
                    </div>
                    <div class="ai-director-indicator__status" id="diling-status" data-status="active">
                        <span class="status-dot"></span>
                        <span class="status-text">活跃</span>
                    </div>
                    <div class="ai-director-indicator__progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="diling-progress" data-progress="80"></div>
                        </div>
                        <span class="progress-text" id="diling-next">下次: 12秒</span>
                    </div>
                    <div class="ai-director-indicator__last-action" id="diling-last">
                        最近: 青云山脉灵兽活动
                    </div>
                </div>

                <!-- 器灵导演 -->
                <div class="ai-director-indicator" data-director="qiling" id="qiling-indicator">
                    <div class="ai-director-indicator__header">
                        <span class="ai-director-indicator__icon">⚡</span>
                        <span class="ai-director-indicator__name">器灵</span>
                        <span class="ai-director-indicator__cycle">10秒</span>
                    </div>
                    <div class="ai-director-indicator__status" id="qiling-status" data-status="active">
                        <span class="status-dot"></span>
                        <span class="status-text">活跃</span>
                    </div>
                    <div class="ai-director-indicator__progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="qiling-progress" data-progress="30"></div>
                        </div>
                        <span class="progress-text" id="qiling-next">下次: 3秒</span>
                    </div>
                    <div class="ai-director-indicator__last-action" id="qiling-last">
                        最近: 感应到修炼机缘
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新故事推进 -->
        <div class="ai-director-panel__story-section">
            <div class="ai-director-panel__story-header">
                <span class="story-title">📖 最新推进</span>
                <div class="story-controls">
                    <button class="story-control-btn" id="story-pause" title="暂停故事推进">
                        <span class="btn-icon">⏸️</span>
                    </button>
                    <button class="story-control-btn" id="story-refresh" title="刷新故事">
                        <span class="btn-icon">🔄</span>
                    </button>
                    <button class="story-control-btn" id="story-history" title="查看历史">
                        <span class="btn-icon">📚</span>
                    </button>
                </div>
            </div>
            <div class="ai-director-panel__messages" id="ai-director-messages">
                <!-- 示例消息 -->
                <div class="ai-director-message" data-type="tiandao" data-priority="high">
                    <div class="message-header">
                        <span class="message-icon">🌌</span>
                        <span class="message-source">天道</span>
                        <span class="message-time">刚刚</span>
                        <span class="message-priority high">重要</span>
                    </div>
                    <div class="message-content">
                        天道感应到修仙界的灵气出现异常波动，各大门派开始关注这一现象。这可能预示着某种重大变化即将到来...
                    </div>
                    <div class="message-actions">
                        <button class="message-action-btn" data-action="investigate">调查</button>
                        <button class="message-action-btn" data-action="ignore">忽略</button>
                    </div>
                </div>

                <div class="ai-director-message" data-type="diling" data-priority="medium">
                    <div class="message-header">
                        <span class="message-icon">🏔️</span>
                        <span class="message-source">地灵</span>
                        <span class="message-time">2分钟前</span>
                        <span class="message-priority medium">一般</span>
                    </div>
                    <div class="message-content">
                        青云山脉深处传来阵阵兽吼声，似乎有强大的灵兽在活动。附近的修士们议论纷纷，有人建议组队前往探索。
                    </div>
                    <div class="message-actions">
                        <button class="message-action-btn" data-action="join_team">组队</button>
                        <button class="message-action-btn" data-action="solo_explore">独自探索</button>
                    </div>
                </div>

                <div class="ai-director-message" data-type="qiling" data-priority="low">
                    <div class="message-header">
                        <span class="message-icon">⚡</span>
                        <span class="message-source">器灵</span>
                        <span class="message-time">5分钟前</span>
                        <span class="message-priority low">提示</span>
                    </div>
                    <div class="message-content">
                        你感受到一股神秘的力量在召唤，似乎与你的修炼功法产生了共鸣。这可能是一个提升实力的好机会。
                    </div>
                    <div class="message-actions">
                        <button class="message-action-btn" data-action="meditate">打坐感应</button>
                    </div>
                </div>
            </div>
            <div class="ai-director-panel__messages-footer">
                <button class="load-more-btn" id="load-more-messages">加载更多消息</button>
            </div>
        </div>

        <!-- 智能提示系统 -->
        <div class="ai-director-panel__hints-section">
            <div class="ai-director-panel__hints-header">
                <span class="hints-title">💡 智能提示</span>
                <div class="hints-controls">
                    <button type="button" class="hints-control-btn" id="hints-toggle" title="开启/关闭智能提示">
                        <span class="btn-icon">🔔</span>
                    </button>
                    <button type="button" class="hints-control-btn" id="hints-clear" title="清空提示">
                        <span class="btn-icon">🗑️</span>
                    </button>
                </div>
            </div>
            <div class="ai-director-panel__hints" id="ai-director-hints">
                <div class="ai-director-panel__hint-list" id="hint-list">
                    <!-- 高优先级提示 -->
                    <div class="ai-director-hint" data-priority="high" data-category="cultivation">
                        <div class="hint-header">
                            <span class="hint-icon">🔥</span>
                            <span class="hint-priority high">紧急</span>
                            <span class="hint-category">修炼</span>
                        </div>
                        <div class="hint-content">
                            <span class="hint-text">检测到你的修为即将突破，建议立即前往练功房进行闭关修炼</span>
                            <div class="hint-details">
                                预计突破时间：30分钟 | 成功率：85%
                            </div>
                        </div>
                        <div class="hint-actions">
                            <button type="button" class="hint-action-btn primary" data-command="go 练功房">立即前往</button>
                            <button type="button" class="hint-action-btn secondary" data-action="delay">稍后提醒</button>
                            <button type="button" class="hint-action-btn dismiss" data-action="dismiss">忽略</button>
                        </div>
                    </div>

                    <!-- 中优先级提示 -->
                    <div class="ai-director-hint" data-priority="medium" data-category="social">
                        <div class="hint-header">
                            <span class="hint-icon">👥</span>
                            <span class="hint-priority medium">一般</span>
                            <span class="hint-category">社交</span>
                        </div>
                        <div class="hint-content">
                            <span class="hint-text">有同门师兄正在组队探索秘境，你可以申请加入获得更多经验</span>
                            <div class="hint-details">
                                队伍人数：3/5 | 推荐等级：20-30级
                            </div>
                        </div>
                        <div class="hint-actions">
                            <button type="button" class="hint-action-btn primary" data-command="team join">申请加入</button>
                            <button type="button" class="hint-action-btn secondary" data-command="team info">查看详情</button>
                        </div>
                    </div>

                    <!-- 低优先级提示 -->
                    <div class="ai-director-hint" data-priority="low" data-category="exploration">
                        <div class="hint-header">
                            <span class="hint-icon">🗺️</span>
                            <span class="hint-priority low">建议</span>
                            <span class="hint-category">探索</span>
                        </div>
                        <div class="hint-content">
                            <span class="hint-text">附近发现了一处灵草丛生的地方，可以采集一些炼丹材料</span>
                            <div class="hint-details">
                                预计收获：中级灵草 x3-5
                            </div>
                        </div>
                        <div class="hint-actions">
                            <button type="button" class="hint-action-btn primary" data-command="gather herbs">开始采集</button>
                        </div>
                    </div>
                </div>

                <!-- 提示统计 -->
                <div class="ai-director-panel__hints-stats">
                    <div class="hints-stat">
                        <span class="stat-label">今日提示：</span>
                        <span class="stat-value" id="hints-today">12</span>
                    </div>
                    <div class="hints-stat">
                        <span class="stat-label">已执行：</span>
                        <span class="stat-value" id="hints-executed">8</span>
                    </div>
                    <div class="hints-stat">
                        <span class="stat-label">成功率：</span>
                        <span class="stat-value" id="hints-success-rate">67%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导演控制面板 -->
        <div class="ai-director-panel__controls-section">
            <div class="ai-director-panel__controls-title">控制面板</div>
            <div class="ai-director-panel__controls">
                <div class="control-group">
                    <button type="button" class="ai-director-panel__btn primary" id="ai-director-pause" data-action="pause">
                        <span class="btn-icon">⏸️</span>
                        <span class="btn-text">暂停导演</span>
                    </button>

                    <button type="button" class="ai-director-panel__btn secondary" id="ai-director-manual" data-action="manual">
                        <span class="btn-icon">✋</span>
                        <span class="btn-text">手动触发</span>
                    </button>
                </div>

                <div class="control-group">
                    <button type="button" class="ai-director-panel__btn" id="ai-director-settings" data-action="settings">
                        <span class="btn-icon">⚙️</span>
                        <span class="btn-text">设置</span>
                    </button>

                    <button type="button" class="ai-director-panel__btn" id="ai-director-stats" data-action="stats">
                        <span class="btn-icon">📊</span>
                        <span class="btn-text">统计</span>
                    </button>
                </div>

                <div class="control-group">
                    <button type="button" class="ai-director-panel__btn" id="ai-director-export" data-action="export">
                        <span class="btn-icon">📤</span>
                        <span class="btn-text">导出日志</span>
                    </button>

                    <button type="button" class="ai-director-panel__btn danger" id="ai-director-reset" data-action="reset">
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">重置</span>
                    </button>
                </div>
            </div>

            <!-- 快速设置 -->
            <div class="ai-director-panel__quick-settings">
                <div class="quick-setting">
                    <label for="ai-intensity">AI强度：</label>
                    <input type="range" id="ai-intensity" min="1" max="10" value="7" class="intensity-slider">
                    <span class="intensity-value">7</span>
                </div>

                <div class="quick-setting">
                    <label for="story-speed">故事节奏：</label>
                    <select id="story-speed" class="story-speed-select">
                        <option value="slow">缓慢</option>
                        <option value="normal" selected>正常</option>
                        <option value="fast">快速</option>
                        <option value="turbo">极速</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI导演设置对话框 -->
<div class="ai-director-settings-dialog" id="ai-director-settings-dialog">
    <div class="ai-director-settings-dialog__backdrop"></div>
    <div class="ai-director-settings-dialog__content">
        <div class="ai-director-settings-dialog__header">
            <h4>AI导演设置</h4>
            <button type="button" class="ai-director-settings-dialog__close">×</button>
        </div>
        <div class="ai-director-settings-dialog__body">
            <div class="settings-section">
                <h5>导演活跃度</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="tiandao-enabled" checked>
                        天道导演（世界事件）
                    </label>
                    <div class="setting-description">控制大型世界事件和剧情推进</div>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="diling-enabled" checked>
                        地灵导演（区域事件）
                    </label>
                    <div class="setting-description">管理门派、城市等区域性事件</div>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="qiling-enabled" checked>
                        器灵导演（个人事件）
                    </label>
                    <div class="setting-description">处理个人任务和互动事件</div>
                </div>
            </div>

            <div class="settings-section">
                <h5>推进频率</h5>
                <div class="setting-item">
                    <label for="story-frequency">故事推进频率：</label>
                    <select id="story-frequency">
                        <option value="low">低频（每10分钟）</option>
                        <option value="medium" selected>中频（每5分钟）</option>
                        <option value="high">高频（每2分钟）</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label for="hint-frequency">提示频率：</label>
                    <select id="hint-frequency">
                        <option value="low">低频</option>
                        <option value="medium" selected>中频</option>
                        <option value="high">高频</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h5>个性化设置</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="auto-hints" checked>
                        自动显示智能提示
                    </label>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="story-notifications" checked>
                        故事推进通知
                    </label>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="immersive-mode">
                        沉浸式模式（隐藏AI标识）
                    </label>
                </div>
            </div>
        </div>
        <div class="ai-director-settings-dialog__footer">
            <button type="button" class="btn btn-secondary" id="ai-settings-cancel">取消</button>
            <button type="button" class="btn" id="ai-settings-save">保存设置</button>
        </div>
    </div>
</div>

<script>
(function() {
    'use strict';

    // AI导演面板管理器
    window.AIDirectorPanel = {
        // 初始化
        init: function() {
            this.initializeProgressBars();
            this.bindEvents();
            this.loadSettings();
            this.startUpdateTimer();
            this.connectWebSocket();
        },

        // 初始化进度条
        initializeProgressBars: function() {
            // 从data-progress属性设置初始进度条宽度
            const progressElements = document.querySelectorAll('.progress-fill[data-progress]');
            progressElements.forEach(function(element) {
                const progress = element.dataset.progress;
                element.style.width = progress + '%';
            });
        },

        // 绑定事件
        bindEvents: function() {
            const self = this;

            // 展开/收起切换
            const toggleBtn = document.getElementById('ai-director-toggle');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    self.togglePanel();
                });
            }

            // 控制按钮事件
            document.addEventListener('click', function(e) {
                // AI导演控制按钮
                if (e.target.closest('.ai-director-panel__btn')) {
                    const btn = e.target.closest('.ai-director-panel__btn');
                    const action = btn.dataset.action;
                    self.handleControlAction(action);
                }

                // 故事控制按钮
                if (e.target.closest('.story-control-btn')) {
                    const btn = e.target.closest('.story-control-btn');
                    const btnId = btn.id;
                    self.handleStoryControl(btnId);
                }

                // 提示控制按钮
                if (e.target.closest('.hints-control-btn')) {
                    const btn = e.target.closest('.hints-control-btn');
                    const btnId = btn.id;
                    self.handleHintsControl(btnId);
                }

                // 提示执行按钮
                if (e.target.closest('.hint-action-btn')) {
                    const btn = e.target.closest('.hint-action-btn');
                    const command = btn.dataset.command;
                    const action = btn.dataset.action;
                    if (command) {
                        self.executeHintCommand(command);
                    } else if (action) {
                        self.handleHintAction(action, btn);
                    }
                }

                // 消息操作按钮
                if (e.target.closest('.message-action-btn')) {
                    const btn = e.target.closest('.message-action-btn');
                    const action = btn.dataset.action;
                    const message = btn.closest('.ai-director-message');
                    self.handleMessageAction(action, message);
                }
            });

            // 快速设置变化事件
            const intensitySlider = document.getElementById('ai-intensity');
            if (intensitySlider) {
                intensitySlider.addEventListener('input', function() {
                    document.querySelector('.intensity-value').textContent = this.value;
                    self.updateAIIntensity(this.value);
                });
            }

            const storySpeedSelect = document.getElementById('story-speed');
            if (storySpeedSelect) {
                storySpeedSelect.addEventListener('change', function() {
                    self.updateStorySpeed(this.value);
                });
            }

            // 设置对话框事件
            this.bindSettingsDialog();
        },

        // 切换面板展开状态
        togglePanel: function() {
            const content = document.getElementById('ai-director-content');
            const toggleIcon = document.querySelector('#ai-director-toggle .toggle-icon');

            if (content && toggleIcon) {
                const isCollapsed = content.style.display === 'none';
                content.style.display = isCollapsed ? 'block' : 'none';
                toggleIcon.textContent = isCollapsed ? '−' : '+';

                // 保存状态
                localStorage.setItem('aiDirectorPanelCollapsed', !isCollapsed);
            }
        },

        // 处理控制操作
        handleControlAction: function(action) {
            switch (action) {
                case 'pause':
                    this.toggleAIDirector();
                    break;
                case 'settings':
                    this.showSettingsDialog();
                    break;
                case 'manual':
                    this.triggerManualEvent();
                    break;
                case 'stats':
                    this.showStatsDialog();
                    break;
                case 'export':
                    this.exportLogs();
                    break;
                case 'reset':
                    this.resetAIDirector();
                    break;
            }
        },

        // 处理故事控制
        handleStoryControl: function(btnId) {
            switch (btnId) {
                case 'story-pause':
                    this.toggleStoryProgression();
                    break;
                case 'story-refresh':
                    this.refreshStory();
                    break;
                case 'story-history':
                    this.showStoryHistory();
                    break;
            }
        },

        // 处理提示控制
        handleHintsControl: function(btnId) {
            switch (btnId) {
                case 'hints-toggle':
                    this.toggleHints();
                    break;
                case 'hints-clear':
                    this.clearHints();
                    break;
            }
        },

        // 处理提示操作
        handleHintAction: function(action, btn) {
            const hint = btn.closest('.ai-director-hint');
            const hintId = hint.dataset.hintId || Date.now();

            switch (action) {
                case 'delay':
                    this.delayHint(hintId);
                    break;
                case 'dismiss':
                    this.dismissHint(hintId, hint);
                    break;
            }
        },

        // 处理消息操作
        handleMessageAction: function(action, messageElement) {
            const messageId = messageElement.dataset.messageId || Date.now();
            const messageType = messageElement.dataset.type;

            switch (action) {
                case 'investigate':
                    this.executeCommand('investigate');
                    this.markMessageHandled(messageElement);
                    break;
                case 'ignore':
                    this.dismissMessage(messageElement);
                    break;
                case 'join_team':
                    this.executeCommand('team join');
                    this.markMessageHandled(messageElement);
                    break;
                case 'solo_explore':
                    this.executeCommand('explore solo');
                    this.markMessageHandled(messageElement);
                    break;
                case 'meditate':
                    this.executeCommand('meditate');
                    this.markMessageHandled(messageElement);
                    break;
            }

            // 记录用户选择用于AI学习
            this.recordUserChoice(messageId, messageType, action);
        },

        // 切换AI导演状态
        toggleAIDirector: function() {
            const statusElement = document.getElementById('ai-director-status');
            const pauseBtn = document.getElementById('ai-director-pause');

            if (statusElement && pauseBtn) {
                const isRunning = statusElement.textContent === '运行中';
                statusElement.textContent = isRunning ? '已暂停' : '运行中';
                statusElement.className = isRunning ? 'ai-director-panel__status paused' : 'ai-director-panel__status';

                const btnText = pauseBtn.querySelector('.btn-text');
                const btnIcon = pauseBtn.querySelector('.btn-icon');
                if (btnText && btnIcon) {
                    btnText.textContent = isRunning ? '恢复' : '暂停';
                    btnIcon.textContent = isRunning ? '▶️' : '⏸️';
                }

                // 发送状态变更到服务器
                this.sendAIDirectorCommand('toggle', { paused: isRunning });
            }
        },

        // 显示设置对话框
        showSettingsDialog: function() {
            const dialog = document.getElementById('ai-director-settings-dialog');
            if (dialog) {
                dialog.style.display = 'block';
            }
        },

        // 触发手动事件
        triggerManualEvent: function() {
            this.sendAIDirectorCommand('manual_trigger', {});
            this.showNotification('已触发手动事件', 'info');
        },

        // 执行提示命令
        executeHintCommand: function(command) {
            this.executeCommand(command);
        },

        // 通用命令执行
        executeCommand: function(command) {
            const inputField = document.getElementById('inputfield');
            if (inputField && command) {
                inputField.value = command;

                // 触发发送事件
                const sendEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13
                });
                inputField.dispatchEvent(sendEvent);

                // 清空输入框
                setTimeout(function() {
                    inputField.value = '';
                }, 100);

                // 显示执行反馈
                this.showNotification('已执行命令: ' + command, 'info');
            }
        },

        // 切换故事推进
        toggleStoryProgression: function() {
            const btn = document.getElementById('story-pause');
            const icon = btn.querySelector('.btn-icon');
            const isRunning = icon.textContent === '⏸️';

            icon.textContent = isRunning ? '▶️' : '⏸️';
            btn.title = isRunning ? '恢复故事推进' : '暂停故事推进';

            this.sendAIDirectorCommand('toggle_story', { paused: isRunning });
            this.showNotification(isRunning ? '故事推进已暂停' : '故事推进已恢复', 'info');
        },

        // 刷新故事
        refreshStory: function() {
            this.sendAIDirectorCommand('refresh_story', {});
            this.showNotification('正在刷新故事内容...', 'info');

            // 添加加载动画
            const btn = document.getElementById('story-refresh');
            const icon = btn.querySelector('.btn-icon');
            const originalIcon = icon.textContent;
            icon.textContent = '⏳';

            setTimeout(function() {
                icon.textContent = originalIcon;
            }, 2000);
        },

        // 显示故事历史
        showStoryHistory: function() {
            this.sendAIDirectorCommand('get_story_history', {});
            // 这里可以打开一个模态框显示历史记录
            this.showNotification('正在加载故事历史...', 'info');
        },

        // 切换提示系统
        toggleHints: function() {
            const btn = document.getElementById('hints-toggle');
            const icon = btn.querySelector('.btn-icon');
            const isEnabled = icon.textContent === '🔔';

            icon.textContent = isEnabled ? '🔕' : '🔔';
            btn.title = isEnabled ? '开启智能提示' : '关闭智能提示';

            this.sendAIDirectorCommand('toggle_hints', { enabled: !isEnabled });
            this.showNotification(isEnabled ? '智能提示已关闭' : '智能提示已开启', 'info');
        },

        // 清空提示
        clearHints: function() {
            const hintList = document.getElementById('hint-list');
            if (hintList) {
                hintList.innerHTML = '<div class="no-hints">暂无智能提示</div>';
            }
            this.sendAIDirectorCommand('clear_hints', {});
            this.showNotification('已清空所有提示', 'info');
        },

        // 延迟提示
        delayHint: function(hintId) {
            this.sendAIDirectorCommand('delay_hint', { hint_id: hintId, delay: 300000 }); // 5分钟后再提醒
            this.showNotification('已设置5分钟后再次提醒', 'info');
        },

        // 忽略提示
        dismissHint: function(hintId, hintElement) {
            if (hintElement) {
                hintElement.style.opacity = '0.5';
                hintElement.style.pointerEvents = 'none';
                setTimeout(function() {
                    hintElement.remove();
                }, 300);
            }
            this.sendAIDirectorCommand('dismiss_hint', { hint_id: hintId });
        },

        // 标记消息已处理
        markMessageHandled: function(messageElement) {
            if (messageElement) {
                messageElement.classList.add('handled');
                const actions = messageElement.querySelector('.message-actions');
                if (actions) {
                    actions.innerHTML = '<span class="handled-indicator">✓ 已处理</span>';
                }
            }
        },

        // 忽略消息
        dismissMessage: function(messageElement) {
            if (messageElement) {
                messageElement.style.opacity = '0.5';
                setTimeout(function() {
                    messageElement.remove();
                }, 300);
            }
        },

        // 记录用户选择
        recordUserChoice: function(messageId, messageType, action) {
            this.sendAIDirectorCommand('record_choice', {
                message_id: messageId,
                message_type: messageType,
                user_action: action,
                timestamp: Date.now()
            });
        },

        // 更新AI强度
        updateAIIntensity: function(intensity) {
            this.sendAIDirectorCommand('update_intensity', { intensity: parseInt(intensity) });
            this.showNotification('AI强度已调整为: ' + intensity, 'info');
        },

        // 更新故事节奏
        updateStorySpeed: function(speed) {
            this.sendAIDirectorCommand('update_story_speed', { speed: speed });
            this.showNotification('故事节奏已调整为: ' + speed, 'info');
        },

        // 显示统计对话框
        showStatsDialog: function() {
            this.sendAIDirectorCommand('get_stats', {});
            this.showNotification('正在加载统计数据...', 'info');
        },

        // 导出日志
        exportLogs: function() {
            this.sendAIDirectorCommand('export_logs', {});
            this.showNotification('正在准备日志文件...', 'info');
        },

        // 重置AI导演
        resetAIDirector: function() {
            if (confirm('确定要重置AI导演系统吗？这将清除所有历史数据和设置。')) {
                this.sendAIDirectorCommand('reset_system', {});
                this.showNotification('AI导演系统正在重置...', 'warning');

                // 重置界面状态
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }
        },

        // 更新消息列表
        updateMessages: function(messages) {
            const messagesContainer = document.getElementById('ai-director-messages');
            if (!messagesContainer || !messages) return;

            // 清空现有消息
            messagesContainer.innerHTML = '';

            // 添加新消息（最多显示5条）
            const self = this;
            messages.slice(-5).forEach(function(message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'ai-director-message';
                messageElement.dataset.type = message.type;
                messageElement.dataset.priority = message.priority || 'medium';
                messageElement.dataset.messageId = message.id;

                messageElement.innerHTML = `
                    <div class="message-header">
                        <span class="message-icon">${self.getDirectorIcon(message.type)}</span>
                        <span class="message-source">${self.getDirectorName(message.type)}</span>
                        <span class="message-time">${self.formatTime(message.timestamp)}</span>
                        <span class="message-priority ${message.priority || 'medium'}">${self.getPriorityText(message.priority)}</span>
                    </div>
                    <div class="message-content">${message.text}</div>
                    <div class="message-actions">
                        ${self.generateMessageActions(message.actions || [])}
                    </div>
                `;
                messagesContainer.appendChild(messageElement);
            });

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        },

        // 生成消息操作按钮
        generateMessageActions: function(actions) {
            if (!actions || actions.length === 0) return '';

            return actions.map(action =>
                `<button type="button" class="message-action-btn" data-action="${action.id}">${action.text}</button>`
            ).join('');
        },

        // 获取导演名称
        getDirectorName: function(type) {
            const names = {
                'tiandao': '天道',
                'diling': '地灵',
                'qiling': '器灵'
            };
            return names[type] || 'AI导演';
        },

        // 获取优先级文本
        getPriorityText: function(priority) {
            const texts = {
                'high': '重要',
                'medium': '一般',
                'low': '提示'
            };
            return texts[priority] || '一般';
        },

        // 更新智能提示
        updateHints: function(hints) {
            const hintList = document.getElementById('hint-list');
            if (!hintList || !hints) return;

            hintList.innerHTML = '';

            const self = this;
            hints.forEach(function(hint) {
                const hintElement = document.createElement('div');
                hintElement.className = 'ai-director-hint';
                hintElement.dataset.priority = hint.priority || 'medium';
                hintElement.dataset.category = hint.category || 'general';
                hintElement.dataset.hintId = hint.id;

                hintElement.innerHTML = `
                    <div class="hint-header">
                        <span class="hint-icon">${self.getHintIcon(hint.category)}</span>
                        <span class="hint-priority ${hint.priority}">${self.getPriorityText(hint.priority)}</span>
                        <span class="hint-category">${self.getCategoryText(hint.category)}</span>
                    </div>
                    <div class="hint-content">
                        <span class="hint-text">${hint.text}</span>
                        ${hint.details ? `<div class="hint-details">${hint.details}</div>` : ''}
                    </div>
                    <div class="hint-actions">
                        ${self.generateHintActions(hint.actions || [])}
                    </div>
                `;
                hintList.appendChild(hintElement);
            });

            // 更新提示统计
            this.updateHintsStats(hints);
        },

        // 生成提示操作按钮
        generateHintActions: function(actions) {
            if (!actions || actions.length === 0) return '';

            return actions.map(action => {
                const btnClass = action.type === 'primary' ? 'hint-action-btn primary' :
                               action.type === 'secondary' ? 'hint-action-btn secondary' :
                               'hint-action-btn';

                const dataAttr = action.command ? `data-command="${action.command}"` :
                               action.action ? `data-action="${action.action}"` : '';

                return `<button type="button" class="${btnClass}" ${dataAttr}>${action.text}</button>`;
            }).join('');
        },

        // 获取提示图标
        getHintIcon: function(category) {
            const icons = {
                'cultivation': '🔥',
                'social': '👥',
                'exploration': '🗺️',
                'combat': '⚔️',
                'trade': '💰',
                'quest': '📜',
                'general': '💡'
            };
            return icons[category] || '💡';
        },

        // 获取分类文本
        getCategoryText: function(category) {
            const texts = {
                'cultivation': '修炼',
                'social': '社交',
                'exploration': '探索',
                'combat': '战斗',
                'trade': '交易',
                'quest': '任务',
                'general': '通用'
            };
            return texts[category] || '通用';
        },

        // 更新提示统计
        updateHintsStats: function(hints) {
            const todayElement = document.getElementById('hints-today');
            const executedElement = document.getElementById('hints-executed');
            const successRateElement = document.getElementById('hints-success-rate');

            if (todayElement) todayElement.textContent = hints.length;

            // 这些数据应该从服务器获取
            const stats = this.getHintsStatsFromStorage();
            if (executedElement) executedElement.textContent = stats.executed;
            if (successRateElement) successRateElement.textContent = stats.successRate + '%';
        },

        // 从本地存储获取提示统计
        getHintsStatsFromStorage: function() {
            const stats = JSON.parse(localStorage.getItem('hintsStats') || '{}');
            return {
                executed: stats.executed || 0,
                successRate: stats.successRate || 0
            };
        },

        // 更新导演状态
        updateDirectorStatus: function(status) {
            const self = this;
            ['tiandao', 'diling', 'qiling'].forEach(function(director) {
                const statusElement = document.getElementById(director + '-status');
                const progressElement = document.getElementById(director + '-progress');
                const nextElement = document.getElementById(director + '-next');
                const lastElement = document.getElementById(director + '-last');

                if (statusElement && status[director]) {
                    const directorStatus = status[director];

                    // 更新状态
                    statusElement.dataset.status = directorStatus.status || 'active';
                    statusElement.querySelector('.status-text').textContent = self.getStatusText(directorStatus.status);

                    // 更新进度条
                    if (progressElement && directorStatus.progress !== undefined) {
                        progressElement.style.width = directorStatus.progress + '%';
                    }

                    // 更新下次执行时间
                    if (nextElement && directorStatus.nextExecution) {
                        nextElement.textContent = '下次: ' + self.formatDuration(directorStatus.nextExecution);
                    }

                    // 更新最近操作
                    if (lastElement && directorStatus.lastAction) {
                        lastElement.textContent = '最近: ' + directorStatus.lastAction;
                    }
                }
            });
        },

        // 获取状态文本
        getStatusText: function(status) {
            const texts = {
                'active': '活跃',
                'idle': '空闲',
                'paused': '暂停',
                'error': '错误'
            };
            return texts[status] || '未知';
        },

        // 格式化持续时间
        formatDuration: function(seconds) {
            if (seconds < 60) return seconds + '秒';
            if (seconds < 3600) return Math.floor(seconds / 60) + '分钟';
            return Math.floor(seconds / 3600) + '小时';
        },

        // 获取导演图标
        getDirectorIcon: function(type) {
            const icons = {
                'tiandao': '🌌',
                'diling': '🏔️',
                'qiling': '⚡'
            };
            return icons[type] || '🎭';
        },

        // 格式化时间
        formatTime: function(timestamp) {
            if (!timestamp) return '刚刚';

            const now = Date.now();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / 60000);

            if (minutes < 1) return '刚刚';
            if (minutes < 60) return minutes + '分钟前';

            const hours = Math.floor(minutes / 60);
            if (hours < 24) return hours + '小时前';

            const days = Math.floor(hours / 24);
            return days + '天前';
        },

        // 发送AI导演命令
        sendAIDirectorCommand: function(command, data) {
            if (window.Evennia && window.Evennia.msg) {
                window.Evennia.msg({
                    type: 'ai_director_command',
                    command: command,
                    data: data
                });
            }
        },

        // 显示通知
        showNotification: function(message, type) {
            if (window.XiuxianClient && window.XiuxianClient.showNotification) {
                window.XiuxianClient.showNotification(message, type);
            }
        },

        // 连接WebSocket
        connectWebSocket: function() {
            const self = this;

            // 监听AI导演消息
            if (window.Evennia && window.Evennia.msg) {
                const originalMsg = window.Evennia.msg;
                window.Evennia.msg = function(data) {
                    originalMsg.call(this, data);

                    if (data.type === 'ai_director_update') {
                        self.handleAIDirectorUpdate(data);
                    }
                };
            }
        },

        // 处理AI导演更新
        handleAIDirectorUpdate: function(data) {
            try {
                if (data.messages) {
                    this.updateMessages(data.messages);
                }

                if (data.hints) {
                    this.updateHints(data.hints);
                }

                if (data.status) {
                    this.updateDirectorStatus(data.status);
                }

                if (data.stats) {
                    this.updateStats(data.stats);
                }

                if (data.notification) {
                    this.showNotification(data.notification.message, data.notification.type);
                }

                // 更新最后更新时间
                this.lastUpdateTime = Date.now();

            } catch (error) {
                console.error('处理AI导演更新时出错:', error);
                this.showNotification('更新数据时出现错误', 'error');
            }
        },

        // 更新统计数据
        updateStats: function(stats) {
            // 更新提示统计
            if (stats.hints) {
                const todayElement = document.getElementById('hints-today');
                const executedElement = document.getElementById('hints-executed');
                const successRateElement = document.getElementById('hints-success-rate');

                if (todayElement) todayElement.textContent = stats.hints.today || 0;
                if (executedElement) executedElement.textContent = stats.hints.executed || 0;
                if (successRateElement) successRateElement.textContent = (stats.hints.successRate || 0) + '%';

                // 保存到本地存储
                localStorage.setItem('hintsStats', JSON.stringify(stats.hints));
            }

            // 更新导演统计
            if (stats.directors) {
                this.updateDirectorStats(stats.directors);
            }
        },

        // 更新导演统计
        updateDirectorStats: function(directorStats) {
            ['tiandao', 'diling', 'qiling'].forEach(function(director) {
                const indicator = document.getElementById(director + '-indicator');
                if (indicator && directorStats[director]) {
                    const stats = directorStats[director];

                    // 可以在这里添加更多统计信息的显示
                    if (stats.eventsToday) {
                        indicator.dataset.eventsToday = stats.eventsToday;
                    }
                }
            });
        },

        // 定时更新
        startUpdateTimer: function() {
            const self = this;

            // 每30秒请求一次更新
            this.updateInterval = setInterval(function() {
                self.sendAIDirectorCommand('request_update', {});
            }, 30000);

            // 每秒更新进度条
            this.progressInterval = setInterval(function() {
                self.updateProgressBars();
            }, 1000);

            // 每5秒检查连接状态
            this.connectionInterval = setInterval(function() {
                self.checkConnectionStatus();
            }, 5000);
        },

        // 更新进度条
        updateProgressBars: function() {
            const directors = ['tiandao', 'diling', 'qiling'];
            const cycles = { tiandao: 300, diling: 60, qiling: 10 }; // 秒

            directors.forEach(function(director) {
                const progressElement = document.getElementById(director + '-progress');
                const nextElement = document.getElementById(director + '-next');

                if (progressElement && nextElement) {
                    // 这里应该从实际的导演状态获取数据
                    // 现在使用模拟数据
                    const cycle = cycles[director];
                    const elapsed = (Date.now() / 1000) % cycle;
                    const progress = (elapsed / cycle) * 100;
                    const remaining = cycle - elapsed;

                    progressElement.style.width = progress + '%';
                    nextElement.textContent = '下次: ' + this.formatDuration(Math.floor(remaining));
                }
            }.bind(this));
        },

        // 检查连接状态
        checkConnectionStatus: function() {
            const statusElement = document.getElementById('ai-director-status');
            if (!statusElement) return;

            const now = Date.now();
            const timeSinceLastUpdate = now - (this.lastUpdateTime || now);

            // 如果超过2分钟没有更新，标记为连接问题
            if (timeSinceLastUpdate > 120000) {
                statusElement.dataset.status = 'disconnected';
                statusElement.querySelector('.status-text').textContent = '连接中断';
                this.showNotification('AI导演连接中断，正在尝试重连...', 'warning');

                // 尝试重连
                this.reconnectWebSocket();
            }
        },

        // 重连WebSocket
        reconnectWebSocket: function() {
            this.sendAIDirectorCommand('ping', {});
        },

        // 停止定时器
        stopTimers: function() {
            if (this.updateInterval) {
                clearInterval(this.updateInterval);
                this.updateInterval = null;
            }

            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }

            if (this.connectionInterval) {
                clearInterval(this.connectionInterval);
                this.connectionInterval = null;
            }
        },

        // 加载设置
        loadSettings: function() {
            const settings = JSON.parse(localStorage.getItem('aiDirectorSettings') || '{}');

            // 应用设置到界面
            Object.keys(settings).forEach(function(key) {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = settings[key];
                    } else {
                        element.value = settings[key];
                    }
                }
            });

            // 恢复面板状态
            const isCollapsed = localStorage.getItem('aiDirectorPanelCollapsed') === 'true';
            if (isCollapsed) {
                this.togglePanel();
            }
        },

        // 绑定设置对话框事件
        bindSettingsDialog: function() {
            const self = this;
            const dialog = document.getElementById('ai-director-settings-dialog');

            if (!dialog) return;

            // 关闭对话框
            const closeBtn = dialog.querySelector('.ai-director-settings-dialog__close');
            const cancelBtn = document.getElementById('ai-settings-cancel');
            const backdrop = dialog.querySelector('.ai-director-settings-dialog__backdrop');

            [closeBtn, cancelBtn, backdrop].forEach(function(element) {
                if (element) {
                    element.addEventListener('click', function() {
                        dialog.style.display = 'none';
                    });
                }
            });

            // 保存设置
            const saveBtn = document.getElementById('ai-settings-save');
            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    self.saveSettings();
                    dialog.style.display = 'none';
                });
            }
        },

        // 保存设置
        saveSettings: function() {
            const settings = {};
            const settingElements = document.querySelectorAll('#ai-director-settings-dialog input, #ai-director-settings-dialog select');

            settingElements.forEach(function(element) {
                if (element.type === 'checkbox') {
                    settings[element.id] = element.checked;
                } else {
                    settings[element.id] = element.value;
                }
            });

            localStorage.setItem('aiDirectorSettings', JSON.stringify(settings));

            // 发送设置到服务器
            this.sendAIDirectorCommand('update_settings', settings);
            this.showNotification('设置已保存', 'success');
        },

        // 销毁组件
        destroy: function() {
            this.stopTimers();

            // 移除事件监听器
            const toggleBtn = document.getElementById('ai-director-toggle');
            if (toggleBtn) {
                toggleBtn.removeEventListener('click', this.togglePanel);
            }

            // 清理WebSocket监听
            if (window.Evennia && this.originalEvenniaMsg) {
                window.Evennia.msg = this.originalEvenniaMsg;
            }

            // 清理本地存储（可选）
            // localStorage.removeItem('aiDirectorSettings');
            // localStorage.removeItem('aiDirectorPanelCollapsed');
            // localStorage.removeItem('hintsStats');
        },

        // 重置到默认状态
        resetToDefaults: function() {
            // 重置界面状态
            const statusElement = document.getElementById('ai-director-status');
            if (statusElement) {
                statusElement.dataset.status = 'active';
                statusElement.querySelector('.status-text').textContent = '运行中';
            }

            // 重置导演状态
            ['tiandao', 'diling', 'qiling'].forEach(function(director) {
                const statusElement = document.getElementById(director + '-status');
                const progressElement = document.getElementById(director + '-progress');

                if (statusElement) {
                    statusElement.dataset.status = 'active';
                    statusElement.querySelector('.status-text').textContent = '活跃';
                }

                if (progressElement) {
                    progressElement.style.width = '0%';
                }
            });

            // 清空消息和提示
            const messagesContainer = document.getElementById('ai-director-messages');
            const hintsContainer = document.getElementById('hint-list');

            if (messagesContainer) {
                messagesContainer.innerHTML = '<div class="no-messages">暂无消息</div>';
            }

            if (hintsContainer) {
                hintsContainer.innerHTML = '<div class="no-hints">暂无智能提示</div>';
            }

            // 重置设置
            const intensitySlider = document.getElementById('ai-intensity');
            const storySpeedSelect = document.getElementById('story-speed');

            if (intensitySlider) {
                intensitySlider.value = 7;
                document.querySelector('.intensity-value').textContent = '7';
            }

            if (storySpeedSelect) {
                storySpeedSelect.value = 'normal';
            }
        },

        // 获取当前状态
        getCurrentState: function() {
            return {
                isCollapsed: document.getElementById('ai-director-content').style.display === 'none',
                aiStatus: document.getElementById('ai-director-status').dataset.status,
                settings: JSON.parse(localStorage.getItem('aiDirectorSettings') || '{}'),
                lastUpdateTime: this.lastUpdateTime,
                version: '2.0'
            };
        },

        // 调试信息
        getDebugInfo: function() {
            return {
                state: this.getCurrentState(),
                timers: {
                    updateInterval: !!this.updateInterval,
                    progressInterval: !!this.progressInterval,
                    connectionInterval: !!this.connectionInterval
                },
                lastUpdateTime: this.lastUpdateTime,
                version: '2.0'
            };
        }
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            AIDirectorPanel.init();
        });
    } else {
        AIDirectorPanel.init();
    }
})();
</script>