{% comment %}
AI导演面板组件模板

功能：
- 显示AI导演状态
- 实时故事推进
- 导演控制面板
- 智能提示系统

集成系统：
- 天道导演（世界事件）
- 地灵导演（区域事件）
- 器灵导演（个人事件）
{% endcomment %}

<div class="ai-director-panel" id="ai-director-panel">
    <div class="ai-director-panel__header">
        <span class="ai-director-panel__title">🎭 AI导演</span>
        <span class="ai-director-panel__status" id="ai-director-status">运行中</span>
        <button class="ai-director-panel__toggle" id="ai-director-toggle" title="展开/收起">
            <span class="toggle-icon">−</span>
        </button>
    </div>

    <div class="ai-director-panel__content" id="ai-director-content">
        <!-- 导演状态指示器 -->
        <div class="ai-director-panel__indicators">
            <div class="ai-director-indicator" data-director="tiandao">
                <span class="ai-director-indicator__icon">🌌</span>
                <span class="ai-director-indicator__name">天道</span>
                <span class="ai-director-indicator__status" id="tiandao-status">活跃</span>
            </div>

            <div class="ai-director-indicator" data-director="diling">
                <span class="ai-director-indicator__icon">🏔️</span>
                <span class="ai-director-indicator__name">地灵</span>
                <span class="ai-director-indicator__status" id="diling-status">活跃</span>
            </div>

            <div class="ai-director-indicator" data-director="qiling">
                <span class="ai-director-indicator__icon">⚡</span>
                <span class="ai-director-indicator__name">器灵</span>
                <span class="ai-director-indicator__status" id="qiling-status">活跃</span>
            </div>
        </div>

        <!-- 最新故事推进 -->
        <div class="ai-director-panel__story">
            <div class="ai-director-panel__story-title">最新推进</div>
            <div class="ai-director-panel__messages" id="ai-director-messages">
                <div class="ai-director-panel__message" data-type="tiandao">
                    <span class="message-icon">🌌</span>
                    <span class="message-text">天道感应到修仙界的灵气波动...</span>
                    <span class="message-time">刚刚</span>
                </div>

                <div class="ai-director-panel__message" data-type="diling">
                    <span class="message-icon">🏔️</span>
                    <span class="message-text">青云山脉出现异象，引起各门派关注</span>
                    <span class="message-time">2分钟前</span>
                </div>

                <div class="ai-director-panel__message" data-type="qiling">
                    <span class="message-icon">⚡</span>
                    <span class="message-text">你感受到一股神秘的力量在召唤...</span>
                    <span class="message-time">5分钟前</span>
                </div>
            </div>
        </div>

        <!-- 智能提示 -->
        <div class="ai-director-panel__hints" id="ai-director-hints">
            <div class="ai-director-panel__hints-title">智能提示</div>
            <div class="ai-director-panel__hint-list" id="hint-list">
                <div class="ai-director-hint" data-priority="high">
                    <span class="hint-icon">💡</span>
                    <span class="hint-text">建议前往练功房提升修为</span>
                    <button class="hint-action" data-command="go 练功房">执行</button>
                </div>

                <div class="ai-director-hint" data-priority="medium">
                    <span class="hint-icon">📚</span>
                    <span class="hint-text">可以学习新的法术技能</span>
                    <button class="hint-action" data-command="skills">查看</button>
                </div>
            </div>
        </div>

        <!-- 导演控制 -->
        <div class="ai-director-panel__controls">
            <button class="ai-director-panel__btn" id="ai-director-pause" data-action="pause">
                <span class="btn-icon">⏸️</span>
                <span class="btn-text">暂停</span>
            </button>

            <button class="ai-director-panel__btn" id="ai-director-settings" data-action="settings">
                <span class="btn-icon">⚙️</span>
                <span class="btn-text">设置</span>
            </button>

            <button class="ai-director-panel__btn" id="ai-director-manual" data-action="manual">
                <span class="btn-icon">✋</span>
                <span class="btn-text">手动</span>
            </button>
        </div>
    </div>
</div>

<!-- AI导演设置对话框 -->
<div class="ai-director-settings-dialog" id="ai-director-settings-dialog" style="display: none;">
    <div class="ai-director-settings-dialog__backdrop"></div>
    <div class="ai-director-settings-dialog__content">
        <div class="ai-director-settings-dialog__header">
            <h4>AI导演设置</h4>
            <button class="ai-director-settings-dialog__close">×</button>
        </div>
        <div class="ai-director-settings-dialog__body">
            <div class="settings-section">
                <h5>导演活跃度</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="tiandao-enabled" checked>
                        天道导演（世界事件）
                    </label>
                    <div class="setting-description">控制大型世界事件和剧情推进</div>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="diling-enabled" checked>
                        地灵导演（区域事件）
                    </label>
                    <div class="setting-description">管理门派、城市等区域性事件</div>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="qiling-enabled" checked>
                        器灵导演（个人事件）
                    </label>
                    <div class="setting-description">处理个人任务和互动事件</div>
                </div>
            </div>

            <div class="settings-section">
                <h5>推进频率</h5>
                <div class="setting-item">
                    <label for="story-frequency">故事推进频率：</label>
                    <select id="story-frequency">
                        <option value="low">低频（每10分钟）</option>
                        <option value="medium" selected>中频（每5分钟）</option>
                        <option value="high">高频（每2分钟）</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label for="hint-frequency">提示频率：</label>
                    <select id="hint-frequency">
                        <option value="low">低频</option>
                        <option value="medium" selected>中频</option>
                        <option value="high">高频</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h5>个性化设置</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="auto-hints" checked>
                        自动显示智能提示
                    </label>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="story-notifications" checked>
                        故事推进通知
                    </label>
                </div>

                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="immersive-mode">
                        沉浸式模式（隐藏AI标识）
                    </label>
                </div>
            </div>
        </div>
        <div class="ai-director-settings-dialog__footer">
            <button class="btn btn-secondary" id="ai-settings-cancel">取消</button>
            <button class="btn" id="ai-settings-save">保存设置</button>
        </div>
    </div>
</div>

<script>
(function() {
    'use strict';

    // AI导演面板管理器
    window.AIDirectorPanel = {
        // 初始化
        init: function() {
            this.bindEvents();
            this.loadSettings();
            this.startUpdateTimer();
            this.connectWebSocket();
        },

        // 绑定事件
        bindEvents: function() {
            const self = this;

            // 展开/收起切换
            const toggleBtn = document.getElementById('ai-director-toggle');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    self.togglePanel();
                });
            }

            // 控制按钮事件
            document.addEventListener('click', function(e) {
                if (e.target.closest('.ai-director-panel__btn')) {
                    const btn = e.target.closest('.ai-director-panel__btn');
                    const action = btn.dataset.action;
                    self.handleControlAction(action);
                }

                // 提示执行按钮
                if (e.target.closest('.hint-action')) {
                    const btn = e.target.closest('.hint-action');
                    const command = btn.dataset.command;
                    self.executeHintCommand(command);
                }
            });

            // 设置对话框事件
            this.bindSettingsDialog();
        },

        // 切换面板展开状态
        togglePanel: function() {
            const content = document.getElementById('ai-director-content');
            const toggleIcon = document.querySelector('#ai-director-toggle .toggle-icon');

            if (content && toggleIcon) {
                const isCollapsed = content.style.display === 'none';
                content.style.display = isCollapsed ? 'block' : 'none';
                toggleIcon.textContent = isCollapsed ? '−' : '+';

                // 保存状态
                localStorage.setItem('aiDirectorPanelCollapsed', !isCollapsed);
            }
        },

        // 处理控制操作
        handleControlAction: function(action) {
            switch (action) {
                case 'pause':
                    this.toggleAIDirector();
                    break;
                case 'settings':
                    this.showSettingsDialog();
                    break;
                case 'manual':
                    this.triggerManualEvent();
                    break;
            }
        },

        // 切换AI导演状态
        toggleAIDirector: function() {
            const statusElement = document.getElementById('ai-director-status');
            const pauseBtn = document.getElementById('ai-director-pause');

            if (statusElement && pauseBtn) {
                const isRunning = statusElement.textContent === '运行中';
                statusElement.textContent = isRunning ? '已暂停' : '运行中';
                statusElement.className = isRunning ? 'ai-director-panel__status paused' : 'ai-director-panel__status';

                const btnText = pauseBtn.querySelector('.btn-text');
                const btnIcon = pauseBtn.querySelector('.btn-icon');
                if (btnText && btnIcon) {
                    btnText.textContent = isRunning ? '恢复' : '暂停';
                    btnIcon.textContent = isRunning ? '▶️' : '⏸️';
                }

                // 发送状态变更到服务器
                this.sendAIDirectorCommand('toggle', { paused: isRunning });
            }
        },

        // 显示设置对话框
        showSettingsDialog: function() {
            const dialog = document.getElementById('ai-director-settings-dialog');
            if (dialog) {
                dialog.style.display = 'block';
            }
        },

        // 触发手动事件
        triggerManualEvent: function() {
            this.sendAIDirectorCommand('manual_trigger', {});
            this.showNotification('已触发手动事件', 'info');
        },

        // 执行提示命令
        executeHintCommand: function(command) {
            const inputField = document.getElementById('inputfield');
            if (inputField && command) {
                inputField.value = command;

                // 触发发送事件
                const sendEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    keyCode: 13,
                    which: 13
                });
                inputField.dispatchEvent(sendEvent);

                // 清空输入框
                setTimeout(function() {
                    inputField.value = '';
                }, 100);
            }
        },

        // 更新消息列表
        updateMessages: function(messages) {
            const messagesContainer = document.getElementById('ai-director-messages');
            if (!messagesContainer || !messages) return;

            // 清空现有消息
            messagesContainer.innerHTML = '';

            // 添加新消息（最多显示5条）
            messages.slice(-5).forEach(function(message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'ai-director-panel__message';
                messageElement.dataset.type = message.type;
                messageElement.innerHTML = `
                    <span class="message-icon">${self.getDirectorIcon(message.type)}</span>
                    <span class="message-text">${message.text}</span>
                    <span class="message-time">${self.formatTime(message.timestamp)}</span>
                `;
                messagesContainer.appendChild(messageElement);
            });

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        },

        // 更新智能提示
        updateHints: function(hints) {
            const hintList = document.getElementById('hint-list');
            if (!hintList || !hints) return;

            hintList.innerHTML = '';

            hints.forEach(function(hint) {
                const hintElement = document.createElement('div');
                hintElement.className = 'ai-director-hint';
                hintElement.dataset.priority = hint.priority;
                hintElement.innerHTML = `
                    <span class="hint-icon">💡</span>
                    <span class="hint-text">${hint.text}</span>
                    <button class="hint-action" data-command="${hint.command}">执行</button>
                `;
                hintList.appendChild(hintElement);
            });
        },

        // 更新导演状态
        updateDirectorStatus: function(status) {
            ['tiandao', 'diling', 'qiling'].forEach(function(director) {
                const statusElement = document.getElementById(director + '-status');
                if (statusElement && status[director]) {
                    statusElement.textContent = status[director];
                    statusElement.className = 'ai-director-indicator__status ' + status[director].toLowerCase();
                }
            });
        },

        // 获取导演图标
        getDirectorIcon: function(type) {
            const icons = {
                'tiandao': '🌌',
                'diling': '🏔️',
                'qiling': '⚡'
            };
            return icons[type] || '🎭';
        },

        // 格式化时间
        formatTime: function(timestamp) {
            if (!timestamp) return '刚刚';

            const now = Date.now();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / 60000);

            if (minutes < 1) return '刚刚';
            if (minutes < 60) return minutes + '分钟前';

            const hours = Math.floor(minutes / 60);
            if (hours < 24) return hours + '小时前';

            const days = Math.floor(hours / 24);
            return days + '天前';
        },

        // 发送AI导演命令
        sendAIDirectorCommand: function(command, data) {
            if (window.Evennia && window.Evennia.msg) {
                window.Evennia.msg({
                    type: 'ai_director_command',
                    command: command,
                    data: data
                });
            }
        },

        // 显示通知
        showNotification: function(message, type) {
            if (window.XiuxianClient && window.XiuxianClient.showNotification) {
                window.XiuxianClient.showNotification(message, type);
            }
        },

        // 连接WebSocket
        connectWebSocket: function() {
            const self = this;

            // 监听AI导演消息
            if (window.Evennia && window.Evennia.msg) {
                const originalMsg = window.Evennia.msg;
                window.Evennia.msg = function(data) {
                    originalMsg.call(this, data);

                    if (data.type === 'ai_director_update') {
                        self.handleAIDirectorUpdate(data);
                    }
                };
            }
        },

        // 处理AI导演更新
        handleAIDirectorUpdate: function(data) {
            if (data.messages) {
                this.updateMessages(data.messages);
            }

            if (data.hints) {
                this.updateHints(data.hints);
            }

            if (data.status) {
                this.updateDirectorStatus(data.status);
            }
        },

        // 定时更新
        startUpdateTimer: function() {
            const self = this;

            // 每30秒请求一次更新
            setInterval(function() {
                self.sendAIDirectorCommand('request_update', {});
            }, 30000);
        },

        // 加载设置
        loadSettings: function() {
            const settings = JSON.parse(localStorage.getItem('aiDirectorSettings') || '{}');

            // 应用设置到界面
            Object.keys(settings).forEach(function(key) {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = settings[key];
                    } else {
                        element.value = settings[key];
                    }
                }
            });

            // 恢复面板状态
            const isCollapsed = localStorage.getItem('aiDirectorPanelCollapsed') === 'true';
            if (isCollapsed) {
                this.togglePanel();
            }
        },

        // 绑定设置对话框事件
        bindSettingsDialog: function() {
            const self = this;
            const dialog = document.getElementById('ai-director-settings-dialog');

            if (!dialog) return;

            // 关闭对话框
            const closeBtn = dialog.querySelector('.ai-director-settings-dialog__close');
            const cancelBtn = document.getElementById('ai-settings-cancel');
            const backdrop = dialog.querySelector('.ai-director-settings-dialog__backdrop');

            [closeBtn, cancelBtn, backdrop].forEach(function(element) {
                if (element) {
                    element.addEventListener('click', function() {
                        dialog.style.display = 'none';
                    });
                }
            });

            // 保存设置
            const saveBtn = document.getElementById('ai-settings-save');
            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    self.saveSettings();
                    dialog.style.display = 'none';
                });
            }
        },

        // 保存设置
        saveSettings: function() {
            const settings = {};
            const settingElements = document.querySelectorAll('#ai-director-settings-dialog input, #ai-director-settings-dialog select');

            settingElements.forEach(function(element) {
                if (element.type === 'checkbox') {
                    settings[element.id] = element.checked;
                } else {
                    settings[element.id] = element.value;
                }
            });

            localStorage.setItem('aiDirectorSettings', JSON.stringify(settings));

            // 发送设置到服务器
            this.sendAIDirectorCommand('update_settings', settings);
            this.showNotification('设置已保存', 'success');
        }
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            AIDirectorPanel.init();
        });
    } else {
        AIDirectorPanel.init();
    }
})();
</script>