"""
仙侠战斗命令集

基于Evennia TurnBattle命令系统的仙侠战斗命令。
提供技能使用、境界查看、五行信息等仙侠特色功能。
"""

from evennia.contrib.game_systems.turnbattle.tb_basic import BattleCmdSet
from evennia import Command
from evennia.utils import logger
from ..systems.xiuxian_skill_system import get_skill_system
from ..systems.wuxing_calculator import get_wuxing_calculator
from ..systems.combat_event_publisher import get_combat_event_publisher


class CmdUseSkill(Command):
    """
    使用仙侠技能
    
    用法:
        skill <技能名称> [目标]
        skill 基础剑法 敌人
        skill 灵力护体
    """
    
    key = "skill"
    aliases = ["技能", "sk"]
    help_category = "战斗"
    
    def func(self):
        """执行技能使用命令"""
        if not self.args:
            self.caller.msg("用法: skill <技能名称> [目标]")
            return
        
        args = self.args.split()
        skill_name = args[0]
        target = None
        
        # 解析目标
        if len(args) > 1:
            target_name = args[1]
            target = self.caller.search(target_name, location=self.caller.location)
            if not target:
                return
        
        # 检查是否在战斗中
        if not hasattr(self.caller, 'ndb') or not self.caller.ndb.combat_handler:
            self.caller.msg("你不在战斗中。")
            return
        
        # 检查是否是仙侠战斗角色
        if not hasattr(self.caller, 'use_xiuxian_skill'):
            self.caller.msg("你无法使用仙侠技能。")
            return
        
        # 使用技能
        result = self.caller.use_xiuxian_skill(skill_name, target)
        
        if result.get("success", False):
            # 成功使用技能
            effects = result.get("effects", [])
            msg = f"你使用了 {skill_name}"
            
            if target:
                msg += f" 对 {target.key}"
            
            # 显示效果
            for effect in effects:
                effect_type = effect.get("type")
                value = effect.get("value", 0)
                
                if effect_type == "damage":
                    wuxing_bonus = effect.get("wuxing_bonus", 1.0)
                    if wuxing_bonus > 1.0:
                        msg += f"\n|r{target.key} 受到 {value} 点伤害！(五行相克加成)|n"
                    elif wuxing_bonus < 1.0:
                        msg += f"\n|y{target.key} 受到 {value} 点伤害。(五行被克减免)|n"
                    else:
                        msg += f"\n{target.key} 受到 {value} 点伤害。"
                
                elif effect_type == "defense":
                    duration = effect.get("duration", 1)
                    msg += f"\n|g你的防御力提升 {value} 点，持续 {duration} 回合。|n"
                
                elif effect_type == "heal":
                    msg += f"\n|g你恢复了 {value} 点生命值。|n"
            
            self.caller.msg(msg)
            
            # 向其他人显示
            if target and target != self.caller:
                target.msg(f"{self.caller.key} 对你使用了 {skill_name}！")
            
            # 向房间其他人显示
            self.caller.location.msg_contents(
                f"{self.caller.key} 使用了 {skill_name}",
                exclude=[self.caller, target]
            )
        
        else:
            # 技能使用失败
            self.caller.msg(f"|r{result.get('message', '技能使用失败')}|n")


class CmdCheckRealm(Command):
    """
    查看境界信息
    
    用法:
        realm [目标]
        realm 敌人
    """
    
    key = "realm"
    aliases = ["境界", "修为"]
    help_category = "战斗"
    
    def func(self):
        """执行境界查看命令"""
        target = self.caller
        
        if self.args:
            target = self.caller.search(self.args, location=self.caller.location)
            if not target:
                return
        
        # 获取境界信息
        realm = getattr(target, '修为境界', '练气')
        element = getattr(target, '五行属性', '土')
        
        if hasattr(target, 'get_realm_level'):
            realm_level = target.get_realm_level()
        else:
            realm_levels = {
                "练气": 0, "筑基": 1, "金丹": 2, "元婴": 3, "化神": 4,
                "炼虚": 5, "合体": 6, "大乘": 7, "渡劫": 8, "仙人": 9
            }
            realm_level = realm_levels.get(realm, 0)
        
        # 获取战斗加成
        if hasattr(target, 'get_realm_combat_bonus'):
            combat_bonus = target.get_realm_combat_bonus()
        else:
            combat_bonus = 1.0 + (realm_level * 0.1)
        
        msg = f"|c{target.key} 的境界信息:|n\n"
        msg += f"修为境界: |y{realm}|n (等级 {realm_level})\n"
        msg += f"五行属性: |g{element}|n\n"
        msg += f"战斗加成: |r{combat_bonus:.1f}x|n"
        
        # 如果是查看自己，显示更多信息
        if target == self.caller:
            if hasattr(target, 'db') and hasattr(target.db, 'spiritual_power'):
                sp = target.db.spiritual_power
                max_sp = target.db.max_spiritual_power
                msg += f"灵力: |b{sp}/{max_sp}|n"
        
        self.caller.msg(msg)


class CmdElementalInfo(Command):
    """
    查看五行信息
    
    用法:
        element [五行属性]
        element 金
        element
    """
    
    key = "element"
    aliases = ["五行", "wuxing"]
    help_category = "战斗"
    
    def func(self):
        """执行五行信息查看命令"""
        wuxing_calc = get_wuxing_calculator()
        
        if not self.args:
            # 显示自己的五行属性
            element = getattr(self.caller, '五行属性', '土')
            element_info = wuxing_calc.get_element_info(element)
            
            if "error" in element_info:
                self.caller.msg(f"|r{element_info['error']}|n")
                return
            
            msg = f"|c你的五行属性: {element}|n\n"
            msg += f"克制: |r{element_info['克制']}|n\n"
            msg += f"被克: |y{element_info['被克']}|n\n"
            msg += f"相生: |g{element_info['相生']}|n\n"
            msg += f"生我: |b{element_info['生我']}|n\n"
            
            characteristics = element_info.get('特性', {})
            if characteristics:
                msg += f"\n|w特性信息:|n\n"
                for key, value in characteristics.items():
                    msg += f"{key}: {value}\n"
            
            self.caller.msg(msg)
        
        else:
            # 显示指定五行的信息
            element = self.args.strip()
            element_info = wuxing_calc.get_element_info(element)
            
            if "error" in element_info:
                self.caller.msg(f"|r{element_info['error']}|n")
                return
            
            msg = f"|c五行属性: {element}|n\n"
            msg += f"克制: |r{element_info['克制']}|n\n"
            msg += f"被克: |y{element_info['被克']}|n\n"
            msg += f"相生: |g{element_info['相生']}|n\n"
            msg += f"生我: |b{element_info['生我']}|n\n"
            
            characteristics = element_info.get('特性', {})
            if characteristics:
                msg += f"\n|w特性信息:|n\n"
                for key, value in characteristics.items():
                    msg += f"{key}: {value}\n"
            
            self.caller.msg(msg)


class CmdCombatStatus(Command):
    """
    查看战斗状态
    
    用法:
        status
        st
    """
    
    key = "status"
    aliases = ["st", "状态"]
    help_category = "战斗"
    
    def func(self):
        """执行战斗状态查看命令"""
        # 检查是否在战斗中
        if not hasattr(self.caller, 'ndb') or not self.caller.ndb.combat_handler:
            self.caller.msg("你不在战斗中。")
            return
        
        # 基础状态
        hp = getattr(self.caller, 'hp', 100)
        max_hp = getattr(self.caller, 'max_hp', 100)
        sp = getattr(self.caller, 'db', {}).get('spiritual_power', 100)
        max_sp = getattr(self.caller, 'db', {}).get('max_spiritual_power', 100)
        
        msg = f"|c{self.caller.key} 的战斗状态:|n\n"
        msg += f"生命值: |r{hp}/{max_hp}|n\n"
        msg += f"灵力: |b{sp}/{max_sp}|n\n"
        
        # 境界和五行
        realm = getattr(self.caller, '修为境界', '练气')
        element = getattr(self.caller, '五行属性', '土')
        msg += f"境界: |y{realm}|n\n"
        msg += f"五行: |g{element}|n\n"
        
        # 可用技能
        if hasattr(self.caller, 'get_available_skills'):
            skills = self.caller.get_available_skills()
            msg += f"\n|w可用技能:|n\n"
            
            for skill in skills:
                skill_name = skill["name"]
                can_use = skill["can_use"]
                reason = skill["reason"]
                
                if can_use:
                    msg += f"  |g{skill_name}|n - {skill['description']}\n"
                else:
                    msg += f"  |r{skill_name}|n - {reason}\n"
        
        # 增益效果
        if hasattr(self.caller, 'db') and 'defense_buffs' in self.caller.db:
            active_buffs = []
            current_time = time.time()
            
            for buff in self.caller.db.defense_buffs:
                if buff.get('expires', 0) > current_time:
                    remaining = buff['expires'] - current_time
                    active_buffs.append(f"防御+{buff['value']} ({remaining:.1f}s)")
            
            if active_buffs:
                msg += f"\n|w增益效果:|n\n"
                for buff in active_buffs:
                    msg += f"  {buff}\n"
        
        self.caller.msg(msg)


class CmdSkillList(Command):
    """
    查看技能列表
    
    用法:
        skills
        技能列表
    """
    
    key = "skills"
    aliases = ["技能列表", "skilllist"]
    help_category = "战斗"
    
    def func(self):
        """执行技能列表查看命令"""
        skill_system = get_skill_system()
        
        if not hasattr(self.caller, 'attributes'):
            self.caller.msg("无法获取技能信息。")
            return
        
        # 加载技能数据
        skill_data = skill_system.load_skill_data(self.caller)
        
        if not skill_data:
            self.caller.msg("你还没有学会任何技能。")
            return
        
        msg = f"|c{self.caller.key} 的技能列表:|n\n"
        
        for skill_name, skill_info in skill_data.items():
            skill_template = skill_info["template"]
            level = skill_info["level"]
            max_level = skill_template.get("max_level", 10)
            
            # 技能基本信息
            skill_type = skill_template.get("type", "未知")
            element = skill_template.get("element", "无")
            grade = skill_template.get("grade", "凡级")
            
            msg += f"\n|w{skill_name}|n (等级 {level}/{max_level})\n"
            msg += f"  类型: {skill_type} | 五行: |g{element}|n | 品级: |y{grade}|n\n"
            msg += f"  描述: {skill_template.get('description', '无描述')}\n"
            
            # 当前属性
            current_stats = skill_system._calculate_current_stats(skill_template, level)
            stats_text = []
            
            if "damage" in current_stats:
                stats_text.append(f"伤害: {current_stats['damage']}")
            if "heal_amount" in current_stats:
                stats_text.append(f"治疗: {current_stats['heal_amount']}")
            if "defense_bonus" in current_stats:
                stats_text.append(f"防御: {current_stats['defense_bonus']}")
            if "mp_cost" in current_stats:
                stats_text.append(f"消耗: {current_stats['mp_cost']}")
            if "cooldown" in current_stats:
                stats_text.append(f"冷却: {current_stats['cooldown']}s")
            
            if stats_text:
                msg += f"  属性: {' | '.join(stats_text)}\n"
        
        self.caller.msg(msg)


class XianxiaCombatCmdSet(BattleCmdSet):
    """
    仙侠战斗命令集
    
    继承TurnBattle的BattleCmdSet，添加仙侠特色命令。
    """
    
    key = "xiuxian_combat"
    priority = 10
    
    def at_cmdset_creation(self):
        """创建命令集时添加命令"""
        super().at_cmdset_creation()
        
        # 添加仙侠战斗命令
        self.add(CmdUseSkill())
        self.add(CmdCheckRealm())
        self.add(CmdElementalInfo())
        self.add(CmdCombatStatus())
        self.add(CmdSkillList())
        
        logger.log_info("XianxiaCombatCmdSet created with xiuxian commands")
