#!/usr/bin/env python3
"""
验证AI导演命令是否正确加载的脚本
"""

import os
import sys
import django

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

# 添加项目路径到sys.path
project_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_path)

# 初始化Django
django.setup()

from evennia import create_account, create_object, DefaultCharacter
from evennia.commands.cmdhandler import cmdhandler
from commands.default_cmdsets import CharacterCmdSet

def test_commands():
    print("=== 验证AI导演命令系统 ===")
    
    try:
        # 获取或创建测试角色
        account = create_account("test_account", email="<EMAIL>", password="testpass", typeclass=None)
        if not account:
            from evennia import AccountDB
            account = AccountDB.objects.get(username="admin")
        
        character = account.db._last_puppet
        if not character:
            character = create_object(<PERSON>fa<PERSON><PERSON><PERSON><PERSON>, key="test_character", location=None)
            character.account = account
        
        print(f"使用角色: {character.key}")
        
        # 测试命令
        test_commands = [
            "ai帮助",
            "ai状态", 
            "ai性能",
            "测试ai",
            "ai决策 测试事件"
        ]
        
        success_count = 0
        total_count = len(test_commands)
        
        for cmd in test_commands:
            try:
                print(f"\n测试命令: {cmd}")
                result = cmdhandler(character, cmd, cmdobj=character)
                if result:
                    print(f"✓ 命令执行成功")
                    success_count += 1
                else:
                    print(f"✗ 命令执行失败")
            except Exception as e:
                print(f"✗ 命令执行异常: {e}")
        
        print(f"\n=== 测试结果 ===")
        print(f"成功执行: {success_count}/{total_count} 个命令")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 检查命令集
        cmdset = CharacterCmdSet()
        cmdset.at_cmdset_creation()
        
        ai_commands = []
        for cmd in cmdset.commands:
            if any(keyword in cmd.key.lower() for keyword in ['ai', '测试', '状态', '性能', '决策', '帮助']):
                ai_commands.append(cmd)
        
        print(f"\n发现 {len(ai_commands)} 个AI相关命令:")
        for cmd in ai_commands:
            aliases = ', '.join(cmd.aliases) if hasattr(cmd, 'aliases') and cmd.aliases else '无'
            print(f"  - {cmd.key} (别名: {aliases})")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_commands()
    exit(0 if success else 1)