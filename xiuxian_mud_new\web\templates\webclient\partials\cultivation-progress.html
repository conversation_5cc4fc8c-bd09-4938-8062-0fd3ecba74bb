{% comment %}
修仙进度组件模板

功能：
- 实时修仙境界和经验显示
- 技能修炼进度追踪
- 功法学习状态监控
- 修炼任务和目标管理
- 突破条件检查和提醒
- 修炼历史记录

技术集成：
- 与Evennia Attribute系统完美集成
- 实时数据同步和进度更新
- 本地存储修炼偏好和目标设置
- 动画效果和视觉反馈
- 移动端友好的进度显示
{% endcomment %}

<div class="cultivation-progress" id="cultivation-progress">
    <!-- 头部信息 -->
    <div class="cultivation-progress__header">
        <div class="cultivation-progress__title-section">
            <span class="cultivation-progress__icon">🧘</span>
            <span class="cultivation-progress__title">修仙进度</span>
            <span class="cultivation-progress__realm" id="current-realm">筑基期</span>
        </div>
        <div class="cultivation-progress__header-controls">
            <button type="button" class="progress-control-btn" id="progress-refresh" title="刷新进度">
                <span class="btn-icon">🔄</span>
            </button>
            <button type="button" class="progress-control-btn" id="progress-history" title="修炼历史">
                <span class="btn-icon">📜</span>
            </button>
            <button type="button" class="progress-control-btn" id="progress-goals" title="修炼目标">
                <span class="btn-icon">🎯</span>
            </button>
            <button type="button" class="progress-control-btn" id="progress-settings" title="设置">
                <span class="btn-icon">⚙️</span>
            </button>
        </div>
    </div>

    <!-- 境界进度 -->
    <div class="cultivation-progress__realm-section">
        <div class="realm-info">
            <div class="realm-current">
                <span class="realm-name" id="realm-name">筑基期</span>
                <span class="realm-level" id="realm-level">第3层</span>
            </div>
            <div class="realm-next">
                <span class="next-realm-text">下一境界:</span>
                <span class="next-realm-name" id="next-realm">金丹期</span>
            </div>
        </div>
        
        <div class="realm-progress">
            <div class="progress-bar-container">
                <div class="progress-bar" id="realm-progress-bar">
                    <div class="progress-fill" id="realm-progress-fill" style="width: 65%"></div>
                    <div class="progress-text" id="realm-progress-text">65%</div>
                </div>
            </div>
            <div class="progress-details">
                <span class="current-exp" id="current-exp">6,500</span>
                <span class="exp-separator">/</span>
                <span class="max-exp" id="max-exp">10,000</span>
                <span class="exp-unit">修为点</span>
            </div>
        </div>

        <!-- 突破条件 -->
        <div class="breakthrough-conditions" id="breakthrough-conditions">
            <div class="conditions-title">突破条件:</div>
            <div class="conditions-list">
                <div class="condition-item completed">
                    <span class="condition-icon">✓</span>
                    <span class="condition-text">修为达到筑基期圆满</span>
                </div>
                <div class="condition-item">
                    <span class="condition-icon">○</span>
                    <span class="condition-text">掌握至少3种筑基期功法</span>
                </div>
                <div class="condition-item">
                    <span class="condition-icon">○</span>
                    <span class="condition-text">获得金丹期突破丹药</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 技能修炼 -->
    <div class="cultivation-progress__skills-section">
        <div class="section-header">
            <h4 class="section-title">技能修炼</h4>
            <button type="button" class="section-toggle" id="skills-toggle">
                <span class="toggle-icon">▼</span>
            </button>
        </div>
        
        <div class="skills-content" id="skills-content">
            <div class="skill-category">
                <div class="category-header">
                    <span class="category-icon">⚔️</span>
                    <span class="category-name">武学技能</span>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">太极剑法</span>
                            <span class="skill-level">第7层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 80%"></div>
                            </div>
                            <span class="progress-percent">80%</span>
                        </div>
                    </div>
                    
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">武当内功</span>
                            <span class="skill-level">第5层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 45%"></div>
                            </div>
                            <span class="progress-percent">45%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="skill-category">
                <div class="category-header">
                    <span class="category-icon">🔮</span>
                    <span class="category-name">法术技能</span>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">御剑术</span>
                            <span class="skill-level">第3层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 30%"></div>
                            </div>
                            <span class="progress-percent">30%</span>
                        </div>
                    </div>
                    
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">五行遁术</span>
                            <span class="skill-level">第2层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 60%"></div>
                            </div>
                            <span class="progress-percent">60%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="skill-category">
                <div class="category-header">
                    <span class="category-icon">🧪</span>
                    <span class="category-name">辅助技能</span>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">炼丹术</span>
                            <span class="skill-level">第4层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 70%"></div>
                            </div>
                            <span class="progress-percent">70%</span>
                        </div>
                    </div>
                    
                    <div class="skill-item">
                        <div class="skill-info">
                            <span class="skill-name">阵法学</span>
                            <span class="skill-level">第2层</span>
                        </div>
                        <div class="skill-progress">
                            <div class="progress-bar mini">
                                <div class="progress-fill" style="width: 25%"></div>
                            </div>
                            <span class="progress-percent">25%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功法修炼 -->
    <div class="cultivation-progress__techniques-section">
        <div class="section-header">
            <h4 class="section-title">功法修炼</h4>
            <button type="button" class="section-toggle" id="techniques-toggle">
                <span class="toggle-icon">▼</span>
            </button>
        </div>
        
        <div class="techniques-content" id="techniques-content">
            <div class="technique-item active">
                <div class="technique-header">
                    <div class="technique-info">
                        <span class="technique-name">太极玄清道</span>
                        <span class="technique-grade">天级上品</span>
                        <span class="technique-status">修炼中</span>
                    </div>
                    <div class="technique-progress">
                        <span class="technique-level">第3重</span>
                        <div class="progress-bar mini">
                            <div class="progress-fill" style="width: 55%"></div>
                        </div>
                        <span class="progress-percent">55%</span>
                    </div>
                </div>
                <div class="technique-details">
                    <div class="technique-description">
                        武当派镇派功法，修炼至大成可达化神期巅峰。当前修炼进度良好，建议配合打坐修炼。
                    </div>
                    <div class="technique-effects">
                        <span class="effect-item">内力恢复 +30%</span>
                        <span class="effect-item">法术威力 +25%</span>
                        <span class="effect-item">突破成功率 +15%</span>
                    </div>
                </div>
            </div>

            <div class="technique-item">
                <div class="technique-header">
                    <div class="technique-info">
                        <span class="technique-name">紫霞神功</span>
                        <span class="technique-grade">地级中品</span>
                        <span class="technique-status">已掌握</span>
                    </div>
                    <div class="technique-progress">
                        <span class="technique-level">圆满</span>
                        <div class="progress-bar mini">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                        <span class="progress-percent">100%</span>
                    </div>
                </div>
                <div class="technique-details">
                    <div class="technique-description">
                        华山派内功心法，已修炼至圆满境界。可作为基础功法辅助其他功法修炼。
                    </div>
                    <div class="technique-effects">
                        <span class="effect-item">内力深厚 +20%</span>
                        <span class="effect-item">修炼速度 +10%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修炼任务 -->
    <div class="cultivation-progress__tasks-section">
        <div class="section-header">
            <h4 class="section-title">修炼任务</h4>
            <button type="button" class="add-task-btn" id="add-task-btn" title="添加任务">
                <span class="btn-icon">+</span>
            </button>
        </div>
        
        <div class="tasks-content" id="tasks-content">
            <div class="task-item priority-high">
                <div class="task-header">
                    <div class="task-info">
                        <span class="task-name">突破至金丹期</span>
                        <span class="task-priority">高优先级</span>
                    </div>
                    <div class="task-progress">
                        <span class="task-completion">2/3</span>
                        <div class="progress-bar mini">
                            <div class="progress-fill" style="width: 67%"></div>
                        </div>
                    </div>
                </div>
                <div class="task-details">
                    <div class="task-description">完成筑基期修炼，准备突破至金丹期</div>
                    <div class="task-deadline">目标时间: 7天内</div>
                </div>
            </div>

            <div class="task-item priority-medium">
                <div class="task-header">
                    <div class="task-info">
                        <span class="task-name">太极剑法提升至第8层</span>
                        <span class="task-priority">中优先级</span>
                    </div>
                    <div class="task-progress">
                        <span class="task-completion">80%</span>
                        <div class="progress-bar mini">
                            <div class="progress-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </div>
                <div class="task-details">
                    <div class="task-description">继续修炼太极剑法，提升武学造诣</div>
                    <div class="task-deadline">目标时间: 14天内</div>
                </div>
            </div>

            <div class="task-item priority-low completed">
                <div class="task-header">
                    <div class="task-info">
                        <span class="task-name">学习炼丹术基础</span>
                        <span class="task-priority">已完成</span>
                    </div>
                    <div class="task-progress">
                        <span class="task-completion">100%</span>
                        <div class="progress-bar mini">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="task-details">
                    <div class="task-description">掌握基础炼丹知识和技巧</div>
                    <div class="task-deadline">已于3天前完成</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修炼统计 -->
    <div class="cultivation-progress__stats-section">
        <div class="section-header">
            <h4 class="section-title">修炼统计</h4>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-info">
                    <div class="stat-value" id="total-time">156小时</div>
                    <div class="stat-label">总修炼时间</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-info">
                    <div class="stat-value" id="daily-progress">+320</div>
                    <div class="stat-label">今日修为增长</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🏆</div>
                <div class="stat-info">
                    <div class="stat-value" id="breakthrough-count">3次</div>
                    <div class="stat-label">成功突破次数</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📚</div>
                <div class="stat-info">
                    <div class="stat-value" id="techniques-learned">8部</div>
                    <div class="stat-label">已学功法数量</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 修仙进度管理器
window.CultivationProgressManager = {
    // 配置
    config: {
        updateInterval: 10000, // 10秒更新一次
        animationDuration: 1000,
        autoSave: true
    },

    // 状态
    state: {
        currentRealm: 'foundation',
        currentLevel: 3,
        currentExp: 6500,
        maxExp: 10000,
        skills: {},
        techniques: {},
        tasks: [],
        stats: {},
        goals: [],
        history: []
    },

    // 初始化
    init: function() {
        this.bindEvents();
        this.loadData();
        this.connectWebSocket();
        this.startUpdateTimer();
        this.requestProgressData();
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 头部控制按钮
        document.getElementById('progress-refresh').addEventListener('click', function() {
            self.refreshProgress();
        });

        document.getElementById('progress-history').addEventListener('click', function() {
            self.showHistory();
        });

        document.getElementById('progress-goals').addEventListener('click', function() {
            self.showGoals();
        });

        document.getElementById('progress-settings').addEventListener('click', function() {
            self.showSettings();
        });

        // 折叠/展开按钮
        document.getElementById('skills-toggle').addEventListener('click', function() {
            self.toggleSection('skills');
        });

        document.getElementById('techniques-toggle').addEventListener('click', function() {
            self.toggleSection('techniques');
        });

        // 添加任务按钮
        document.getElementById('add-task-btn').addEventListener('click', function() {
            self.showAddTaskDialog();
        });

        // 技能项点击
        document.addEventListener('click', function(e) {
            if (e.target.closest('.skill-item')) {
                const skillItem = e.target.closest('.skill-item');
                const skillName = skillItem.querySelector('.skill-name').textContent;
                self.showSkillDetails(skillName);
            }
        });

        // 功法项点击
        document.addEventListener('click', function(e) {
            if (e.target.closest('.technique-item')) {
                const techniqueItem = e.target.closest('.technique-item');
                const techniqueName = techniqueItem.querySelector('.technique-name').textContent;
                self.showTechniqueDetails(techniqueName);
            }
        });

        // 任务项操作
        document.addEventListener('click', function(e) {
            if (e.target.closest('.task-item')) {
                const taskItem = e.target.closest('.task-item');
                const taskName = taskItem.querySelector('.task-name').textContent;

                if (e.detail === 2) { // 双击编辑
                    self.editTask(taskName);
                } else { // 单击查看详情
                    self.showTaskDetails(taskName);
                }
            }
        });
    },

    // 切换区域折叠状态
    toggleSection: function(section) {
        const content = document.getElementById(section + '-content');
        const toggle = document.getElementById(section + '-toggle');
        const icon = toggle.querySelector('.toggle-icon');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.textContent = '▼';
        } else {
            content.style.display = 'none';
            icon.textContent = '▶';
        }

        // 保存折叠状态
        localStorage.setItem('cultivation-' + section + '-collapsed', content.style.display === 'none');
    },

    // 更新境界进度
    updateRealmProgress: function(realmData) {
        this.state.currentRealm = realmData.realm;
        this.state.currentLevel = realmData.level;
        this.state.currentExp = realmData.currentExp;
        this.state.maxExp = realmData.maxExp;

        // 更新显示
        document.getElementById('current-realm').textContent = this.getRealmText(realmData.realm);
        document.getElementById('realm-name').textContent = this.getRealmText(realmData.realm);
        document.getElementById('realm-level').textContent = `第${realmData.level}层`;
        document.getElementById('next-realm').textContent = this.getRealmText(realmData.nextRealm);

        // 更新经验显示
        document.getElementById('current-exp').textContent = this.formatNumber(realmData.currentExp);
        document.getElementById('max-exp').textContent = this.formatNumber(realmData.maxExp);

        // 更新进度条
        const progressPercent = Math.round((realmData.currentExp / realmData.maxExp) * 100);
        this.animateProgressBar('realm-progress-fill', progressPercent);
        document.getElementById('realm-progress-text').textContent = progressPercent + '%';

        // 更新突破条件
        this.updateBreakthroughConditions(realmData.conditions);
    },

    // 更新突破条件
    updateBreakthroughConditions: function(conditions) {
        const container = document.getElementById('breakthrough-conditions');
        const conditionsList = container.querySelector('.conditions-list');

        conditionsList.innerHTML = '';

        conditions.forEach(function(condition) {
            const conditionItem = document.createElement('div');
            conditionItem.className = 'condition-item' + (condition.completed ? ' completed' : '');

            conditionItem.innerHTML = `
                <span class="condition-icon">${condition.completed ? '✓' : '○'}</span>
                <span class="condition-text">${condition.text}</span>
            `;

            conditionsList.appendChild(conditionItem);
        });
    },

    // 更新技能进度
    updateSkillsProgress: function(skillsData) {
        this.state.skills = skillsData;

        Object.keys(skillsData).forEach(function(category) {
            const skills = skillsData[category];
            const categoryElement = this.findSkillCategory(category);

            if (categoryElement) {
                const skillsList = categoryElement.querySelector('.skills-list');
                skillsList.innerHTML = '';

                skills.forEach(function(skill) {
                    const skillItem = this.createSkillItem(skill);
                    skillsList.appendChild(skillItem);
                }.bind(this));
            }
        }.bind(this));
    },

    // 创建技能项
    createSkillItem: function(skill) {
        const skillItem = document.createElement('div');
        skillItem.className = 'skill-item';

        const progressPercent = Math.round((skill.currentExp / skill.maxExp) * 100);

        skillItem.innerHTML = `
            <div class="skill-info">
                <span class="skill-name">${skill.name}</span>
                <span class="skill-level">第${skill.level}层</span>
            </div>
            <div class="skill-progress">
                <div class="progress-bar mini">
                    <div class="progress-fill" style="width: ${progressPercent}%"></div>
                </div>
                <span class="progress-percent">${progressPercent}%</span>
            </div>
        `;

        return skillItem;
    },

    // 查找技能分类
    findSkillCategory: function(category) {
        const categoryMap = {
            martial: '武学技能',
            magic: '法术技能',
            auxiliary: '辅助技能'
        };

        const categoryText = categoryMap[category];
        if (!categoryText) return null;

        const categories = document.querySelectorAll('.skill-category');
        for (let i = 0; i < categories.length; i++) {
            const categoryName = categories[i].querySelector('.category-name').textContent;
            if (categoryName === categoryText) {
                return categories[i];
            }
        }

        return null;
    },

    // 更新功法进度
    updateTechniquesProgress: function(techniquesData) {
        this.state.techniques = techniquesData;

        const container = document.getElementById('techniques-content');
        container.innerHTML = '';

        techniquesData.forEach(function(technique) {
            const techniqueItem = this.createTechniqueItem(technique);
            container.appendChild(techniqueItem);
        }.bind(this));
    },

    // 创建功法项
    createTechniqueItem: function(technique) {
        const techniqueItem = document.createElement('div');
        techniqueItem.className = 'technique-item' + (technique.active ? ' active' : '');

        const progressPercent = technique.level === 'perfect' ? 100 :
            Math.round((technique.currentExp / technique.maxExp) * 100);

        techniqueItem.innerHTML = `
            <div class="technique-header">
                <div class="technique-info">
                    <span class="technique-name">${technique.name}</span>
                    <span class="technique-grade">${technique.grade}</span>
                    <span class="technique-status">${technique.status}</span>
                </div>
                <div class="technique-progress">
                    <span class="technique-level">${technique.level === 'perfect' ? '圆满' : '第' + technique.level + '重'}</span>
                    <div class="progress-bar mini">
                        <div class="progress-fill" style="width: ${progressPercent}%"></div>
                    </div>
                    <span class="progress-percent">${progressPercent}%</span>
                </div>
            </div>
            <div class="technique-details">
                <div class="technique-description">${technique.description}</div>
                <div class="technique-effects">
                    ${technique.effects.map(function(effect) {
                        return `<span class="effect-item">${effect}</span>`;
                    }).join('')}
                </div>
            </div>
        `;

        return techniqueItem;
    },

    // 更新任务列表
    updateTasks: function(tasksData) {
        this.state.tasks = tasksData;

        const container = document.getElementById('tasks-content');
        container.innerHTML = '';

        tasksData.forEach(function(task) {
            const taskItem = this.createTaskItem(task);
            container.appendChild(taskItem);
        }.bind(this));
    },

    // 创建任务项
    createTaskItem: function(task) {
        const taskItem = document.createElement('div');
        taskItem.className = `task-item priority-${task.priority}` + (task.completed ? ' completed' : '');

        const progressPercent = Math.round(task.progress * 100);

        taskItem.innerHTML = `
            <div class="task-header">
                <div class="task-info">
                    <span class="task-name">${task.name}</span>
                    <span class="task-priority">${task.completed ? '已完成' : this.getPriorityText(task.priority)}</span>
                </div>
                <div class="task-progress">
                    <span class="task-completion">${task.completed ? '100%' : progressPercent + '%'}</span>
                    <div class="progress-bar mini">
                        <div class="progress-fill" style="width: ${progressPercent}%"></div>
                    </div>
                </div>
            </div>
            <div class="task-details">
                <div class="task-description">${task.description}</div>
                <div class="task-deadline">${task.completed ? '已于' + task.completedDate + '完成' : '目标时间: ' + task.deadline}</div>
            </div>
        `;

        return taskItem;
    },

    // 更新统计信息
    updateStats: function(statsData) {
        this.state.stats = statsData;

        document.getElementById('total-time').textContent = this.formatTime(statsData.totalTime);
        document.getElementById('daily-progress').textContent = '+' + this.formatNumber(statsData.dailyProgress);
        document.getElementById('breakthrough-count').textContent = statsData.breakthroughCount + '次';
        document.getElementById('techniques-learned').textContent = statsData.techniquesLearned + '部';
    },

    // 动画进度条
    animateProgressBar: function(elementId, targetPercent) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const currentWidth = parseInt(element.style.width) || 0;
        const targetWidth = targetPercent;

        if (currentWidth === targetWidth) return;

        const duration = this.config.animationDuration;
        const startTime = Date.now();

        const animate = function() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentValue = currentWidth + (targetWidth - currentWidth) * progress;
            element.style.width = currentValue + '%';

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    },

    // 显示技能详情
    showSkillDetails: function(skillName) {
        // 这里可以实现技能详情对话框
        this.showNotification(`查看 ${skillName} 详情功能开发中`, 'info');
    },

    // 显示功法详情
    showTechniqueDetails: function(techniqueName) {
        // 这里可以实现功法详情对话框
        this.showNotification(`查看 ${techniqueName} 详情功能开发中`, 'info');
    },

    // 显示任务详情
    showTaskDetails: function(taskName) {
        // 这里可以实现任务详情对话框
        this.showNotification(`查看任务 ${taskName} 详情功能开发中`, 'info');
    },

    // 编辑任务
    editTask: function(taskName) {
        // 这里可以实现任务编辑功能
        this.showNotification(`编辑任务 ${taskName} 功能开发中`, 'info');
    },

    // 显示添加任务对话框
    showAddTaskDialog: function() {
        // 这里可以实现添加任务对话框
        this.showNotification('添加任务功能开发中', 'info');
    },

    // 显示修炼历史
    showHistory: function() {
        // 这里可以实现修炼历史对话框
        this.showNotification('修炼历史功能开发中', 'info');
    },

    // 显示修炼目标
    showGoals: function() {
        // 这里可以实现修炼目标对话框
        this.showNotification('修炼目标功能开发中', 'info');
    },

    // 显示设置
    showSettings: function() {
        // 这里可以实现设置对话框
        this.showNotification('修仙进度设置功能开发中', 'info');
    },

    // 工具方法：获取境界文本
    getRealmText: function(realm) {
        const realmMap = {
            qi: '练气期',
            foundation: '筑基期',
            golden: '金丹期',
            nascent: '元婴期',
            spirit: '化神期',
            void: '炼虚期',
            unity: '合体期',
            tribulation: '大乘期',
            immortal: '仙人境'
        };
        return realmMap[realm] || '未知境界';
    },

    // 工具方法：获取优先级文本
    getPriorityText: function(priority) {
        const priorityMap = {
            high: '高优先级',
            medium: '中优先级',
            low: '低优先级'
        };
        return priorityMap[priority] || '普通';
    },

    // 工具方法：格式化数字
    formatNumber: function(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    },

    // 工具方法：格式化时间
    formatTime: function(seconds) {
        if (seconds >= 3600) {
            return Math.floor(seconds / 3600) + '小时';
        } else if (seconds >= 60) {
            return Math.floor(seconds / 60) + '分钟';
        }
        return seconds + '秒';
    },

    // WebSocket集成
    connectWebSocket: function() {
        if (!window.Evennia || !window.Evennia.msg) {
            console.warn('Evennia WebSocket not available');
            return;
        }

        // 保存原始消息处理器
        this.originalEvenniaMsg = window.Evennia.msg;

        // 扩展消息处理器
        const self = this;
        window.Evennia.msg = function(cmdname, args, kwargs) {
            // 处理修仙进度相关消息
            if (cmdname === 'cultivation_progress') {
                self.handleProgressUpdate(args, kwargs);
            } else if (cmdname === 'skill_progress') {
                self.handleSkillUpdate(args, kwargs);
            } else if (cmdname === 'technique_progress') {
                self.handleTechniqueUpdate(args, kwargs);
            } else if (cmdname === 'breakthrough_success') {
                self.handleBreakthrough(args, kwargs);
            }

            // 调用原始处理器
            if (self.originalEvenniaMsg) {
                self.originalEvenniaMsg(cmdname, args, kwargs);
            }
        };
    },

    // 处理进度更新
    handleProgressUpdate: function(args, kwargs) {
        if (args.realm) {
            this.updateRealmProgress(args.realm);
        }
        if (args.skills) {
            this.updateSkillsProgress(args.skills);
        }
        if (args.techniques) {
            this.updateTechniquesProgress(args.techniques);
        }
        if (args.tasks) {
            this.updateTasks(args.tasks);
        }
        if (args.stats) {
            this.updateStats(args.stats);
        }
    },

    // 处理技能更新
    handleSkillUpdate: function(args, kwargs) {
        const skillName = args.skill;
        const newLevel = args.level;
        const newExp = args.exp;

        this.showNotification(`${skillName} 提升至第${newLevel}层！`, 'success');

        // 更新技能数据
        this.requestProgressData();
    },

    // 处理功法更新
    handleTechniqueUpdate: function(args, kwargs) {
        const techniqueName = args.technique;
        const newLevel = args.level;

        this.showNotification(`${techniqueName} 突破至第${newLevel}重！`, 'success');

        // 更新功法数据
        this.requestProgressData();
    },

    // 处理突破成功
    handleBreakthrough: function(args, kwargs) {
        const newRealm = args.realm;
        const newLevel = args.level;

        this.showNotification(`恭喜！成功突破至${this.getRealmText(newRealm)}第${newLevel}层！`, 'success');

        // 播放突破特效
        this.playBreakthroughEffect();

        // 更新进度数据
        this.requestProgressData();
    },

    // 播放突破特效
    playBreakthroughEffect: function() {
        // 这里可以添加突破特效动画
        const progressElement = document.getElementById('cultivation-progress');
        progressElement.classList.add('breakthrough-effect');

        setTimeout(function() {
            progressElement.classList.remove('breakthrough-effect');
        }, 2000);
    },

    // 发送修仙命令
    sendCultivationCommand: function(command, data) {
        if (window.Evennia && window.Evennia.msg) {
            window.Evennia.msg('cultivation_command', [command], data);
        }
    },

    // 请求进度数据
    requestProgressData: function() {
        this.sendCultivationCommand('get_progress', {});
    },

    // 刷新进度
    refreshProgress: function() {
        this.requestProgressData();
        this.showNotification('正在刷新修仙进度...', 'info');
    },

    // 开始更新定时器
    startUpdateTimer: function() {
        const self = this;
        setInterval(function() {
            self.requestProgressData();
        }, this.config.updateInterval);
    },

    // 显示通知
    showNotification: function(message, type) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `cultivation-notification ${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(function() {
            notification.remove();
        }, 3000);
    },

    // 加载数据
    loadData: function() {
        // 加载折叠状态
        const skillsCollapsed = localStorage.getItem('cultivation-skills-collapsed') === 'true';
        const techniquesCollapsed = localStorage.getItem('cultivation-techniques-collapsed') === 'true';

        if (skillsCollapsed) {
            document.getElementById('skills-content').style.display = 'none';
            document.getElementById('skills-toggle').querySelector('.toggle-icon').textContent = '▶';
        }

        if (techniquesCollapsed) {
            document.getElementById('techniques-content').style.display = 'none';
            document.getElementById('techniques-toggle').querySelector('.toggle-icon').textContent = '▶';
        }
    },

    // 销毁组件
    destroy: function() {
        // 恢复原始消息处理器
        if (this.originalEvenniaMsg) {
            window.Evennia.msg = this.originalEvenniaMsg;
        }
    }
};

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.CultivationProgressManager) {
        window.CultivationProgressManager.init();
    }
});
</script>
