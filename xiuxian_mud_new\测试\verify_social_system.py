"""
仙侠社交系统验证脚本

验证社交系统的完整性、性能和功能正确性：
- 系统组件完整性检查
- 性能基准测试
- 功能集成验证
- 数据一致性检查
- AI导演集成验证

可以在Evennia环境中直接运行进行系统验证。
"""

import time
import traceback
from typing import Dict, Any, List, Tuple
from evennia.utils import logger
from evennia import search, create


class SocialSystemVerifier:
    """社交系统验证器"""
    
    def __init__(self):
        self.verification_results = {
            "component_integrity": {},
            "performance_tests": {},
            "functional_tests": {},
            "integration_tests": {},
            "overall_status": "unknown"
        }
        self.test_characters = []
        self.test_channels = []
    
    def run_full_verification(self) -> Dict[str, Any]:
        """运行完整的系统验证"""
        logger.log_info("开始社交系统完整验证...")
        
        try:
            # 1. 组件完整性检查
            self._verify_component_integrity()
            
            # 2. 创建测试数据
            self._setup_test_data()
            
            # 3. 功能验证
            self._verify_functionality()
            
            # 4. 性能测试
            self._run_performance_tests()
            
            # 5. 集成测试
            self._verify_integration()
            
            # 6. 清理测试数据
            self._cleanup_test_data()
            
            # 7. 生成总体评估
            self._generate_overall_assessment()
            
            logger.log_info("社交系统验证完成")
            return self.verification_results
            
        except Exception as e:
            logger.log_err(f"社交系统验证失败: {e}")
            logger.log_err(traceback.format_exc())
            self.verification_results["overall_status"] = "failed"
            self.verification_results["error"] = str(e)
            return self.verification_results
    
    def _verify_component_integrity(self):
        """验证组件完整性"""
        logger.log_info("验证组件完整性...")
        
        components = {
            "social_events": "systems.social_events",
            "sect_channels": "typeclasses.sect_channels", 
            "mentorship_manager": "systems.mentorship_manager",
            "social_relationship_manager": "systems.social_relationship_manager",
            "social_query_manager": "systems.social_query_manager",
            "social_commands": "commands.social_commands",
            "sect_management": "systems.sect_management",
            "character_extensions": "typeclasses.characters"
        }
        
        integrity_results = {}
        
        for component_name, module_path in components.items():
            try:
                # 尝试导入模块
                module = __import__(module_path, fromlist=[''])
                
                # 检查关键类和函数
                integrity_results[component_name] = {
                    "import_success": True,
                    "module_path": module_path,
                    "key_classes": self._check_key_classes(module, component_name),
                    "status": "ok"
                }
                
            except ImportError as e:
                integrity_results[component_name] = {
                    "import_success": False,
                    "error": str(e),
                    "status": "failed"
                }
            except Exception as e:
                integrity_results[component_name] = {
                    "import_success": True,
                    "error": str(e),
                    "status": "warning"
                }
        
        self.verification_results["component_integrity"] = integrity_results
        logger.log_info(f"组件完整性检查完成: {len([r for r in integrity_results.values() if r['status'] == 'ok'])}/{len(integrity_results)} 通过")
    
    def _check_key_classes(self, module, component_name: str) -> List[str]:
        """检查模块中的关键类"""
        key_classes_map = {
            "social_events": ["SocialInteractionEvent", "SocialEventType"],
            "sect_channels": ["SectChannel", "SectChannelManager"],
            "mentorship_manager": ["MentorshipManager"],
            "social_relationship_manager": ["SocialRelationshipManager", "ReputationManager"],
            "social_query_manager": ["SocialQueryManager"],
            "social_commands": ["SocialCmdSet"],
            "sect_management": ["SectManager"],
            "character_extensions": ["XianxiaCharacter"]
        }
        
        expected_classes = key_classes_map.get(component_name, [])
        found_classes = []
        
        for class_name in expected_classes:
            if hasattr(module, class_name):
                found_classes.append(class_name)
        
        return found_classes
    
    def _setup_test_data(self):
        """创建测试数据"""
        logger.log_info("创建测试数据...")
        
        try:
            # 创建测试角色
            for i in range(3):
                char = create.create_object(
                    "typeclasses.characters.XianxiaCharacter",
                    key=f"验证测试角色{i+1}"
                )
                self.test_characters.append(char)
            
            # 设置角色属性
            if len(self.test_characters) >= 3:
                self.test_characters[0].修为境界 = "化神"
                self.test_characters[1].修为境界 = "金丹"
                self.test_characters[2].修为境界 = "练气"
            
            logger.log_info(f"创建了 {len(self.test_characters)} 个测试角色")
            
        except Exception as e:
            logger.log_err(f"创建测试数据失败: {e}")
            raise
    
    def _verify_functionality(self):
        """验证功能正确性"""
        logger.log_info("验证功能正确性...")
        
        functional_tests = {
            "sect_creation": self._test_sect_creation,
            "mentorship_system": self._test_mentorship_system,
            "relationship_system": self._test_relationship_system,
            "social_commands": self._test_social_commands,
            "event_system": self._test_event_system
        }
        
        functional_results = {}
        
        for test_name, test_func in functional_tests.items():
            try:
                start_time = time.time()
                result = test_func()
                end_time = time.time()
                
                functional_results[test_name] = {
                    "success": result,
                    "execution_time": end_time - start_time,
                    "status": "passed" if result else "failed"
                }
                
            except Exception as e:
                functional_results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "status": "error"
                }
        
        self.verification_results["functional_tests"] = functional_results
        passed_tests = len([r for r in functional_results.values() if r["status"] == "passed"])
        logger.log_info(f"功能测试完成: {passed_tests}/{len(functional_tests)} 通过")
    
    def _test_sect_creation(self) -> bool:
        """测试门派创建功能"""
        try:
            from systems.sect_management import SectManager
            
            if not self.test_characters:
                return False
            
            # 创建测试门派
            result = SectManager.create_sect(
                self.test_characters[0],
                "验证测试门派",
                "修炼门派",
                description="用于验证的测试门派"
            )
            
            if not result:
                return False
            
            # 验证门派信息
            sect_info = SectManager.get_sect_info("验证测试门派")
            if not sect_info:
                return False
            
            # 验证创始人门派归属
            if self.test_characters[0].门派归属 != "验证测试门派":
                return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"门派创建测试失败: {e}")
            return False
    
    def _test_mentorship_system(self) -> bool:
        """测试师徒系统功能"""
        try:
            from systems.mentorship_manager import MentorshipManager
            
            if len(self.test_characters) < 2:
                return False
            
            master = self.test_characters[0]
            disciple = self.test_characters[2]
            
            # 建立师徒关系
            result = MentorshipManager.establish_mentorship(master, disciple)
            if not result:
                return False
            
            # 验证关系
            if not MentorshipManager.is_master_of(master, disciple):
                return False
            
            # 获取师父
            found_master = MentorshipManager.get_master(disciple)
            if found_master != master:
                return False
            
            # 终止关系
            result = MentorshipManager.terminate_mentorship(master, disciple)
            if not result:
                return False
            
            # 验证关系已终止
            if MentorshipManager.is_master_of(master, disciple):
                return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"师徒系统测试失败: {e}")
            return False
    
    def _test_relationship_system(self) -> bool:
        """测试关系系统功能"""
        try:
            from systems.social_relationship_manager import SocialRelationshipManager, ReputationManager
            
            if len(self.test_characters) < 2:
                return False
            
            char1 = self.test_characters[0]
            char2 = self.test_characters[1]
            
            # 建立友谊关系
            result = SocialRelationshipManager.establish_relationship(
                char1, char2, "friend", "strong"
            )
            if not result:
                return False
            
            # 验证关系
            relationship = SocialRelationshipManager.get_relationship(char1, char2)
            if not relationship or relationship["type"] != "friend":
                return False
            
            # 测试声望系统
            initial_rep = ReputationManager.get_reputation(char1)
            ReputationManager.add_reputation(char1, 100, "测试增加")
            new_rep = ReputationManager.get_reputation(char1)
            
            if new_rep != initial_rep + 100:
                return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"关系系统测试失败: {e}")
            return False
    
    def _test_social_commands(self) -> bool:
        """测试社交命令功能"""
        try:
            from commands.social_commands import SocialCmdSet
            
            # 检查命令集是否正确定义
            cmdset = SocialCmdSet()
            
            # 验证命令数量
            if len(cmdset.commands) < 5:
                return False
            
            # 验证关键命令存在
            command_keys = [cmd.key for cmd in cmdset.commands]
            required_commands = ["入门", "收徒", "加好友"]
            
            for cmd in required_commands:
                if cmd not in command_keys:
                    return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"社交命令测试失败: {e}")
            return False
    
    def _test_event_system(self) -> bool:
        """测试事件系统功能"""
        try:
            from systems.social_events import publish_sect_event, SocialEventType
            
            if not self.test_characters:
                return False
            
            # 发布测试事件
            result = publish_sect_event(
                self.test_characters[0],
                "测试门派",
                SocialEventType.SECT_FOUNDED.value,
                founder_name=self.test_characters[0].key
            )
            
            return result
            
        except Exception as e:
            logger.log_err(f"事件系统测试失败: {e}")
            return False
    
    def _run_performance_tests(self):
        """运行性能测试"""
        logger.log_info("运行性能测试...")
        
        performance_tests = {
            "query_performance": self._test_query_performance,
            "event_publishing_performance": self._test_event_performance,
            "relationship_lookup_performance": self._test_relationship_performance
        }
        
        performance_results = {}
        
        for test_name, test_func in performance_tests.items():
            try:
                result = test_func()
                performance_results[test_name] = result
                
            except Exception as e:
                performance_results[test_name] = {
                    "error": str(e),
                    "status": "failed"
                }
        
        self.verification_results["performance_tests"] = performance_results
        logger.log_info("性能测试完成")
    
    def _test_query_performance(self) -> Dict[str, Any]:
        """测试查询性能"""
        try:
            from systems.social_query_manager import SocialQueryManager
            
            # 测试门派成员查询
            start_time = time.time()
            for _ in range(100):
                SocialQueryManager.get_sect_members("验证测试门派")
            end_time = time.time()
            
            avg_query_time = (end_time - start_time) / 100
            
            return {
                "average_query_time": avg_query_time,
                "queries_per_second": 1 / avg_query_time if avg_query_time > 0 else 0,
                "status": "passed" if avg_query_time < 0.01 else "warning"
            }
            
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def _test_event_performance(self) -> Dict[str, Any]:
        """测试事件发布性能"""
        try:
            from systems.social_events import publish_sect_event, SocialEventType
            
            if not self.test_characters:
                return {"error": "No test characters", "status": "failed"}
            
            # 测试事件发布
            start_time = time.time()
            for i in range(50):
                publish_sect_event(
                    self.test_characters[0],
                    "测试门派",
                    SocialEventType.SECT_MEMBER_JOINED.value,
                    member_name=f"测试成员{i}"
                )
            end_time = time.time()
            
            avg_publish_time = (end_time - start_time) / 50
            
            return {
                "average_publish_time": avg_publish_time,
                "events_per_second": 1 / avg_publish_time if avg_publish_time > 0 else 0,
                "status": "passed" if avg_publish_time < 0.005 else "warning"
            }
            
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def _test_relationship_performance(self) -> Dict[str, Any]:
        """测试关系查找性能"""
        try:
            from systems.social_relationship_manager import SocialRelationshipManager
            
            if len(self.test_characters) < 2:
                return {"error": "Insufficient test characters", "status": "failed"}
            
            # 测试关系查找
            start_time = time.time()
            for _ in range(100):
                SocialRelationshipManager.get_relationship(
                    self.test_characters[0], 
                    self.test_characters[1]
                )
            end_time = time.time()
            
            avg_lookup_time = (end_time - start_time) / 100
            
            return {
                "average_lookup_time": avg_lookup_time,
                "lookups_per_second": 1 / avg_lookup_time if avg_lookup_time > 0 else 0,
                "status": "passed" if avg_lookup_time < 0.001 else "warning"
            }
            
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def _verify_integration(self):
        """验证集成功能"""
        logger.log_info("验证集成功能...")
        
        integration_tests = {
            "ai_director_integration": self._test_ai_director_integration,
            "event_bus_integration": self._test_event_bus_integration,
            "character_integration": self._test_character_integration
        }
        
        integration_results = {}
        
        for test_name, test_func in integration_tests.items():
            try:
                result = test_func()
                integration_results[test_name] = {
                    "success": result,
                    "status": "passed" if result else "failed"
                }
                
            except Exception as e:
                integration_results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "status": "error"
                }
        
        self.verification_results["integration_tests"] = integration_results
        passed_tests = len([r for r in integration_results.values() if r["status"] == "passed"])
        logger.log_info(f"集成测试完成: {passed_tests}/{len(integration_tests)} 通过")
    
    def _test_ai_director_integration(self) -> bool:
        """测试AI导演集成"""
        try:
            # 检查AI导演是否能获取社交事件
            from scripts.ai_directors.diling_director import DilingDirector
            
            director = DilingDirector()
            
            # 尝试收集包含社交事件的上下文数据
            context = director.collect_context_data()
            
            # 验证社交事件数据存在
            return "social_events" in context
            
        except Exception as e:
            logger.log_err(f"AI导演集成测试失败: {e}")
            return False
    
    def _test_event_bus_integration(self) -> bool:
        """测试事件总线集成"""
        try:
            from systems.social_events import publish_sect_event, SocialEventType
            
            if not self.test_characters:
                return False
            
            # 发布事件并验证
            result = publish_sect_event(
                self.test_characters[0],
                "集成测试门派",
                SocialEventType.SECT_FOUNDED.value,
                founder_name=self.test_characters[0].key
            )
            
            return result
            
        except Exception as e:
            logger.log_err(f"事件总线集成测试失败: {e}")
            return False
    
    def _test_character_integration(self) -> bool:
        """测试角色集成"""
        try:
            if not self.test_characters:
                return False
            
            char = self.test_characters[0]
            
            # 测试角色社交方法
            social_info = char.get_social_info()
            
            # 验证社交信息结构
            required_keys = ["basic", "mentorship", "reputation", "relationships"]
            for key in required_keys:
                if key not in social_info:
                    return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"角色集成测试失败: {e}")
            return False
    
    def _cleanup_test_data(self):
        """清理测试数据"""
        logger.log_info("清理测试数据...")
        
        try:
            # 删除测试角色
            for char in self.test_characters:
                char.delete()
            
            # 删除测试门派频道
            test_channels = search.search_object_tag("sect_channel", category="channel_type")
            for channel in test_channels:
                if "测试" in channel.key or "验证" in channel.key:
                    channel.delete()
            
            logger.log_info("测试数据清理完成")
            
        except Exception as e:
            logger.log_err(f"清理测试数据失败: {e}")
    
    def _generate_overall_assessment(self):
        """生成总体评估"""
        results = self.verification_results
        
        # 统计各类测试结果
        component_ok = len([r for r in results["component_integrity"].values() if r["status"] == "ok"])
        component_total = len(results["component_integrity"])
        
        functional_passed = len([r for r in results["functional_tests"].values() if r["status"] == "passed"])
        functional_total = len(results["functional_tests"])
        
        integration_passed = len([r for r in results["integration_tests"].values() if r["status"] == "passed"])
        integration_total = len(results["integration_tests"])
        
        # 计算总体得分
        total_score = 0
        max_score = 0
        
        # 组件完整性权重: 30%
        if component_total > 0:
            total_score += (component_ok / component_total) * 30
        max_score += 30
        
        # 功能测试权重: 40%
        if functional_total > 0:
            total_score += (functional_passed / functional_total) * 40
        max_score += 40
        
        # 集成测试权重: 30%
        if integration_total > 0:
            total_score += (integration_passed / integration_total) * 30
        max_score += 30
        
        # 确定总体状态
        if total_score >= max_score * 0.9:
            overall_status = "excellent"
        elif total_score >= max_score * 0.8:
            overall_status = "good"
        elif total_score >= max_score * 0.6:
            overall_status = "acceptable"
        else:
            overall_status = "needs_improvement"
        
        self.verification_results["overall_status"] = overall_status
        self.verification_results["overall_score"] = total_score
        self.verification_results["max_score"] = max_score
        self.verification_results["score_percentage"] = (total_score / max_score * 100) if max_score > 0 else 0
        
        logger.log_info(f"总体评估: {overall_status} ({total_score:.1f}/{max_score:.1f}, {total_score/max_score*100:.1f}%)")


def run_verification():
    """运行社交系统验证的便利函数"""
    verifier = SocialSystemVerifier()
    return verifier.run_full_verification()


def print_verification_report(results: Dict[str, Any]):
    """打印验证报告"""
    print("\n" + "="*60)
    print("仙侠社交系统验证报告")
    print("="*60)
    
    print(f"\n总体状态: {results.get('overall_status', 'unknown')}")
    if 'score_percentage' in results:
        print(f"总体得分: {results['score_percentage']:.1f}%")
    
    print("\n组件完整性:")
    for component, result in results.get("component_integrity", {}).items():
        status = result.get("status", "unknown")
        print(f"  {component}: {status}")
    
    print("\n功能测试:")
    for test, result in results.get("functional_tests", {}).items():
        status = result.get("status", "unknown")
        time_info = f" ({result.get('execution_time', 0):.3f}s)" if 'execution_time' in result else ""
        print(f"  {test}: {status}{time_info}")
    
    print("\n性能测试:")
    for test, result in results.get("performance_tests", {}).items():
        status = result.get("status", "unknown")
        print(f"  {test}: {status}")
    
    print("\n集成测试:")
    for test, result in results.get("integration_tests", {}).items():
        status = result.get("status", "unknown")
        print(f"  {test}: {status}")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    # 直接运行验证
    results = run_verification()
    print_verification_report(results)
