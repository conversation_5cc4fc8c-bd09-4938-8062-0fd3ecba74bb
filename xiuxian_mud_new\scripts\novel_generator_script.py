"""
小说生成系统主脚本

基于Evennia DefaultScript的智能小说生成系统：
- 双重触发机制：定时触发（1小时）+ 事件触发
- 游戏事件收集和分析
- LLM智能内容生成
- 叙事连贯性管理
- 与三层AI导演系统集成
"""

import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from evennia.scripts.scripts import DefaultScript
from evennia.utils import logger

# 导入系统组件
from systems.event_system import XianxiaEventBus, BaseEvent, EventPriority, BaseEventHandler, EventFilter
from systems.novel_event_collector import NovelEventCollector
from systems.narrative_context_manager import NarrativeContextManager
from systems.novel_content_generator import NovelContentGenerator

# 尝试导入LLM系统
try:
    from evennia.contrib.rpg.llm import llm_request
    LLM_AVAILABLE = True
except ImportError:
    logger.log_warn("Evennia LLM系统不可用，小说生成将使用模拟模式")
    LLM_AVAILABLE = False
    llm_request = None


class NovelEventHandler(BaseEventHandler):
    """小说生成系统的事件处理器"""

    def __init__(self, novel_script):
        """
        初始化事件处理器

        Args:
            novel_script: NovelGeneratorScript实例
        """
        super().__init__()
        self.novel_script = novel_script

    def handle_event(self, event: BaseEvent) -> bool:
        """
        处理事件

        Args:
            event: 要处理的事件

        Returns:
            处理是否成功
        """
        try:
            # 将事件转换为字典格式
            event_data = {
                "type": event.event_type,
                "description": getattr(event, 'description', ''),
                "participants": getattr(event, 'participants', []),
                "location": getattr(event, 'location', ''),
                "significance": getattr(event, 'significance', 0.5),
                "timestamp": event.timestamp,
                "event_id": event.event_id,
                "priority": event.priority.value if hasattr(event.priority, 'value') else str(event.priority)
            }

            # 调用小说生成脚本的事件处理方法
            self.novel_script.on_event_trigger(event_data)

            return True

        except Exception as e:
            logger.log_err(f"小说事件处理器处理事件失败: {e}")
            return False

    def can_handle(self, event: BaseEvent) -> bool:
        """
        检查是否可以处理该事件

        Args:
            event: 要检查的事件

        Returns:
            是否可以处理
        """
        # 检查事件类型是否是我们关心的
        important_event_types = [
            "CultivationBreakthroughEvent",
            "SectJoinEvent",
            "CombatVictoryEvent",
            "TreasureFoundEvent",
            "MasterApprenticeEvent",
            "WorldEvent",
            "CharacterDeathEvent",
            "SectConflictEvent"
        ]

        return event.event_type in important_event_types or event.priority in [EventPriority.HIGH, EventPriority.CRITICAL]


class NovelGeneratorScript(DefaultScript):
    """
    小说生成系统主脚本
    
    基于Evennia DefaultScript的智能小说生成系统，支持：
    - 定时触发小说生成（每小时）
    - 事件驱动的即时叙事生成
    - 智能事件收集和分析
    - LLM驱动的内容生成
    - 叙事连贯性管理
    """
    
    def at_script_creation(self):
        """脚本创建时的初始化"""
        self.key = "novel_generator"
        self.desc = "仙侠MUD智能小说生成系统"
        self.interval = 3600  # 1小时定时触发
        self.persistent = True
        
        # 初始化数据存储
        self._initialize_data_storage()
        
        # 初始化子系统
        self._initialize_subsystems()

        # 初始化缓存系统
        self._initialize_cache()

        # 注册事件监听
        self._register_event_listeners()
        
        # 记录启动信息
        logger.log_info(f"小说生成系统已启动 - {datetime.now()}")
        
    def _initialize_data_storage(self):
        """初始化数据存储结构"""
        # 游戏事件记录（最近1000条）
        self.attributes.add("game_records", [], category="novel")
        
        # 生成的章节（最近50章）
        self.attributes.add("generated_chapters", [], category="novel")
        
        # 叙事上下文
        self.attributes.add("narrative_context", {
            "world_state": {},
            "character_arcs": {},
            "story_timeline": [],
            "current_themes": []
        }, category="novel")
        
        # 系统配置
        self.attributes.add("generator_config", {
            "generation_interval": 3600,  # 1小时
            "max_records": 1000,
            "max_chapters": 50,
            "significance_threshold": 0.7,
            "auto_generation": True
        }, category="novel")
        
        # 性能统计
        self.attributes.add("performance_stats", {
            "total_chapters": 0,
            "total_events_processed": 0,
            "last_generation_time": None,
            "average_generation_time": 0.0,
            "error_count": 0
        }, category="novel")
        
    def _initialize_subsystems(self):
        """初始化子系统"""
        try:
            # 事件收集器
            self.event_collector = NovelEventCollector(self)
            
            # 叙事上下文管理器
            self.context_manager = NarrativeContextManager(self)
            
            # 内容生成器
            self.content_generator = NovelContentGenerator(self)
            
            logger.log_info("小说生成系统子系统初始化完成")
            
        except Exception as e:
            logger.log_err(f"小说生成系统子系统初始化失败: {e}")
            # 设置为模拟模式
            self.event_collector = None
            self.context_manager = None
            self.content_generator = None

    def _initialize_cache(self):
        """初始化缓存系统"""
        try:
            # 内存缓存
            self.cache = {
                "narrative_context": None,
                "narrative_context_timestamp": 0,
                "world_state": None,
                "world_state_timestamp": 0,
                "character_arcs": None,
                "character_arcs_timestamp": 0,
                "recent_events": None,
                "recent_events_timestamp": 0
            }

            # 缓存配置
            self.cache_config = {
                "narrative_context_ttl": 300,  # 5分钟
                "world_state_ttl": 600,        # 10分钟
                "character_arcs_ttl": 900,     # 15分钟
                "recent_events_ttl": 180       # 3分钟
            }

            logger.log_info("缓存系统已初始化")

        except Exception as e:
            logger.log_err(f"缓存系统初始化失败: {e}")
            self.cache = {}
            self.cache_config = {}

    def _get_cached_data(self, cache_key: str, ttl_key: str):
        """获取缓存数据"""
        try:
            if not hasattr(self, 'cache') or cache_key not in self.cache:
                return None

            current_time = time.time()
            cache_timestamp = self.cache.get(f"{cache_key}_timestamp", 0)
            ttl = self.cache_config.get(ttl_key, 300)

            # 检查缓存是否过期
            if current_time - cache_timestamp > ttl:
                return None

            return self.cache.get(cache_key)

        except Exception as e:
            logger.log_err(f"获取缓存数据失败: {e}")
            return None

    def _set_cached_data(self, cache_key: str, data):
        """设置缓存数据"""
        try:
            if not hasattr(self, 'cache'):
                return

            current_time = time.time()
            self.cache[cache_key] = data
            self.cache[f"{cache_key}_timestamp"] = current_time

        except Exception as e:
            logger.log_err(f"设置缓存数据失败: {e}")

    def _clear_cache(self, cache_key: str = None):
        """清理缓存"""
        try:
            if not hasattr(self, 'cache'):
                return

            if cache_key:
                # 清理特定缓存
                if cache_key in self.cache:
                    del self.cache[cache_key]
                if f"{cache_key}_timestamp" in self.cache:
                    del self.cache[f"{cache_key}_timestamp"]
            else:
                # 清理所有缓存
                for key in list(self.cache.keys()):
                    if not key.endswith('_timestamp'):
                        self.cache[key] = None
                        self.cache[f"{key}_timestamp"] = 0

        except Exception as e:
            logger.log_err(f"清理缓存失败: {e}")

    def _register_event_listeners(self):
        """注册事件监听器"""
        try:
            # 获取事件总线实例
            event_bus = XianxiaEventBus.get_instance()

            # 创建小说事件处理器
            self.novel_event_handler = NovelEventHandler(self)

            # 创建事件过滤器 - 只处理重要事件
            novel_filter = EventFilter(
                min_priority=EventPriority.NORMAL,  # 处理普通及以上优先级的事件
                event_types=[
                    "CultivationBreakthroughEvent",  # 修为突破
                    "SectJoinEvent",                 # 加入门派
                    "CombatVictoryEvent",            # 战斗胜利
                    "TreasureFoundEvent",            # 发现宝物
                    "MasterApprenticeEvent",         # 师徒关系
                    "WorldEvent",                    # 世界事件
                    "CharacterDeathEvent",           # 角色死亡
                    "SectConflictEvent",             # 门派冲突
                    "CelestialAnomalyEvent",         # 天象异常
                    "SpiritualTideEvent"             # 灵气潮汐
                ]
            )

            # 注册事件处理器
            event_bus.register_handler("novel_generator", self.novel_event_handler, novel_filter)

            logger.log_info("小说生成系统事件处理器已注册到事件总线")

        except Exception as e:
            logger.log_err(f"事件监听器注册失败: {e}")
            # 设置为None，表示事件处理不可用
            self.novel_event_handler = None
            
    def at_repeat(self):
        """定时触发小说生成（每小时）"""
        try:
            config = self.attributes.get("generator_config", {})
            
            # 检查是否启用自动生成
            if not config.get("auto_generation", True):
                return
                
            # 检查是否有足够的内容生成章节
            if self.should_generate_content():
                self.generate_novel_chapter()
                
        except Exception as e:
            logger.log_err(f"定时小说生成失败: {e}")
            self._update_error_stats()
            
    def on_event_trigger(self, event_data: Dict[str, Any]):
        """事件触发的小说生成"""
        try:
            # 收集事件
            if self.event_collector:
                self.event_collector.collect_event(event_data)
            
            # 评估事件重要性
            if self.is_significant_event(event_data):
                # 生成事件叙事
                self.generate_event_narrative(event_data)
                
        except Exception as e:
            logger.log_err(f"事件触发小说生成失败: {e}")
            self._update_error_stats()
            
    def should_generate_content(self) -> bool:
        """判断是否应该生成内容"""
        try:
            # 检查是否有足够的新事件
            records = self.attributes.get("game_records", [])
            if len(records) < 10:  # 至少需要10个事件
                return False
                
            # 检查上次生成时间
            stats = self.attributes.get("performance_stats", {})
            last_time = stats.get("last_generation_time")
            
            if last_time:
                time_since_last = time.time() - last_time
                min_interval = 1800  # 最少间隔30分钟
                if time_since_last < min_interval:
                    return False
                    
            return True
            
        except Exception as e:
            logger.log_err(f"内容生成判断失败: {e}")
            return False
            
    def is_significant_event(self, event_data: Dict[str, Any]) -> bool:
        """判断事件是否具有叙事重要性"""
        try:
            if self.event_collector:
                return self.event_collector.evaluate_significance(event_data)
            else:
                # 简单的重要性判断
                important_types = [
                    "cultivation_breakthrough",
                    "character_death", 
                    "world_event",
                    "sect_conflict"
                ]
                return event_data.get("type") in important_types
                
        except Exception as e:
            logger.log_err(f"事件重要性评估失败: {e}")
            return False
            
    def generate_novel_chapter(self) -> Optional[str]:
        """生成完整的小说章节"""
        start_time = time.perf_counter()
        
        try:
            logger.log_info("开始生成小说章节...")
            
            # 构建叙事上下文
            if self.context_manager:
                context = self.context_manager.build_narrative_context()
            else:
                context = self._build_basic_context()
                
            # 生成章节内容
            if self.content_generator:
                chapter_content = self.content_generator.generate_chapter(context)
            else:
                chapter_content = self._generate_fallback_chapter(context)
                
            if chapter_content:
                # 保存章节
                self._save_chapter(chapter_content, context)
                
                # 更新统计信息
                self._update_generation_stats(start_time)
                
                # 通知玩家
                self._notify_new_chapter(chapter_content)
                
                logger.log_info("小说章节生成完成")
                return chapter_content
            else:
                logger.log_warn("小说章节生成失败：内容为空")
                return None
                
        except Exception as e:
            logger.log_err(f"小说章节生成失败: {e}")
            self._update_error_stats()
            return None
            
    def generate_event_narrative(self, event_data: Dict[str, Any]) -> Optional[str]:
        """生成事件叙事"""
        try:
            logger.log_info(f"生成事件叙事: {event_data.get('type', 'unknown')}")
            
            # 构建事件上下文
            if self.context_manager:
                context = self.context_manager.build_event_context(event_data)
            else:
                context = {"event": event_data}
                
            # 生成事件叙事
            if self.content_generator:
                narrative = self.content_generator.generate_event_narrative(event_data, context)
            else:
                narrative = self._generate_fallback_narrative(event_data)
                
            if narrative:
                # 保存事件叙事
                self._save_event_narrative(narrative, event_data)
                
                logger.log_info("事件叙事生成完成")
                return narrative
            else:
                logger.log_warn("事件叙事生成失败：内容为空")
                return None
                
        except Exception as e:
            logger.log_err(f"事件叙事生成失败: {e}")
            return None

    def _build_basic_context(self) -> Dict[str, Any]:
        """构建基础叙事上下文（备用方案）"""
        records = self.attributes.get("game_records", [])
        recent_records = records[-20:] if records else []

        return {
            "recent_events": recent_records,
            "world_state": {"time": datetime.now().isoformat()},
            "character_arcs": {},
            "previous_chapters": self._get_recent_chapters(3)
        }

    def _generate_fallback_chapter(self, context: Dict[str, Any]) -> str:
        """生成备用章节（当LLM不可用时）"""
        recent_events = context.get("recent_events", [])

        if not recent_events:
            return "在这个宁静的修仙世界中，一切都在按照天道的安排缓缓进行着..."

        # 简单的事件总结
        event_summary = []
        for event in recent_events[-5:]:
            event_type = event.get("type", "unknown")
            description = event.get("description", "")
            if description:
                event_summary.append(f"- {description}")

        chapter = f"""
第{len(self.attributes.get('generated_chapters', [])) + 1}章

近日来，修仙界中发生了诸多事件：

{chr(10).join(event_summary)}

这些事件如涟漪般在修仙界中传播，影响着每一个修仙者的命运...
"""
        return chapter.strip()

    def _generate_fallback_narrative(self, event_data: Dict[str, Any]) -> str:
        """生成备用事件叙事"""
        event_type = event_data.get("type", "unknown")
        description = event_data.get("description", "")

        return f"【{event_type}】{description}"

    def _save_chapter(self, content: str, context: Dict[str, Any]):
        """保存生成的章节"""
        chapters = self.attributes.get("generated_chapters", [])

        chapter_data = {
            "timestamp": time.time(),
            "content": content,
            "context": context,
            "chapter_number": len(chapters) + 1,
            "word_count": len(content)
        }

        chapters.append(chapter_data)

        # 保持最近50章
        config = self.attributes.get("generator_config", {})
        max_chapters = config.get("max_chapters", 50)
        if len(chapters) > max_chapters:
            chapters = chapters[-max_chapters:]

        self.attributes.add("generated_chapters", chapters, category="novel")

    def _save_event_narrative(self, narrative: str, event_data: Dict[str, Any]):
        """保存事件叙事"""
        # 将事件叙事添加到最新章节或创建新的事件记录
        chapters = self.attributes.get("generated_chapters", [])

        if chapters:
            # 添加到最新章节的事件叙事中
            latest_chapter = chapters[-1]
            if "event_narratives" not in latest_chapter:
                latest_chapter["event_narratives"] = []
            latest_chapter["event_narratives"].append({
                "timestamp": time.time(),
                "narrative": narrative,
                "event": event_data
            })
            self.attributes.add("generated_chapters", chapters, category="novel")

    def _get_recent_chapters(self, count: int) -> List[Dict[str, Any]]:
        """获取最近的章节"""
        chapters = self.attributes.get("generated_chapters", [])
        return chapters[-count:] if chapters else []

    def _update_generation_stats(self, start_time: float):
        """更新生成统计信息"""
        stats = self.attributes.get("performance_stats", {})

        generation_time = time.perf_counter() - start_time

        stats["total_chapters"] = stats.get("total_chapters", 0) + 1
        stats["last_generation_time"] = time.time()

        # 更新平均生成时间
        total_time = stats.get("average_generation_time", 0.0) * (stats["total_chapters"] - 1)
        stats["average_generation_time"] = (total_time + generation_time) / stats["total_chapters"]

        self.attributes.add("performance_stats", stats, category="novel")

    def _update_error_stats(self):
        """更新错误统计"""
        stats = self.attributes.get("performance_stats", {})
        stats["error_count"] = stats.get("error_count", 0) + 1
        self.attributes.add("performance_stats", stats, category="novel")

    def _notify_new_chapter(self, content: str):
        """通知玩家新章节生成"""
        try:
            # 向所有在线玩家发送通知
            from evennia import GLOBAL_SCRIPTS

            # 获取章节标题（前50个字符）
            title = content[:50].replace('\n', ' ').strip()
            if len(content) > 50:
                title += "..."

            message = f"|y【小说更新】|n 新章节已生成：{title}"

            # 通过全局频道发送通知
            from evennia.comms.models import ChannelDB
            try:
                novel_channel = ChannelDB.objects.get(db_key="novel")
                novel_channel.msg(message)
            except ChannelDB.DoesNotExist:
                # 如果没有小说频道，向所有在线玩家发送
                from evennia import SESSION_HANDLER
                SESSION_HANDLER.announce_all(message)

        except Exception as e:
            logger.log_err(f"新章节通知发送失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        stats = self.attributes.get("performance_stats", {})
        config = self.attributes.get("generator_config", {})
        records = self.attributes.get("game_records", [])
        chapters = self.attributes.get("generated_chapters", [])

        return {
            "system_status": "运行中" if self.is_active else "已停止",
            "total_chapters": stats.get("total_chapters", 0),
            "total_events": len(records),
            "last_generation": stats.get("last_generation_time"),
            "average_generation_time": stats.get("average_generation_time", 0.0),
            "error_count": stats.get("error_count", 0),
            "auto_generation": config.get("auto_generation", True),
            "subsystems": {
                "event_collector": self.event_collector is not None,
                "context_manager": self.context_manager is not None,
                "content_generator": self.content_generator is not None,
                "llm_available": LLM_AVAILABLE
            }
        }

    def manual_generate(self) -> Optional[str]:
        """手动触发章节生成"""
        logger.log_info("手动触发小说章节生成")
        return self.generate_novel_chapter()

    def update_config(self, new_config: Dict[str, Any]):
        """更新系统配置"""
        config = self.attributes.get("generator_config", {})
        config.update(new_config)
        self.attributes.add("generator_config", config, category="novel")

        # 如果更新了生成间隔，重新设置脚本间隔
        if "generation_interval" in new_config:
            self.interval = new_config["generation_interval"]

        logger.log_info(f"小说生成系统配置已更新: {new_config}")

    def at_script_delete(self):
        """脚本删除时的清理工作"""
        try:
            # 从事件总线注销事件处理器
            if hasattr(self, 'novel_event_handler') and self.novel_event_handler:
                event_bus = XianxiaEventBus.get_instance()
                event_bus.unregister_handler("novel_generator")

            logger.log_info("小说生成系统已停止")

        except Exception as e:
            logger.log_err(f"小说生成系统停止时出错: {e}")
