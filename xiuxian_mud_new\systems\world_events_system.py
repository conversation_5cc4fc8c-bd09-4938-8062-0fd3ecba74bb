"""
世界动态事件系统

为天道导演提供世界级事件管理功能：
- 世界级事件生成和管理
- 历史进程跟踪
- 跨区域事件协调
- 重大事件影响评估
"""

import time
import random
import json
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from evennia.utils import logger
from evennia.scripts.scripts import DefaultScript

from .event_system import BaseEvent, EventPriority, publish_event


class WorldEventType(Enum):
    """世界事件类型"""
    CELESTIAL_ANOMALY = "celestial_anomaly"  # 天象异常
    CROSS_SECT_CONFLICT = "cross_sect_conflict"  # 跨门派冲突
    ANCIENT_RELIC_DISCOVERY = "ancient_relic_discovery"  # 上古遗迹发现
    SPIRITUAL_TIDE_MAJOR = "spiritual_tide_major"  # 重大灵气变化
    IMMORTAL_INTERVENTION = "immortal_intervention"  # 仙人干预
    NATURAL_DISASTER = "natural_disaster"  # 天灾
    RESOURCE_DISCOVERY = "resource_discovery"  # 资源发现
    HISTORICAL_MILESTONE = "historical_milestone"  # 历史里程碑


class WorldEventSeverity(Enum):
    """世界事件严重程度"""
    MINOR = "minor"  # 轻微
    MODERATE = "moderate"  # 中等
    MAJOR = "major"  # 重大
    CATASTROPHIC = "catastrophic"  # 灾难性


@dataclass
class WorldEvent:
    """世界事件数据结构"""
    event_id: str
    event_type: WorldEventType
    severity: WorldEventSeverity
    title: str
    description: str
    location: str
    start_time: float
    duration: int
    effects: Dict[str, Any]
    conditions: List[str]
    participants: List[str]
    status: str  # planned, active, completed, cancelled
    historical_impact: float
    cross_regional: bool


class WorldEventsSystem(DefaultScript):
    """
    世界动态事件系统
    
    管理修仙界的重大世界级事件
    """
    
    def at_script_creation(self):
        """系统初始化"""
        self.key = "world_events_system"
        self.desc = "世界动态事件系统"
        self.persistent = True
        self.interval = 300  # 5分钟检查一次
        
        # 初始化事件状态
        self.db.active_events = {}
        self.db.event_history = []
        self.db.event_templates = self._initialize_event_templates()
        self.db.world_state_modifiers = {}
        self.db.historical_timeline = []
        
        # 事件生成配置
        self.db.generation_config = {
            "base_event_chance": 0.1,  # 基础事件生成概率
            "severity_weights": {
                WorldEventSeverity.MINOR: 0.5,
                WorldEventSeverity.MODERATE: 0.3,
                WorldEventSeverity.MAJOR: 0.15,
                WorldEventSeverity.CATASTROPHIC: 0.05
            },
            "type_weights": {
                WorldEventType.CELESTIAL_ANOMALY: 0.2,
                WorldEventType.CROSS_SECT_CONFLICT: 0.15,
                WorldEventType.ANCIENT_RELIC_DISCOVERY: 0.1,
                WorldEventType.SPIRITUAL_TIDE_MAJOR: 0.2,
                WorldEventType.IMMORTAL_INTERVENTION: 0.05,
                WorldEventType.NATURAL_DISASTER: 0.15,
                WorldEventType.RESOURCE_DISCOVERY: 0.1,
                WorldEventType.HISTORICAL_MILESTONE: 0.05
            }
        }
        
        logger.log_info("世界动态事件系统已初始化")
    
    def at_repeat(self):
        """定期检查和更新世界事件"""
        try:
            # 更新活跃事件状态
            self._update_active_events()
            
            # 检查事件生成条件
            self._check_event_generation()
            
            # 处理事件效果
            self._process_event_effects()
            
            # 清理过期事件
            self._cleanup_expired_events()
            
        except Exception as e:
            logger.log_err(f"世界事件系统更新失败: {e}")
    
    def generate_world_event(self, event_type: Optional[WorldEventType] = None, 
                           severity: Optional[WorldEventSeverity] = None,
                           forced: bool = False) -> Optional[WorldEvent]:
        """
        生成世界事件
        
        Args:
            event_type: 指定事件类型，None为随机
            severity: 指定严重程度，None为随机
            forced: 是否强制生成
            
        Returns:
            生成的世界事件，失败返回None
        """
        try:
            # 检查生成条件
            if not forced and not self._should_generate_event():
                return None
            
            # 确定事件类型和严重程度
            if not event_type:
                event_type = self._select_event_type()
            if not severity:
                severity = self._select_event_severity()
            
            # 生成事件
            world_event = self._create_world_event(event_type, severity)
            
            if world_event:
                # 注册事件
                self._register_event(world_event)
                
                # 发布事件通知
                self._publish_event_notification(world_event)
                
                logger.log_info(f"生成世界事件: {world_event.title} ({world_event.severity.value})")
                
                return world_event
            
            return None
            
        except Exception as e:
            logger.log_err(f"生成世界事件失败: {e}")
            return None
    
    def get_active_events(self) -> List[WorldEvent]:
        """获取所有活跃的世界事件"""
        return [event for event in self.db.active_events.values() 
                if event.status == "active"]
    
    def get_event_by_id(self, event_id: str) -> Optional[WorldEvent]:
        """根据ID获取事件"""
        return self.db.active_events.get(event_id)
    
    def complete_event(self, event_id: str, results: Dict[str, Any] = None):
        """完成事件"""
        event = self.get_event_by_id(event_id)
        if event and event.status == "active":
            event.status = "completed"
            
            # 记录历史影响
            self._record_historical_impact(event, results or {})
            
            # 移除世界状态修饰符
            self._remove_world_modifiers(event)
            
            logger.log_info(f"世界事件已完成: {event.title}")
    
    def cancel_event(self, event_id: str, reason: str = ""):
        """取消事件"""
        event = self.get_event_by_id(event_id)
        if event and event.status in ["planned", "active"]:
            event.status = "cancelled"
            
            # 移除世界状态修饰符
            self._remove_world_modifiers(event)
            
            logger.log_info(f"世界事件已取消: {event.title} (原因: {reason})")
    
    def get_world_state_modifiers(self) -> Dict[str, Any]:
        """获取当前世界状态修饰符"""
        return self.db.world_state_modifiers.copy()
    
    def get_historical_timeline(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取历史时间线"""
        timeline = self.db.historical_timeline
        return timeline[-limit:] if limit > 0 else timeline
    
    def _should_generate_event(self) -> bool:
        """检查是否应该生成事件"""
        base_chance = self.db.generation_config["base_event_chance"]
        
        # 根据当前活跃事件数量调整概率
        active_count = len(self.get_active_events())
        if active_count >= 5:  # 最多同时5个重大事件
            return False
        
        # 根据最近事件频率调整
        recent_events = [e for e in self.db.event_history 
                        if time.time() - e.get("timestamp", 0) < 3600]  # 1小时内
        if len(recent_events) >= 3:  # 1小时内最多3个事件
            base_chance *= 0.5
        
        return random.random() < base_chance
    
    def _select_event_type(self) -> WorldEventType:
        """选择事件类型"""
        weights = self.db.generation_config["type_weights"]
        types = list(weights.keys())
        probabilities = list(weights.values())
        
        return random.choices(types, weights=probabilities)[0]
    
    def _select_event_severity(self) -> WorldEventSeverity:
        """选择事件严重程度"""
        weights = self.db.generation_config["severity_weights"]
        severities = list(weights.keys())
        probabilities = list(weights.values())
        
        return random.choices(severities, weights=probabilities)[0]
    
    def _create_world_event(self, event_type: WorldEventType, 
                          severity: WorldEventSeverity) -> Optional[WorldEvent]:
        """创建世界事件"""
        try:
            # 获取事件模板
            template = self.db.event_templates.get(event_type)
            if not template:
                logger.log_warn(f"未找到事件类型模板: {event_type}")
                return None
            
            # 生成事件ID
            event_id = f"{event_type.value}_{int(time.time())}_{random.randint(1000, 9999)}"
            
            # 根据严重程度调整模板
            adjusted_template = self._adjust_template_by_severity(template, severity)
            
            # 创建事件实例
            world_event = WorldEvent(
                event_id=event_id,
                event_type=event_type,
                severity=severity,
                title=adjusted_template["title"],
                description=adjusted_template["description"],
                location=self._select_event_location(event_type),
                start_time=time.time(),
                duration=adjusted_template["duration"],
                effects=adjusted_template["effects"],
                conditions=adjusted_template["conditions"],
                participants=adjusted_template.get("participants", []),
                status="planned",
                historical_impact=adjusted_template["historical_impact"],
                cross_regional=adjusted_template.get("cross_regional", False)
            )
            
            return world_event
            
        except Exception as e:
            logger.log_err(f"创建世界事件失败: {e}")
            return None
    
    def _register_event(self, event: WorldEvent):
        """注册事件"""
        self.db.active_events[event.event_id] = event
        
        # 记录到历史
        event_record = {
            "event_id": event.event_id,
            "type": event.event_type.value,
            "severity": event.severity.value,
            "title": event.title,
            "timestamp": event.start_time,
            "status": "registered"
        }
        self.db.event_history.append(event_record)
        
        # 应用世界状态修饰符
        self._apply_world_modifiers(event)
    
    def _publish_event_notification(self, event: WorldEvent):
        """发布事件通知"""
        notification_event = BaseEvent(
            event_type="world_event_generated",
            source_id=self.key,
            data={
                "world_event": {
                    "event_id": event.event_id,
                    "type": event.event_type.value,
                    "severity": event.severity.value,
                    "title": event.title,
                    "location": event.location,
                    "cross_regional": event.cross_regional
                }
            },
            priority=EventPriority.HIGH if event.severity in [WorldEventSeverity.MAJOR, WorldEventSeverity.CATASTROPHIC] else EventPriority.NORMAL
        )
        
        publish_event(notification_event)
    
    def _update_active_events(self):
        """更新活跃事件状态"""
        current_time = time.time()
        
        for event in list(self.db.active_events.values()):
            if event.status == "planned":
                # 激活计划中的事件
                event.status = "active"
                logger.log_info(f"世界事件已激活: {event.title}")
                
            elif event.status == "active":
                # 检查事件是否应该结束
                if current_time >= event.start_time + event.duration:
                    self.complete_event(event.event_id)
    
    def _check_event_generation(self):
        """检查事件生成条件"""
        # 这里可以添加更复杂的事件生成逻辑
        # 例如基于世界状态、玩家行为等触发特定事件
        pass
    
    def _process_event_effects(self):
        """处理事件效果"""
        for event in self.get_active_events():
            # 持续性效果处理
            self._apply_continuous_effects(event)
    
    def _cleanup_expired_events(self):
        """清理过期事件"""
        current_time = time.time()
        expired_events = []
        
        for event_id, event in self.db.active_events.items():
            if event.status == "completed" and current_time - event.start_time > 86400:  # 24小时后清理
                expired_events.append(event_id)
        
        for event_id in expired_events:
            del self.db.active_events[event_id]
    
    def _initialize_event_templates(self) -> Dict[WorldEventType, Dict[str, Any]]:
        """初始化事件模板"""
        return {
            WorldEventType.CELESTIAL_ANOMALY: {
                "title": "天象异常",
                "description": "天空出现异常现象，灵气波动剧烈",
                "duration": 7200,  # 2小时
                "effects": {"global_spiritual_energy": 1.2},
                "conditions": ["天象观测"],
                "historical_impact": 0.3,
                "cross_regional": True
            },
            WorldEventType.CROSS_SECT_CONFLICT: {
                "title": "门派冲突",
                "description": "大型门派之间爆发冲突",
                "duration": 14400,  # 4小时
                "effects": {"sect_relations": -0.2},
                "conditions": ["门派关系紧张"],
                "historical_impact": 0.5,
                "cross_regional": True
            },
            WorldEventType.ANCIENT_RELIC_DISCOVERY: {
                "title": "上古遗迹现世",
                "description": "发现上古修仙者留下的遗迹",
                "duration": 10800,  # 3小时
                "effects": {"ancient_knowledge": 1.0, "rare_resources": 1.5},
                "conditions": ["考古发现"],
                "historical_impact": 0.7,
                "cross_regional": False
            },
            WorldEventType.SPIRITUAL_TIDE_MAJOR: {
                "title": "灵气大潮",
                "description": "修仙界灵气发生重大变化",
                "duration": 21600,  # 6小时
                "effects": {"global_spiritual_energy": 1.5},
                "conditions": ["灵气异常"],
                "historical_impact": 0.4,
                "cross_regional": True
            },
            WorldEventType.IMMORTAL_INTERVENTION: {
                "title": "仙人降临",
                "description": "高阶仙人干预凡间事务",
                "duration": 3600,  # 1小时
                "effects": {"divine_blessing": 1.0},
                "conditions": ["天地异象"],
                "historical_impact": 0.9,
                "cross_regional": True
            }
        }
    
    def _adjust_template_by_severity(self, template: Dict[str, Any], 
                                   severity: WorldEventSeverity) -> Dict[str, Any]:
        """根据严重程度调整模板"""
        adjusted = template.copy()
        
        severity_multipliers = {
            WorldEventSeverity.MINOR: 0.5,
            WorldEventSeverity.MODERATE: 1.0,
            WorldEventSeverity.MAJOR: 1.5,
            WorldEventSeverity.CATASTROPHIC: 2.0
        }
        
        multiplier = severity_multipliers[severity]
        
        # 调整持续时间
        adjusted["duration"] = int(adjusted["duration"] * multiplier)
        
        # 调整效果强度
        for effect_key, effect_value in adjusted["effects"].items():
            if isinstance(effect_value, (int, float)):
                adjusted["effects"][effect_key] = effect_value * multiplier
        
        # 调整历史影响
        adjusted["historical_impact"] *= multiplier
        
        # 调整标题
        severity_prefixes = {
            WorldEventSeverity.MINOR: "轻微",
            WorldEventSeverity.MODERATE: "",
            WorldEventSeverity.MAJOR: "重大",
            WorldEventSeverity.CATASTROPHIC: "灾难性"
        }
        
        prefix = severity_prefixes[severity]
        if prefix:
            adjusted["title"] = f"{prefix}{adjusted['title']}"
        
        return adjusted
    
    def _select_event_location(self, event_type: WorldEventType) -> str:
        """选择事件发生地点"""
        locations = {
            WorldEventType.CELESTIAL_ANOMALY: ["天空", "九霄云外", "星空"],
            WorldEventType.CROSS_SECT_CONFLICT: ["青云山", "天音山", "焚香谷", "万毒门"],
            WorldEventType.ANCIENT_RELIC_DISCOVERY: ["神秘秘境", "上古遗址", "深山老林"],
            WorldEventType.SPIRITUAL_TIDE_MAJOR: ["修仙界各地", "灵气源头", "天地之间"],
            WorldEventType.IMMORTAL_INTERVENTION: ["仙界", "凡间", "天地交界"]
        }
        
        possible_locations = locations.get(event_type, ["未知地点"])
        return random.choice(possible_locations)
    
    def _apply_world_modifiers(self, event: WorldEvent):
        """应用世界状态修饰符"""
        for effect_key, effect_value in event.effects.items():
            if effect_key not in self.db.world_state_modifiers:
                self.db.world_state_modifiers[effect_key] = 1.0
            
            if isinstance(effect_value, (int, float)):
                self.db.world_state_modifiers[effect_key] *= effect_value
    
    def _remove_world_modifiers(self, event: WorldEvent):
        """移除世界状态修饰符"""
        for effect_key, effect_value in event.effects.items():
            if effect_key in self.db.world_state_modifiers and isinstance(effect_value, (int, float)):
                # 反向应用效果
                self.db.world_state_modifiers[effect_key] /= effect_value
                
                # 如果接近1.0，则移除修饰符
                if abs(self.db.world_state_modifiers[effect_key] - 1.0) < 0.01:
                    del self.db.world_state_modifiers[effect_key]
    
    def _apply_continuous_effects(self, event: WorldEvent):
        """应用持续性效果"""
        # 这里可以添加持续性效果的处理逻辑
        pass
    
    def _record_historical_impact(self, event: WorldEvent, results: Dict[str, Any]):
        """记录历史影响"""
        historical_record = {
            "timestamp": time.time(),
            "event_id": event.event_id,
            "title": event.title,
            "type": event.event_type.value,
            "severity": event.severity.value,
            "impact": event.historical_impact,
            "results": results,
            "duration": event.duration,
            "location": event.location
        }
        
        self.db.historical_timeline.append(historical_record)
        
        # 保留最近1000个历史记录
        if len(self.db.historical_timeline) > 1000:
            self.db.historical_timeline = self.db.historical_timeline[-1000:]


def get_world_events_system() -> Optional[WorldEventsSystem]:
    """获取世界事件系统实例"""
    from evennia import search_script
    
    scripts = search_script("world_events_system")
    return scripts[0] if scripts else None
