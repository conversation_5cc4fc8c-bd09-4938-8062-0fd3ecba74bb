"""
集成测试框架基础设施

为仙侠MUD系统集成测试提供统一的测试框架，包括：
- 集成测试基类和工具函数
- 系统状态监控和快照功能
- 事件链追踪机制
- 性能指标收集器
- 测试报告生成器
"""

import time
import json
import threading
import psutil
import traceback
from typing import Dict, Any, List, Optional, Callable
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from datetime import datetime

from evennia.utils.test_resources import BaseEvenniaTest
from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err
from evennia import DefaultScript

from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..systems.tag_property_system import TagPropertyQueryManager
from ..systems.query_interfaces import AIDirectorQueryInterface


@dataclass
class SystemSnapshot:
    """系统状态快照"""
    timestamp: float
    system_name: str
    component_states: Dict[str, Any]
    performance_metrics: Dict[str, float]
    active_events: List[Dict[str, Any]]
    memory_usage: float
    cpu_usage: float


@dataclass
class EventTraceRecord:
    """事件追踪记录"""
    event_id: str
    event_type: str
    publish_time: float
    processing_chain: List[Dict[str, Any]]
    total_latency: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    event_processing_latency: float
    query_response_time: float
    active_connections: int
    event_queue_size: int


class EventChainTracker:
    """事件链追踪器"""

    def __init__(self):
        self.active_traces = {}
        self.completed_traces = []
        self.lock = threading.Lock()

    def start_trace(self, event: BaseEvent) -> str:
        """开始追踪事件"""
        trace_id = f"{event.event_type}_{event.event_id}_{time.time()}"

        with self.lock:
            self.active_traces[trace_id] = EventTraceRecord(
                event_id=event.event_id,
                event_type=event.event_type,
                publish_time=time.time(),
                processing_chain=[],
                total_latency=0.0,
                success=False
            )

        return trace_id

    def add_processing_step(self, trace_id: str, system_name: str,
                          start_time: float, end_time: float,
                          success: bool, details: Dict[str, Any] = None):
        """添加处理步骤"""
        with self.lock:
            if trace_id in self.active_traces:
                step = {
                    "system": system_name,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "success": success,
                    "details": details or {}
                }
                self.active_traces[trace_id].processing_chain.append(step)

    def complete_trace(self, trace_id: str, success: bool, error_message: str = None):
        """完成事件追踪"""
        with self.lock:
            if trace_id in self.active_traces:
                trace = self.active_traces.pop(trace_id)
                trace.total_latency = time.time() - trace.publish_time
                trace.success = success
                trace.error_message = error_message
                self.completed_traces.append(trace)

    def get_trace_statistics(self) -> Dict[str, Any]:
        """获取追踪统计信息"""
        with self.lock:
            if not self.completed_traces:
                return {"total_traces": 0}

            successful_traces = [t for t in self.completed_traces if t.success]
            failed_traces = [t for t in self.completed_traces if not t.success]

            latencies = [t.total_latency for t in self.completed_traces]
            avg_latency = sum(latencies) / len(latencies) if latencies else 0

            return {
                "total_traces": len(self.completed_traces),
                "successful_traces": len(successful_traces),
                "failed_traces": len(failed_traces),
                "success_rate": len(successful_traces) / len(self.completed_traces) * 100,
                "average_latency": avg_latency,
                "max_latency": max(latencies) if latencies else 0,
                "min_latency": min(latencies) if latencies else 0
            }


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, collection_interval: float = 1.0):
        self.collection_interval = collection_interval
        self.metrics_history = deque(maxlen=1000)
        self.monitoring = False
        self.monitor_thread = None
        self.lock = threading.Lock()

    def start_monitoring(self):
        """开始性能监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._collect_metrics)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()

    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)

    def _collect_metrics(self):
        """收集性能指标"""
        while self.monitoring:
            try:
                # 收集系统性能指标
                cpu_usage = psutil.cpu_percent()
                memory_info = psutil.virtual_memory()
                memory_usage = memory_info.percent

                # 收集事件总线指标
                event_queue_size = self._get_event_queue_size()

                # 收集查询性能指标
                query_response_time = self._measure_query_performance()

                metrics = PerformanceMetrics(
                    timestamp=time.time(),
                    cpu_usage=cpu_usage,
                    memory_usage=memory_usage,
                    event_processing_latency=0.0,  # 将由事件追踪器提供
                    query_response_time=query_response_time,
                    active_connections=0,  # 可以从Evennia获取
                    event_queue_size=event_queue_size
                )

                with self.lock:
                    self.metrics_history.append(metrics)

                time.sleep(self.collection_interval)

            except Exception as e:
                log_err(f"性能监控收集指标失败: {e}")
                time.sleep(self.collection_interval)

    def _get_event_queue_size(self) -> int:
        """获取事件队列大小"""
        try:
            # 查找事件总线脚本
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                # 假设事件总线有获取队列大小的方法
                if hasattr(event_bus, 'get_queue_size'):
                    return event_bus.get_queue_size()
            return 0
        except Exception:
            return 0

    def _measure_query_performance(self) -> float:
        """测量查询性能"""
        try:
            start_time = time.time()
            # 执行一个简单的查询测试
            TagPropertyQueryManager.find_characters_by_realm("练气")
            end_time = time.time()
            return (end_time - start_time) * 1000  # 转换为毫秒
        except Exception:
            return 0.0

    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        with self.lock:
            return self.metrics_history[-1] if self.metrics_history else None

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        with self.lock:
            if not self.metrics_history:
                return {"no_data": True}

            cpu_values = [m.cpu_usage for m in self.metrics_history]
            memory_values = [m.memory_usage for m in self.metrics_history]
            query_times = [m.query_response_time for m in self.metrics_history]

            return {
                "collection_period": len(self.metrics_history),
                "cpu_usage": {
                    "avg": sum(cpu_values) / len(cpu_values),
                    "max": max(cpu_values),
                    "min": min(cpu_values)
                },
                "memory_usage": {
                    "avg": sum(memory_values) / len(memory_values),
                    "max": max(memory_values),
                    "min": min(memory_values)
                },
                "query_response_time": {
                    "avg": sum(query_times) / len(query_times),
                    "max": max(query_times),
                    "min": min(query_times)
                }
            }


class SystemStateManager:
    """系统状态管理器"""

    def __init__(self):
        self.snapshots = []
        self.system_components = {
            "event_bus": self._capture_event_bus_state,
            "ai_directors": self._capture_ai_directors_state,
            "tag_property": self._capture_tag_property_state,
            "world_system": self._capture_world_system_state,
            "npc_system": self._capture_npc_system_state,
            "combat_system": self._capture_combat_system_state,
            "social_system": self._capture_social_system_state,
            "novel_system": self._capture_novel_system_state
        }

    def capture_system_snapshot(self, system_name: str = "all") -> SystemSnapshot:
        """捕获系统状态快照"""
        timestamp = time.time()

        if system_name == "all":
            component_states = {}
            for name, capture_func in self.system_components.items():
                try:
                    component_states[name] = capture_func()
                except Exception as e:
                    component_states[name] = {"error": str(e)}
        else:
            capture_func = self.system_components.get(system_name)
            if capture_func:
                component_states = {system_name: capture_func()}
            else:
                component_states = {"error": f"Unknown system: {system_name}"}

        # 获取性能指标
        cpu_usage = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        memory_usage = memory_info.percent

        snapshot = SystemSnapshot(
            timestamp=timestamp,
            system_name=system_name,
            component_states=component_states,
            performance_metrics={
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage
            },
            active_events=[],  # 将由事件总线提供
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )

        self.snapshots.append(snapshot)
        return snapshot

    def _capture_event_bus_state(self) -> Dict[str, Any]:
        """捕获事件总线状态"""
        try:
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                return {
                    "script_id": event_bus.id,
                    "is_running": event_bus.is_running,
                    "interval": getattr(event_bus, 'interval', 0),
                    "queue_size": getattr(event_bus, 'get_queue_size', lambda: 0)()
                }
            return {"status": "not_found"}
        except Exception as e:
            return {"error": str(e)}

    def _capture_ai_directors_state(self) -> Dict[str, Any]:
        """捕获AI导演系统状态"""
        try:
            directors = {}
            for director_name in ["tiandao_director_script", "diling_director_script", "qiling_director_script"]:
                scripts = search_script(director_name)
                if scripts:
                    director = scripts[0]
                    directors[director_name] = {
                        "script_id": director.id,
                        "is_running": director.is_running,
                        "interval": getattr(director, 'interval', 0)
                    }
                else:
                    directors[director_name] = {"status": "not_found"}
            return directors
        except Exception as e:
            return {"error": str(e)}

    def _capture_tag_property_state(self) -> Dict[str, Any]:
        """捕获TagProperty系统状态"""
        try:
            # 测试查询功能
            test_query_start = time.time()
            result = TagPropertyQueryManager.find_characters_by_realm("练气")
            query_time = time.time() - test_query_start

            return {
                "query_response_time": query_time * 1000,  # 毫秒
                "test_query_result_count": len(result) if result else 0,
                "status": "operational"
            }
        except Exception as e:
            return {"error": str(e)}

    def _capture_world_system_state(self) -> Dict[str, Any]:
        """捕获世界系统状态"""
        try:
            # 检查世界系统组件
            from ..systems.world_location_manager import world_location_manager
            from ..systems.world_query_manager import world_query_manager
            from ..systems.world_events import world_event_system

            return {
                "location_manager": "loaded" if world_location_manager else "not_loaded",
                "query_manager": "loaded" if world_query_manager else "not_loaded",
                "event_system": "loaded" if world_event_system else "not_loaded",
                "status": "operational"
            }
        except Exception as e:
            return {"error": str(e)}

    def _capture_npc_system_state(self) -> Dict[str, Any]:
        """捕获NPC系统状态"""
        try:
            # 查找NPC对象
            npcs = search_object(typeclass="typeclasses.npcs.XianxiaNPC")
            return {
                "total_npcs": len(npcs) if npcs else 0,
                "status": "operational"
            }
        except Exception as e:
            return {"error": str(e)}

    def _capture_combat_system_state(self) -> Dict[str, Any]:
        """捕获战斗系统状态"""
        try:
            # 检查战斗系统组件
            return {
                "status": "operational",
                "combat_handler": "loaded"
            }
        except Exception as e:
            return {"error": str(e)}

    def _capture_social_system_state(self) -> Dict[str, Any]:
        """捕获社交系统状态"""
        try:
            # 检查社交系统组件
            return {
                "status": "operational",
                "relationship_manager": "loaded"
            }
        except Exception as e:
            return {"error": str(e)}

    def _capture_novel_system_state(self) -> Dict[str, Any]:
        """捕获小说生成系统状态"""
        try:
            # 查找小说生成脚本
            novel_scripts = search_script("novel_generator_script")
            if novel_scripts:
                script = novel_scripts[0]
                return {
                    "script_id": script.id,
                    "is_running": script.is_running,
                    "status": "operational"
                }
            return {"status": "not_found"}
        except Exception as e:
            return {"error": str(e)}

    def compare_snapshots(self, snapshot1: SystemSnapshot, snapshot2: SystemSnapshot) -> Dict[str, Any]:
        """比较两个系统快照"""
        comparison = {
            "time_diff": snapshot2.timestamp - snapshot1.timestamp,
            "performance_changes": {},
            "component_changes": {},
            "issues_detected": []
        }

        # 比较性能指标
        for metric in ["cpu_usage", "memory_usage"]:
            if metric in snapshot1.performance_metrics and metric in snapshot2.performance_metrics:
                change = snapshot2.performance_metrics[metric] - snapshot1.performance_metrics[metric]
                comparison["performance_changes"][metric] = {
                    "before": snapshot1.performance_metrics[metric],
                    "after": snapshot2.performance_metrics[metric],
                    "change": change
                }

                # 检测异常变化
                if metric == "memory_usage" and change > 10:  # 内存使用增加超过10%
                    comparison["issues_detected"].append(f"Memory usage increased significantly: +{change:.2f}%")
                elif metric == "cpu_usage" and change > 20:  # CPU使用增加超过20%
                    comparison["issues_detected"].append(f"CPU usage increased significantly: +{change:.2f}%")

        return comparison


class TestReportGenerator:
    """测试报告生成器"""

    def __init__(self):
        self.test_results = []
        self.performance_data = []
        self.error_logs = []

    def add_test_result(self, test_name: str, success: bool, duration: float,
                       details: Dict[str, Any] = None, error_message: str = None):
        """添加测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "duration": duration,
            "timestamp": time.time(),
            "details": details or {},
            "error_message": error_message
        }
        self.test_results.append(result)

    def add_performance_data(self, metrics: PerformanceMetrics):
        """添加性能数据"""
        self.performance_data.append(asdict(metrics))

    def add_error_log(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """添加错误日志"""
        error_log = {
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": time.time(),
            "context": context or {}
        }
        self.error_logs.append(error_log)

    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        if not self.test_results:
            return {"error": "No test results available"}

        successful_tests = [t for t in self.test_results if t["success"]]
        failed_tests = [t for t in self.test_results if not t["success"]]

        total_duration = sum(t["duration"] for t in self.test_results)
        avg_duration = total_duration / len(self.test_results)

        report = {
            "summary": {
                "total_tests": len(self.test_results),
                "successful_tests": len(successful_tests),
                "failed_tests": len(failed_tests),
                "success_rate": len(successful_tests) / len(self.test_results) * 100,
                "total_duration": total_duration,
                "average_duration": avg_duration
            },
            "test_results": self.test_results,
            "performance_summary": self._generate_performance_summary(),
            "error_summary": self._generate_error_summary(),
            "recommendations": self._generate_recommendations()
        }

        return report

    def _generate_performance_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        if not self.performance_data:
            return {"no_data": True}

        cpu_values = [p["cpu_usage"] for p in self.performance_data]
        memory_values = [p["memory_usage"] for p in self.performance_data]
        query_times = [p["query_response_time"] for p in self.performance_data]

        return {
            "cpu_usage": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory_usage": {
                "avg": sum(memory_values) / len(memory_values),
                "max": max(memory_values),
                "min": min(memory_values)
            },
            "query_response_time": {
                "avg": sum(query_times) / len(query_times),
                "max": max(query_times),
                "min": min(query_times)
            }
        }

    def _generate_error_summary(self) -> Dict[str, Any]:
        """生成错误摘要"""
        if not self.error_logs:
            return {"no_errors": True}

        error_types = defaultdict(int)
        for error in self.error_logs:
            error_types[error["error_type"]] += 1

        return {
            "total_errors": len(self.error_logs),
            "error_types": dict(error_types),
            "recent_errors": self.error_logs[-5:]  # 最近5个错误
        }

    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 基于测试结果生成建议
        if self.test_results:
            success_rate = len([t for t in self.test_results if t["success"]]) / len(self.test_results) * 100
            if success_rate < 90:
                recommendations.append("测试成功率较低，建议检查系统集成问题")

        # 基于性能数据生成建议
        if self.performance_data:
            avg_cpu = sum(p["cpu_usage"] for p in self.performance_data) / len(self.performance_data)
            avg_memory = sum(p["memory_usage"] for p in self.performance_data) / len(self.performance_data)
            avg_query_time = sum(p["query_response_time"] for p in self.performance_data) / len(self.performance_data)

            if avg_cpu > 80:
                recommendations.append("CPU使用率过高，建议优化计算密集型操作")
            if avg_memory > 80:
                recommendations.append("内存使用率过高，建议检查内存泄漏")
            if avg_query_time > 100:
                recommendations.append("查询响应时间过长，建议优化数据库查询")

        # 基于错误日志生成建议
        if self.error_logs:
            error_types = defaultdict(int)
            for error in self.error_logs:
                error_types[error["error_type"]] += 1

            most_common_error = max(error_types.items(), key=lambda x: x[1])
            recommendations.append(f"最常见错误类型：{most_common_error[0]}，建议重点关注")

        return recommendations

    def save_report_to_file(self, filename: str = None):
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"integration_test_report_{timestamp}.json"

        report = self.generate_report()

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            log_info(f"测试报告已保存到: {filename}")
            return filename
        except Exception as e:
            log_err(f"保存测试报告失败: {e}")
            return None


class XianxiaIntegrationTest(BaseEvenniaTest):
    """仙侠MUD集成测试基类"""

    def setUp(self):
        """测试前准备"""
        super().setUp()

        # 初始化测试框架组件
        self.event_tracker = EventChainTracker()
        self.performance_monitor = PerformanceMonitor()
        self.state_manager = SystemStateManager()
        self.report_generator = TestReportGenerator()

        # 启动性能监控
        self.performance_monitor.start_monitoring()

        # 捕获初始系统状态
        self.initial_snapshot = self.state_manager.capture_system_snapshot()

        # 测试开始时间
        self.test_start_time = time.time()

        log_info("集成测试环境初始化完成")

    def tearDown(self):
        """测试后清理"""
        try:
            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            # 捕获最终系统状态
            final_snapshot = self.state_manager.capture_system_snapshot()

            # 比较系统状态变化
            state_comparison = self.state_manager.compare_snapshots(
                self.initial_snapshot, final_snapshot
            )

            # 生成测试报告
            test_duration = time.time() - self.test_start_time
            self.report_generator.add_test_result(
                test_name=self.__class__.__name__,
                success=True,  # 子类可以覆盖
                duration=test_duration,
                details={
                    "state_comparison": state_comparison,
                    "event_statistics": self.event_tracker.get_trace_statistics(),
                    "performance_summary": self.performance_monitor.get_metrics_summary()
                }
            )

            log_info("集成测试环境清理完成")

        except Exception as e:
            log_err(f"测试清理过程中发生错误: {e}")

        super().tearDown()

    def assert_event_chain_success(self, trace_id: str, timeout: float = 5.0):
        """断言事件链处理成功"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 检查事件是否完成处理
            with self.event_tracker.lock:
                completed_trace = None
                for trace in self.event_tracker.completed_traces:
                    if trace.event_id in trace_id:
                        completed_trace = trace
                        break

                if completed_trace:
                    self.assertTrue(completed_trace.success,
                                  f"事件链处理失败: {completed_trace.error_message}")
                    self.assertLess(completed_trace.total_latency, 1.0,
                                  "事件处理延迟过高")
                    return

            time.sleep(0.1)

        self.fail(f"事件链处理超时: {trace_id}")

    def assert_performance_within_limits(self, cpu_limit: float = 80.0,
                                       memory_limit: float = 80.0,
                                       query_time_limit: float = 100.0):
        """断言性能在限制范围内"""
        current_metrics = self.performance_monitor.get_current_metrics()
        if current_metrics:
            self.assertLess(current_metrics.cpu_usage, cpu_limit,
                          f"CPU使用率超限: {current_metrics.cpu_usage}% > {cpu_limit}%")
            self.assertLess(current_metrics.memory_usage, memory_limit,
                          f"内存使用率超限: {current_metrics.memory_usage}% > {memory_limit}%")
            self.assertLess(current_metrics.query_response_time, query_time_limit,
                          f"查询响应时间超限: {current_metrics.query_response_time}ms > {query_time_limit}ms")

    def assert_system_state_consistent(self, system_name: str = "all"):
        """断言系统状态一致性"""
        snapshot = self.state_manager.capture_system_snapshot(system_name)

        # 检查各系统组件状态
        for component, state in snapshot.component_states.items():
            if isinstance(state, dict) and "error" in state:
                self.fail(f"系统组件 {component} 状态异常: {state['error']}")

    def simulate_concurrent_load(self, operation_func: Callable,
                               concurrent_count: int = 10,
                               duration: float = 5.0):
        """模拟并发负载"""
        import concurrent.futures

        def run_operation():
            end_time = time.time() + duration
            operations_count = 0
            while time.time() < end_time:
                try:
                    operation_func()
                    operations_count += 1
                    time.sleep(0.1)  # 避免过度负载
                except Exception as e:
                    log_err(f"并发操作失败: {e}")
            return operations_count

        # 启动并发操作
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [executor.submit(run_operation) for _ in range(concurrent_count)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        total_operations = sum(results)
        log_info(f"并发负载测试完成: {concurrent_count}个线程，总操作数: {total_operations}")

        return total_operations

    def wait_for_system_stabilization(self, timeout: float = 10.0):
        """等待系统稳定"""
        start_time = time.time()
        stable_count = 0
        required_stable_count = 5  # 需要连续5次检查都稳定

        while time.time() - start_time < timeout:
            current_metrics = self.performance_monitor.get_current_metrics()
            if current_metrics:
                # 检查系统是否稳定（CPU和内存使用率变化不大）
                if (current_metrics.cpu_usage < 50 and
                    current_metrics.memory_usage < 70 and
                    current_metrics.event_queue_size < 10):
                    stable_count += 1
                    if stable_count >= required_stable_count:
                        log_info("系统已稳定")
                        return True
                else:
                    stable_count = 0

            time.sleep(1.0)

        log_err("等待系统稳定超时")
        return False


# 工具函数

def create_test_character(name: str = "test_char", location=None):
    """创建测试角色"""
    from ..typeclasses.characters import XianxiaCharacter
    return create_object(XianxiaCharacter, key=name, location=location)


def create_test_room(name: str = "test_room"):
    """创建测试房间"""
    from ..typeclasses.rooms import XianxiaRoom
    return create_object(XianxiaRoom, key=name)


def create_test_npc(name: str = "test_npc", location=None):
    """创建测试NPC"""
    from ..typeclasses.npcs import XianxiaNPC
    return create_object(XianxiaNPC, key=name, location=location)


def cleanup_test_objects():
    """清理测试对象"""
    # 清理测试角色
    test_chars = search_object("test_char*")
    for char in test_chars:
        char.delete()

    # 清理测试房间
    test_rooms = search_object("test_room*")
    for room in test_rooms:
        room.delete()

    # 清理测试NPC
    test_npcs = search_object("test_npc*")
    for npc in test_npcs:
        npc.delete()

    # 清理测试脚本
    test_scripts = search_script("test_*")
    for script in test_scripts:
        script.delete()


def measure_operation_performance(operation_func: Callable, iterations: int = 100) -> Dict[str, float]:
    """测量操作性能"""
    times = []

    for _ in range(iterations):
        start_time = time.time()
        try:
            operation_func()
            end_time = time.time()
            times.append(end_time - start_time)
        except Exception as e:
            log_err(f"性能测试操作失败: {e}")

    if not times:
        return {"error": "所有操作都失败"}

    times.sort()
    return {
        "iterations": len(times),
        "avg_time": sum(times) / len(times),
        "min_time": min(times),
        "max_time": max(times),
        "median_time": times[len(times) // 2],
        "p95_time": times[int(len(times) * 0.95)],
        "p99_time": times[int(len(times) * 0.99)]
    }