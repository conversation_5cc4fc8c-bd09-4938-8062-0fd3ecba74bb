"""
智能NPC系统测试套件

测试智能NPC系统的各个组件：
- IntelligentNPC类功能测试
- NPCFactory工厂模式测试
- NPCPersonalityEngine个性引擎测试
- NPCContextManager上下文管理测试
- NPCDirectorIntegration集成系统测试
- NPC管理命令测试
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from evennia.utils.test_resources import EvenniaTest
from evennia.utils import create


class TestIntelligentNPCSystem(EvenniaTest):
    """智能NPC系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        
        # 创建测试位置
        self.test_location = create.create_object(
            "typeclasses.rooms.Room",
            key="测试房间"
        )
        
        # 创建测试玩家
        self.test_player = create.create_object(
            "typeclasses.characters.Character",
            key="测试玩家"
        )
        self.test_player.location = self.test_location
        
        # 设置玩家属性
        setattr(self.test_player, "修为境界", "练气")
        setattr(self.test_player, "门派归属", "青云门")
        setattr(self.test_player, "五行属性", "金")
    
    def test_npc_factory_creation(self):
        """测试NPC工厂创建功能"""
        try:
            from typeclasses.npcs import NPCFactory
            
            # 测试创建长老NPC
            elder_npc = NPCFactory.create_npc(
                npc_type="elder",
                name="测试长老",
                location=self.test_location,
                门派归属="青云门",
                修为境界="元婴"
            )
            
            self.assertIsNotNone(elder_npc)
            self.assertEqual(elder_npc.key, "测试长老")
            self.assertEqual(getattr(elder_npc, "门派归属"), "青云门")
            self.assertEqual(getattr(elder_npc, "修为境界"), "元婴")
            self.assertEqual(getattr(elder_npc, "角色类型"), "长老")
            
            # 测试创建师兄NPC
            brother_npc = NPCFactory.create_npc(
                npc_type="senior_brother",
                name="测试师兄",
                location=self.test_location
            )
            
            self.assertIsNotNone(brother_npc)
            self.assertEqual(getattr(brother_npc, "角色类型"), "师兄")
            
            print("✓ NPC工厂创建测试通过")
            
        except ImportError:
            self.skipTest("NPC系统模块未找到")
    
    def test_npc_personality_engine(self):
        """测试NPC个性引擎"""
        try:
            from systems.npc_personality_engine import NPCPersonalityEngine
            
            engine = NPCPersonalityEngine()
            
            # 测试个性档案生成
            npc_attributes = {
                "修为境界": "金丹",
                "门派归属": "青云门",
                "五行属性": "火",
                "角色类型": "师兄"
            }
            
            profile = engine.generate_personality_profile(npc_attributes)
            
            self.assertIn("basic_info", profile)
            self.assertIn("dialogue_style", profile)
            self.assertIn("personality_traits", profile)
            self.assertIn("knowledge_areas", profile)
            
            # 验证基本信息
            basic_info = profile["basic_info"]
            self.assertEqual(basic_info["realm"], "金丹")
            self.assertEqual(basic_info["sect"], "青云门")
            self.assertEqual(basic_info["element"], "火")
            self.assertEqual(basic_info["role"], "师兄")
            
            # 测试动态提示生成
            context = {
                "npc_name": "测试师兄",
                "current_situation": "正在与弟子对话"
            }
            
            prompt = engine.generate_dynamic_prompt(profile, context)
            self.assertIn("测试师兄", prompt)
            self.assertIn("金丹", prompt)
            self.assertIn("青云门", prompt)
            
            print("✓ NPC个性引擎测试通过")
            
        except ImportError:
            self.skipTest("个性引擎模块未找到")
    
    def test_npc_context_manager(self):
        """测试NPC上下文管理器"""
        try:
            from systems.npc_context_manager import NPCContextManager
            from typeclasses.npcs import NPCFactory
            
            manager = NPCContextManager()
            
            # 创建测试NPC
            npc = NPCFactory.create_npc(
                npc_type="elder",
                name="测试长老",
                location=self.test_location
            )
            
            # 测试上下文构建
            context = manager.build_conversation_context(
                npc=npc,
                player=self.test_player,
                message="请问如何修炼？"
            )
            
            self.assertIn("npc_info", context)
            self.assertIn("player_info", context)
            self.assertIn("conversation_history", context)
            self.assertIn("current_message", context)
            self.assertIn("world_state", context)
            
            # 验证NPC信息
            npc_info = context["npc_info"]
            self.assertEqual(npc_info["name"], "测试长老")
            
            # 验证玩家信息
            player_info = context["player_info"]
            self.assertEqual(player_info["name"], "测试玩家")
            
            # 验证消息
            self.assertEqual(context["current_message"], "请问如何修炼？")
            
            print("✓ NPC上下文管理器测试通过")
            
        except ImportError:
            self.skipTest("上下文管理器模块未找到")
    
    def test_npc_director_integration(self):
        """测试NPC与AI导演集成"""
        try:
            from systems.npc_integration_system import NPCDirectorIntegration
            from typeclasses.npcs import NPCFactory
            
            integration = NPCDirectorIntegration()
            
            # 创建测试NPC
            npc = NPCFactory.create_npc(
                npc_type="elder",
                name="测试长老",
                location=self.test_location,
                修为境界="化神"
            )
            
            # 测试NPC注册
            integration.register_npc_with_directors(npc)
            
            # 验证标签设置
            self.assertTrue(npc.tags.get("qiling_managed", category="ai_director"))
            
            # 测试修炼指导
            guidance = integration.provide_cultivation_guidance(npc, self.test_player)
            if guidance:  # 如果NPC有资格提供指导
                self.assertIsInstance(guidance, str)
                self.assertGreater(len(guidance), 0)
            
            # 测试集成状态
            status = integration.get_integration_status(npc)
            self.assertIn("registered_directors", status)
            self.assertIn("guidance_count", status)
            
            print("✓ NPC导演集成测试通过")
            
        except ImportError:
            self.skipTest("导演集成模块未找到")
    
    def test_npc_conversation_system(self):
        """测试NPC对话系统"""
        try:
            from typeclasses.npcs import IntelligentNPC, NPCFactory
            
            # 创建测试NPC
            npc = NPCFactory.create_npc(
                npc_type="senior_brother",
                name="测试师兄",
                location=self.test_location
            )
            
            # 模拟对话
            with patch.object(npc, '_call_llm_with_fallback') as mock_llm:
                mock_llm.return_value = "师弟，修炼要循序渐进，不可急于求成。"
                
                # 测试对话处理
                response = npc.handle_conversation(self.test_player, "请问如何修炼？")
                
                self.assertIsInstance(response, str)
                self.assertGreater(len(response), 0)
                
                # 验证对话历史记录
                conversation_history = npc.db.npc_state.get("conversation_history", {})
                self.assertIn(self.test_player.key, conversation_history)
                
                player_history = conversation_history[self.test_player.key]
                self.assertGreater(len(player_history), 0)
                
                # 验证最新对话记录
                latest_conversation = player_history[-1]
                self.assertEqual(latest_conversation["message"], "请问如何修炼？")
                self.assertEqual(latest_conversation["response"], response)
            
            print("✓ NPC对话系统测试通过")
            
        except ImportError:
            self.skipTest("NPC对话系统模块未找到")
    
    def test_sect_npc_creation(self):
        """测试门派NPC套装创建"""
        try:
            from typeclasses.npcs import NPCFactory
            
            # 测试创建门派NPC套装
            npcs = NPCFactory.create_sect_npcs("青云门", self.test_location)
            
            self.assertGreater(len(npcs), 0)
            
            # 验证NPC类型多样性
            npc_roles = [getattr(npc, "角色类型") for npc in npcs]
            expected_roles = ["长老", "师兄", "师姐", "师弟", "师妹"]
            
            for role in expected_roles:
                self.assertIn(role, npc_roles)
            
            # 验证所有NPC都属于同一门派
            for npc in npcs:
                self.assertEqual(getattr(npc, "门派归属"), "青云门")
            
            print("✓ 门派NPC套装创建测试通过")
            
        except ImportError:
            self.skipTest("NPC工厂模块未找到")
    
    def test_npc_performance(self):
        """测试NPC系统性能"""
        try:
            from typeclasses.npcs import NPCFactory
            from systems.npc_personality_engine import NPCPersonalityEngine
            
            # 性能测试：创建多个NPC
            start_time = time.time()
            
            npcs = []
            for i in range(10):
                npc = NPCFactory.create_npc(
                    npc_type="disciple",
                    name=f"测试弟子{i}",
                    location=self.test_location
                )
                npcs.append(npc)
            
            creation_time = time.time() - start_time
            self.assertLess(creation_time, 5.0)  # 创建10个NPC应该在5秒内完成
            
            # 性能测试：个性生成
            engine = NPCPersonalityEngine()
            start_time = time.time()
            
            for i in range(50):
                profile = engine.generate_personality_profile({
                    "修为境界": "练气",
                    "门派归属": "青云门",
                    "五行属性": "土",
                    "角色类型": "普通弟子"
                })
            
            personality_time = time.time() - start_time
            self.assertLess(personality_time, 2.0)  # 生成50个个性档案应该在2秒内完成
            
            print(f"✓ NPC系统性能测试通过 (创建时间: {creation_time:.2f}s, 个性生成时间: {personality_time:.2f}s)")
            
        except ImportError:
            self.skipTest("NPC性能测试模块未找到")
    
    def test_npc_error_handling(self):
        """测试NPC系统错误处理"""
        try:
            from typeclasses.npcs import NPCFactory
            
            # 测试无效NPC类型
            with self.assertRaises(ValueError):
                NPCFactory.create_npc(
                    npc_type="invalid_type",
                    name="测试NPC",
                    location=self.test_location
                )
            
            # 测试缺少必要参数
            with self.assertRaises(TypeError):
                NPCFactory.create_npc()
            
            print("✓ NPC系统错误处理测试通过")
            
        except ImportError:
            self.skipTest("NPC错误处理测试模块未找到")
    
    def tearDown(self):
        """测试后清理"""
        super().tearDown()


class TestNPCManagementCommands(EvenniaTest):
    """NPC管理命令测试类"""
    
    def setUp(self):
        """测试前准备"""
        super().setUp()
        
        # 创建测试角色
        self.char1.permissions.add("Builder")
        
        # 创建测试位置
        self.test_location = create.create_object(
            "typeclasses.rooms.Room",
            key="测试房间"
        )
        self.char1.location = self.test_location
    
    def test_create_npc_command(self):
        """测试创建NPC命令"""
        try:
            from commands.npc_management_commands import CmdCreateNPC
            
            cmd = CmdCreateNPC()
            cmd.caller = self.char1
            cmd.args = "elder 测试长老 门派归属=青云门 修为境界=元婴"
            
            # 模拟命令执行
            with patch('typeclasses.npcs.NPCFactory.create_npc') as mock_create:
                mock_npc = Mock()
                mock_npc.key = "测试长老"
                mock_npc.location = self.test_location
                mock_create.return_value = mock_npc
                
                cmd.func()
                
                # 验证工厂方法被调用
                mock_create.assert_called_once()
            
            print("✓ 创建NPC命令测试通过")
            
        except ImportError:
            self.skipTest("NPC管理命令模块未找到")
    
    def test_npc_status_command(self):
        """测试NPC状态命令"""
        try:
            from commands.npc_management_commands import CmdNPCStatus
            
            cmd = CmdNPCStatus()
            cmd.caller = self.char1
            cmd.args = ""
            
            # 模拟命令执行
            cmd.func()
            
            print("✓ NPC状态命令测试通过")
            
        except ImportError:
            self.skipTest("NPC状态命令模块未找到")


def run_npc_system_tests():
    """运行NPC系统测试套件"""
    print("开始运行智能NPC系统测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(unittest.makeSuite(TestIntelligentNPCSystem))
    suite.addTest(unittest.makeSuite(TestNPCManagementCommands))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n🎉 所有NPC系统测试通过！")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        for test, traceback in result.failures:
            print(f"\n失败: {test}")
            print(traceback)
        
        for test, traceback in result.errors:
            print(f"\n错误: {test}")
            print(traceback)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_npc_system_tests()
