"""
Room

Rooms are simple containers that has no location of their own.

"""

from evennia.objects.objects import DefaultRoom
from evennia import TagProperty, TagCategoryProperty
from evennia.utils.logger import log_info

from .objects import ObjectParent
from ..systems.tag_property_system import XianxiaTagProperty, TagPropertyQueryManager


class Room(ObjectParent, DefaultRoom):
    """
    Rooms are like any Object, except their location is None
    (which is default). They also use basetype_setup() to
    add locks so they cannot be puppeted or picked up.
    (to change that, use at_object_creation instead)

    See mygame/typeclasses/objects.py for a list of
    properties and methods available on all Objects.
    """

    pass


class XianxiaRoom(Room):
    """
    仙侠世界房间类

    使用TagProperty实现高性能的语义化属性查询：
    - 灵气浓度：影响修炼效果
    - 危险等级：影响随机事件
    - 地点类型：决定可用功能
    - 门派归属：影响访问权限
    - 特殊属性：五行属性、天象等
    """

    # 核心地理属性
    灵气浓度 = XianxiaTagProperty(
        category="spiritual_energy",
        default="一般",
        xianxia_type="environment",
        valid_values=TagPropertyQueryManager.SPIRITUAL_ENERGY_LEVELS
    )

    危险等级 = XianxiaTagProperty(
        category="danger_level",
        default="安全",
        xianxia_type="environment",
        valid_values=TagPropertyQueryManager.DANGER_LEVELS
    )

    地点类型 = XianxiaTagProperty(
        category="location_type",
        default="野外",
        xianxia_type="geography",
        valid_values=TagPropertyQueryManager.LOCATION_TYPES
    )

    门派归属 = XianxiaTagProperty(
        category="sect_territory",
        default="无门派",
        xianxia_type="politics",
        valid_values=TagPropertyQueryManager.SECT_TERRITORIES
    )

    # 五行属性
    五行属性 = XianxiaTagProperty(
        category="elemental_type",
        default="土",
        xianxia_type="elemental",
        valid_values=TagPropertyQueryManager.ELEMENTAL_TYPES
    )

    # 特殊标记
    特殊标记 = TagCategoryProperty("秘境", "洞府", "传送点", "安全区", "PK区")

    def at_object_creation(self):
        """房间创建时的初始化"""
        super().at_object_creation()

        # 设置默认属性
        if not self.tags.get(category="spiritual_energy"):
            self.灵气浓度 = "一般"

        if not self.tags.get(category="danger_level"):
            self.危险等级 = "安全"

        if not self.tags.get(category="location_type"):
            self.地点类型 = "野外"

        if not self.tags.get(category="sect_territory"):
            self.门派归属 = "无门派"

        if not self.tags.get(category="elemental_type"):
            self.五行属性 = "土"

        log_info(f"XianxiaRoom created: {self.key} with spiritual energy: {self.灵气浓度}")

    def get_spiritual_bonus(self) -> float:
        """
        获取灵气修炼加成

        Returns:
            float: 修炼效果倍数
        """
        bonus_map = {
            "稀薄": 0.5,
            "一般": 1.0,
            "浓郁": 1.5,
            "极浓": 2.0,
            "仙境": 3.0
        }
        return bonus_map.get(self.灵气浓度, 1.0)

    def get_danger_modifier(self) -> float:
        """
        获取危险系数

        Returns:
            float: 危险事件概率倍数
        """
        danger_map = {
            "安全": 0.1,
            "低危": 0.3,
            "中危": 0.6,
            "高危": 1.0,
            "极危": 2.0
        }
        return danger_map.get(self.危险等级, 0.6)

    def is_sect_territory(self, sect_name: str) -> bool:
        """
        检查是否为指定门派领地

        Args:
            sect_name: 门派名称

        Returns:
            bool: 是否为该门派领地
        """
        return self.门派归属 == sect_name

    def can_cultivate(self) -> bool:
        """
        检查是否可以修炼

        Returns:
            bool: 是否允许修炼
        """
        # 极危地区不允许修炼
        if self.危险等级 == "极危":
            return False

        # 城镇中某些地方不允许修炼
        if self.地点类型 == "城镇" and "修炼禁地" in self.特殊标记:
            return False

        return True

    def get_available_functions(self) -> List[str]:
        """
        获取当前地点可用功能

        Returns:
            List[str]: 可用功能列表
        """
        functions = ["查看", "移动"]

        # 根据地点类型添加功能
        if self.地点类型 == "城镇":
            functions.extend(["交易", "休息", "打听消息"])
        elif self.地点类型 == "洞府":
            functions.extend(["修炼", "炼丹", "炼器"])
        elif self.地点类型 == "秘境":
            functions.extend(["探索", "寻宝"])
        elif self.地点类型 == "仙山":
            functions.extend(["修炼", "采药", "悟道"])

        # 根据特殊标记添加功能
        if "传送点" in self.特殊标记:
            functions.append("传送")
        if "安全区" in self.特殊标记:
            functions.append("安全休息")

        # 根据灵气浓度决定修炼效果
        if self.can_cultivate():
            if "修炼" not in functions:
                functions.append("修炼")

        return functions

    def get_room_info(self) -> Dict[str, Any]:
        """
        获取房间详细信息

        Returns:
            Dict: 房间信息字典
        """
        return {
            "name": self.key,
            "description": self.db.desc or "这里没有特别的描述。",
            "spiritual_energy": self.灵气浓度,
            "danger_level": self.危险等级,
            "location_type": self.地点类型,
            "sect_territory": self.门派归属,
            "elemental_type": self.五行属性,
            "special_marks": list(self.特殊标记),
            "spiritual_bonus": self.get_spiritual_bonus(),
            "danger_modifier": self.get_danger_modifier(),
            "available_functions": self.get_available_functions(),
            "can_cultivate": self.can_cultivate()
        }
