"""
全系统端到端测试

测试完整游戏流程：
- 完整游戏会话模拟
- 多玩家并发交互测试
- 长时间运行稳定性测试
- 系统恢复能力测试
- 数据一致性验证
"""

import time
import threading
import unittest
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import (
    XianxiaIntegrationTest, create_test_character, create_test_room, 
    create_test_npc, cleanup_test_objects, measure_operation_performance
)
from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..systems.tag_property_system import TagPropertyQueryManager
from ..typeclasses.characters import XianxiaCharacter
from ..typeclasses.rooms import XianxiaRoom
from ..typeclasses.npcs import XianxiaNPC


class CompleteGameSessionTest(XianxiaIntegrationTest):
    """完整游戏会话模拟测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建完整的游戏世界
        self.game_world = self._create_game_world()
        
        # 初始化所有系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(3)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def _create_game_world(self):
        """创建测试游戏世界"""
        world = {
            "rooms": {},
            "characters": {},
            "npcs": {}
        }
        
        # 创建多个房间
        room_configs = [
            ("sect_entrance", "宗门入口", {"location_type": "entrance", "spiritual_level": "medium"}),
            ("training_ground", "练功场", {"location_type": "training", "spiritual_level": "high"}),
            ("library", "藏书阁", {"location_type": "library", "spiritual_level": "medium"}),
            ("alchemy_room", "炼丹房", {"location_type": "alchemy", "spiritual_level": "high"}),
            ("market", "坊市", {"location_type": "market", "spiritual_level": "low"})
        ]
        
        for room_key, room_name, attributes in room_configs:
            room = create_test_room(room_key)
            room.db.desc = room_name
            for attr_key, attr_value in attributes.items():
                room.tags.add(attr_value, category=attr_key)
            world["rooms"][room_key] = room
        
        # 创建测试角色
        char_configs = [
            ("player1", "测试玩家一", "sect_entrance", {"realm": "练气三层", "sect": "青云宗"}),
            ("player2", "测试玩家二", "training_ground", {"realm": "练气五层", "sect": "青云宗"}),
            ("player3", "测试玩家三", "market", {"realm": "筑基一层", "sect": "天剑门"})
        ]
        
        for char_key, char_name, location_key, attributes in char_configs:
            location = world["rooms"][location_key]
            char = create_test_character(char_key, location)
            char.db.desc = char_name
            for attr_key, attr_value in attributes.items():
                char.tags.add(attr_value, category=attr_key)
            world["characters"][char_key] = char
        
        # 创建NPC
        npc_configs = [
            ("sect_elder", "宗门长老", "sect_entrance", {"role": "elder", "realm": "金丹期"}),
            ("training_master", "练功师父", "training_ground", {"role": "instructor", "realm": "筑基后期"}),
            ("librarian", "藏书管理员", "library", {"role": "librarian", "realm": "筑基中期"}),
            ("alchemist", "炼丹师", "alchemy_room", {"role": "alchemist", "realm": "筑基后期"}),
            ("merchant", "商人", "market", {"role": "merchant", "realm": "练气九层"})
        ]
        
        for npc_key, npc_name, location_key, attributes in npc_configs:
            location = world["rooms"][location_key]
            npc = create_test_npc(npc_key, location)
            npc.db.desc = npc_name
            for attr_key, attr_value in attributes.items():
                npc.tags.add(attr_value, category=attr_key)
            world["npcs"][npc_key] = npc
        
        return world
    
    def test_complete_player_journey(self):
        """测试完整的玩家游戏历程"""
        log_info("开始测试完整玩家游戏历程")
        
        player = self.game_world["characters"]["player1"]
        
        # 1. 玩家进入游戏世界
        self._simulate_player_login(player)
        
        # 2. 探索世界
        self._simulate_world_exploration(player)
        
        # 3. 与NPC互动
        self._simulate_npc_interactions(player)
        
        # 4. 修炼提升
        self._simulate_cultivation_progress(player)
        
        # 5. 社交互动
        self._simulate_social_interactions(player)
        
        # 6. 战斗体验
        self._simulate_combat_experience(player)
        
        # 7. 验证整个历程的系统响应
        self._verify_journey_system_responses(player)
        
        log_info("完整玩家游戏历程测试完成")
    
    def _simulate_player_login(self, player):
        """模拟玩家登录"""
        login_event = BaseEvent(
            event_type="player_login",
            event_data={
                "character_id": player.id,
                "character_name": player.key,
                "login_time": time.time(),
                "location": player.location.id if player.location else None
            },
            priority="MEDIUM"
        )
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(login_event)
            time.sleep(1)
    
    def _simulate_world_exploration(self, player):
        """模拟世界探索"""
        # 玩家移动到不同房间
        exploration_path = ["training_ground", "library", "alchemy_room", "market"]
        
        for room_key in exploration_path:
            target_room = self.game_world["rooms"][room_key]
            
            # 移动事件
            move_event = BaseEvent(
                event_type="character_movement",
                event_data={
                    "character_id": player.id,
                    "from_location": player.location.id if player.location else None,
                    "to_location": target_room.id,
                    "movement_type": "walk"
                },
                priority="LOW"
            )
            
            # 更新玩家位置
            player.location = target_room
            
            # 发布移动事件
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(move_event)
                time.sleep(0.5)
    
    def _simulate_npc_interactions(self, player):
        """模拟NPC互动"""
        # 与不同NPC对话
        npc_interactions = [
            ("sect_elder", "greeting", "向长老问好"),
            ("training_master", "ask_training", "询问修炼方法"),
            ("librarian", "browse_books", "浏览功法书籍"),
            ("merchant", "check_goods", "查看商品")
        ]
        
        for npc_key, interaction_type, description in npc_interactions:
            npc = self.game_world["npcs"][npc_key]
            
            interaction_event = BaseEvent(
                event_type="npc_interaction",
                event_data={
                    "character_id": player.id,
                    "npc_id": npc.id,
                    "interaction_type": interaction_type,
                    "description": description,
                    "location": npc.location.id
                },
                priority="MEDIUM"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(interaction_event)
                time.sleep(1)
    
    def _simulate_cultivation_progress(self, player):
        """模拟修炼进度"""
        # 模拟修炼活动
        cultivation_event = BaseEvent(
            event_type="cultivation_session",
            event_data={
                "character_id": player.id,
                "cultivation_type": "qi_gathering",
                "duration": 3600,  # 1小时
                "location": self.game_world["rooms"]["training_ground"].id,
                "progress_gained": 50
            },
            priority="MEDIUM"
        )
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(cultivation_event)
            time.sleep(2)
            
            # 如果修炼进度足够，触发突破
            breakthrough_event = BaseEvent(
                event_type="cultivation_breakthrough",
                event_data={
                    "character_id": player.id,
                    "old_realm": "练气三层",
                    "new_realm": "练气四层",
                    "location": self.game_world["rooms"]["training_ground"].id
                },
                priority="HIGH"
            )
            
            event_bus.publish_event(breakthrough_event)
            time.sleep(2)
    
    def _simulate_social_interactions(self, player):
        """模拟社交互动"""
        other_player = self.game_world["characters"]["player2"]
        
        # 建立友谊
        social_event = BaseEvent(
            event_type="social_interaction",
            event_data={
                "initiator_id": player.id,
                "target_id": other_player.id,
                "interaction_type": "make_friend",
                "location": player.location.id
            },
            priority="MEDIUM"
        )
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(social_event)
            time.sleep(1)
    
    def _simulate_combat_experience(self, player):
        """模拟战斗体验"""
        opponent = self.game_world["characters"]["player3"]
        
        # 发起切磋
        combat_event = BaseEvent(
            event_type="combat_initiated",
            event_data={
                "participants": [player.id, opponent.id],
                "combat_type": "friendly_spar",
                "location": self.game_world["rooms"]["training_ground"].id
            },
            priority="HIGH"
        )
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(combat_event)
            time.sleep(3)
    
    def _verify_journey_system_responses(self, player):
        """验证整个历程的系统响应"""
        # 验证AI导演系统是否记录了玩家活动
        qiling_scripts = search_script("qiling_director_script")
        if qiling_scripts:
            qiling = qiling_scripts[0]
            if hasattr(qiling, 'player_activity_log'):
                activity_log = qiling.player_activity_log
                player_activities = [a for a in activity_log if a.get('character_id') == player.id]
                self.assertTrue(player_activities, "AI导演未记录玩家活动")
        
        # 验证小说生成系统是否创建了故事
        novel_scripts = search_script("novel_generator_script")
        if novel_scripts:
            novel_generator = novel_scripts[0]
            if hasattr(novel_generator, 'character_stories'):
                stories = novel_generator.character_stories
                player_stories = [s for s in stories if s.get('character_id') == player.id]
                self.assertTrue(player_stories, "小说生成系统未创建玩家故事")
        
        # 验证社交系统是否更新了关系
        from ..systems.social_relationship_manager import social_relationship_manager
        player_relationships = social_relationship_manager.get_character_relationships(player)
        self.assertTrue(player_relationships, "社交系统未记录玩家关系")


class MultiPlayerConcurrentTest(XianxiaIntegrationTest):
    """多玩家并发交互测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建多个玩家
        self.players = []
        for i in range(5):
            player = create_test_character(f"concurrent_player_{i}")
            self.players.append(player)
        
        # 创建共享环境
        self.shared_room = create_test_room("concurrent_test_room")
        for player in self.players:
            player.location = self.shared_room
        
        # 初始化系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_concurrent_player_interactions(self):
        """测试并发玩家互动"""
        log_info("开始测试并发玩家互动")
        
        def player_activity(player, activity_id):
            """单个玩家的活动"""
            activities = [
                self._perform_cultivation,
                self._perform_social_interaction,
                self._perform_exploration,
                self._perform_npc_interaction
            ]
            
            results = []
            for i in range(10):  # 每个玩家执行10个活动
                activity = activities[i % len(activities)]
                try:
                    result = activity(player, f"{activity_id}_{i}")
                    results.append(result)
                    time.sleep(0.1)  # 短暂间隔
                except Exception as e:
                    log_err(f"玩家活动失败: {e}")
                    results.append(False)
            
            return results
        
        # 启动并发玩家活动
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for i, player in enumerate(self.players):
                future = executor.submit(player_activity, player, f"activity_{i}")
                futures.append(future)
            
            # 收集结果
            all_results = []
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=30)
                    all_results.extend(result)
                except Exception as e:
                    log_err(f"并发活动执行失败: {e}")
        
        # 验证并发性能
        success_rate = sum(1 for r in all_results if r) / len(all_results) * 100
        self.assertGreater(success_rate, 80, f"并发活动成功率过低: {success_rate}%")
        
        # 验证系统稳定性
        self.assert_performance_within_limits(cpu_limit=90, memory_limit=85)
        
        log_info("并发玩家互动测试完成")
    
    def _perform_cultivation(self, player, activity_id):
        """执行修炼活动"""
        try:
            cultivation_event = BaseEvent(
                event_type="cultivation_session",
                event_data={
                    "character_id": player.id,
                    "activity_id": activity_id,
                    "cultivation_type": "meditation"
                },
                priority="MEDIUM"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(cultivation_event)
                return True
            return False
        except Exception:
            return False
    
    def _perform_social_interaction(self, player, activity_id):
        """执行社交互动"""
        try:
            # 随机选择另一个玩家互动
            other_players = [p for p in self.players if p != player]
            if other_players:
                target = other_players[0]
                
                social_event = BaseEvent(
                    event_type="social_interaction",
                    event_data={
                        "initiator_id": player.id,
                        "target_id": target.id,
                        "activity_id": activity_id,
                        "interaction_type": "chat"
                    },
                    priority="LOW"
                )
                
                event_bus_scripts = search_script("xianxia_event_bus")
                if event_bus_scripts:
                    event_bus = event_bus_scripts[0]
                    event_bus.publish_event(social_event)
                    return True
            return False
        except Exception:
            return False
    
    def _perform_exploration(self, player, activity_id):
        """执行探索活动"""
        try:
            exploration_event = BaseEvent(
                event_type="exploration",
                event_data={
                    "character_id": player.id,
                    "activity_id": activity_id,
                    "exploration_type": "area_scan"
                },
                priority="LOW"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(exploration_event)
                return True
            return False
        except Exception:
            return False
    
    def _perform_npc_interaction(self, player, activity_id):
        """执行NPC互动"""
        try:
            npc_event = BaseEvent(
                event_type="npc_interaction",
                event_data={
                    "character_id": player.id,
                    "activity_id": activity_id,
                    "interaction_type": "query"
                },
                priority="MEDIUM"
            )
            
            event_bus_scripts = search_script("xianxia_event_bus")
            if event_bus_scripts:
                event_bus = event_bus_scripts[0]
                event_bus.publish_event(npc_event)
                return True
            return False
        except Exception:
            return False


class LongRunningStabilityTest(XianxiaIntegrationTest):
    """长时间运行稳定性测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建持续运行的测试环境
        self.test_char = create_test_character("stability_test_char")
        self.test_room = create_test_room("stability_test_room")
        self.test_char.location = self.test_room
        
        # 初始化系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        self.stability_test_running = False
        self.stability_results = {
            "events_processed": 0,
            "errors_encountered": 0,
            "memory_samples": [],
            "cpu_samples": []
        }
    
    def tearDown(self):
        self.stability_test_running = False
        cleanup_test_objects()
        super().tearDown()
    
    def test_24_hour_stability_simulation(self):
        """模拟24小时稳定性测试（压缩到5分钟）"""
        log_info("开始24小时稳定性模拟测试")
        
        # 压缩时间：5分钟模拟24小时
        test_duration = 300  # 5分钟
        event_interval = 1.0  # 每秒一个事件
        
        self.stability_test_running = True
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitor_system_health)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 启动事件生成线程
        event_thread = threading.Thread(
            target=self._generate_continuous_events, 
            args=(test_duration, event_interval)
        )
        event_thread.daemon = True
        event_thread.start()
        
        # 等待测试完成
        event_thread.join()
        self.stability_test_running = False
        monitor_thread.join(timeout=5)
        
        # 分析稳定性结果
        self._analyze_stability_results()
        
        log_info("24小时稳定性模拟测试完成")
    
    def _monitor_system_health(self):
        """监控系统健康状况"""
        while self.stability_test_running:
            try:
                # 收集性能指标
                current_metrics = self.performance_monitor.get_current_metrics()
                if current_metrics:
                    self.stability_results["memory_samples"].append(current_metrics.memory_usage)
                    self.stability_results["cpu_samples"].append(current_metrics.cpu_usage)
                
                # 检查系统状态
                self.assert_system_state_consistent()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self.stability_results["errors_encountered"] += 1
                log_err(f"系统健康监控错误: {e}")
    
    def _generate_continuous_events(self, duration, interval):
        """生成连续事件"""
        start_time = time.time()
        event_types = [
            "cultivation_session",
            "social_interaction", 
            "npc_interaction",
            "exploration",
            "system_maintenance"
        ]
        
        while time.time() - start_time < duration and self.stability_test_running:
            try:
                # 随机选择事件类型
                event_type = event_types[int(time.time()) % len(event_types)]
                
                event = BaseEvent(
                    event_type=event_type,
                    event_data={
                        "character_id": self.test_char.id,
                        "timestamp": time.time(),
                        "test_event": True
                    },
                    priority="LOW"
                )
                
                # 发布事件
                event_bus_scripts = search_script("xianxia_event_bus")
                if event_bus_scripts:
                    event_bus = event_bus_scripts[0]
                    event_bus.publish_event(event)
                    self.stability_results["events_processed"] += 1
                
                time.sleep(interval)
                
            except Exception as e:
                self.stability_results["errors_encountered"] += 1
                log_err(f"事件生成错误: {e}")
    
    def _analyze_stability_results(self):
        """分析稳定性结果"""
        results = self.stability_results
        
        # 验证事件处理数量
        self.assertGreater(results["events_processed"], 200, "处理的事件数量过少")
        
        # 验证错误率
        error_rate = results["errors_encountered"] / max(results["events_processed"], 1) * 100
        self.assertLess(error_rate, 5, f"错误率过高: {error_rate}%")
        
        # 验证内存稳定性
        if results["memory_samples"]:
            memory_growth = max(results["memory_samples"]) - min(results["memory_samples"])
            self.assertLess(memory_growth, 20, f"内存增长过大: {memory_growth}%")
        
        # 验证CPU稳定性
        if results["cpu_samples"]:
            avg_cpu = sum(results["cpu_samples"]) / len(results["cpu_samples"])
            self.assertLess(avg_cpu, 80, f"平均CPU使用率过高: {avg_cpu}%")
        
        log_info(f"稳定性测试结果: 处理事件{results['events_processed']}个, "
                f"错误{results['errors_encountered']}个, 错误率{error_rate:.2f}%")


if __name__ == '__main__':
    # 运行端到端测试
    unittest.main(verbosity=2)
