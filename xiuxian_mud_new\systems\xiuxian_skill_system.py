"""
仙侠技能系统

管理仙侠技能的学习、使用、升级和效果计算。
基于Evennia Attributes系统存储技能数据，支持复杂的技能逻辑。
"""

from typing import Dict, List, Optional, Any
from evennia.utils import logger
from .wuxing_calculator import get_wuxing_calculator
import time
import json


class SkillType:
    """技能类型常量"""
    ATTACK = "攻击"      # 攻击技能
    DEFENSE = "防御"     # 防御技能
    HEAL = "治疗"        # 治疗技能
    BUFF = "增益"        # 增益技能
    DEBUFF = "减益"      # 减益技能
    MOVEMENT = "身法"    # 移动技能
    SPECIAL = "特殊"     # 特殊技能


class SkillGrade:
    """技能品级常量"""
    MORTAL = "凡级"      # 凡级
    YELLOW = "黄级"      # 黄级
    MYSTIC = "玄级"      # 玄级
    EARTH = "地级"       # 地级
    HEAVEN = "天级"      # 天级
    IMMORTAL = "仙级"    # 仙级


class XianxiaSkillSystem:
    """
    仙侠技能系统
    
    功能：
    - 技能数据管理和存储
    - 技能学习和升级
    - 技能效果计算
    - 技能冷却管理
    - 技能需求验证
    """
    
    # 默认技能库
    DEFAULT_SKILLS = {
        "基础剑法": {
            "type": SkillType.ATTACK,
            "grade": SkillGrade.MORTAL,
            "element": "金",
            "base_damage": 20,
            "mp_cost": 5,
            "cooldown": 0,
            "max_level": 10,
            "requirements": {
                "realm_level": 0,
                "weapon_type": "剑"
            },
            "description": "最基础的剑法技能，简单实用",
            "level_scaling": {
                "damage": 2,    # 每级增加2点伤害
                "mp_cost": 0.5  # 每级增加0.5点消耗
            }
        },
        "御剑术": {
            "type": SkillType.ATTACK,
            "grade": SkillGrade.YELLOW,
            "element": "金",
            "base_damage": 35,
            "mp_cost": 15,
            "cooldown": 2,
            "max_level": 8,
            "range": "long",
            "requirements": {
                "realm_level": 2,
                "weapon_type": "剑",
                "prerequisite": "基础剑法"
            },
            "description": "御剑飞行攻击敌人，可远程攻击",
            "level_scaling": {
                "damage": 5,
                "mp_cost": 1,
                "range": 0.5
            }
        },
        "灵力护体": {
            "type": SkillType.DEFENSE,
            "grade": SkillGrade.MORTAL,
            "element": "土",
            "defense_bonus": 15,
            "mp_cost": 8,
            "duration": 3,
            "cooldown": 5,
            "max_level": 10,
            "requirements": {
                "realm_level": 1
            },
            "description": "用灵力护体，提升防御力",
            "level_scaling": {
                "defense_bonus": 3,
                "duration": 0.5,
                "mp_cost": 0.8
            }
        },
        "回春术": {
            "type": SkillType.HEAL,
            "grade": SkillGrade.YELLOW,
            "element": "木",
            "heal_amount": 40,
            "mp_cost": 20,
            "cooldown": 8,
            "max_level": 6,
            "requirements": {
                "realm_level": 3,
                "profession": "医修"
            },
            "description": "木系治疗法术，恢复生命力",
            "level_scaling": {
                "heal_amount": 8,
                "mp_cost": 2
            }
        },
        "烈火掌": {
            "type": SkillType.ATTACK,
            "grade": SkillGrade.YELLOW,
            "element": "火",
            "base_damage": 30,
            "mp_cost": 12,
            "cooldown": 1,
            "max_level": 8,
            "special_effects": ["burn"],
            "requirements": {
                "realm_level": 2,
                "element_affinity": "火"
            },
            "description": "火系掌法，有几率造成灼烧效果",
            "level_scaling": {
                "damage": 4,
                "mp_cost": 1,
                "burn_chance": 0.05
            }
        },
        "寒冰箭": {
            "type": SkillType.ATTACK,
            "grade": SkillGrade.YELLOW,
            "element": "水",
            "base_damage": 25,
            "mp_cost": 10,
            "cooldown": 1,
            "max_level": 8,
            "special_effects": ["slow"],
            "requirements": {
                "realm_level": 2,
                "element_affinity": "水"
            },
            "description": "水系法术，有几率造成减速效果",
            "level_scaling": {
                "damage": 3,
                "mp_cost": 1,
                "slow_chance": 0.05
            }
        }
    }
    
    def __init__(self):
        """初始化技能系统"""
        self.wuxing_calc = get_wuxing_calculator()
        logger.log_info("XianxiaSkillSystem initialized")
    
    def load_skill_data(self, character) -> Dict:
        """
        加载角色的技能数据
        
        Args:
            character: 角色对象
            
        Returns:
            Dict: 技能数据字典
        """
        # 从Attributes加载技能数据
        skill_data = character.attributes.get("xiuxian_skills", {})
        
        # 如果没有技能数据，初始化默认技能
        if not skill_data:
            skill_data = self._initialize_default_skills(character)
            character.attributes.add("xiuxian_skills", skill_data)
        
        return skill_data
    
    def _initialize_default_skills(self, character) -> Dict:
        """
        初始化角色的默认技能
        
        Args:
            character: 角色对象
            
        Returns:
            Dict: 初始化的技能数据
        """
        skill_data = {}
        
        # 根据角色属性给予初始技能
        realm_level = getattr(character, 'get_realm_level', lambda: 0)()
        element = getattr(character, '五行属性', '土')
        
        # 所有角色都有基础技能
        basic_skills = ["基础剑法", "灵力护体"]
        
        for skill_name in basic_skills:
            if skill_name in self.DEFAULT_SKILLS:
                skill_template = self.DEFAULT_SKILLS[skill_name].copy()
                skill_data[skill_name] = {
                    "level": 1,
                    "experience": 0,
                    "last_used": 0,
                    "template": skill_template
                }
        
        logger.log_info(f"Initialized default skills for {character.key}: {list(skill_data.keys())}")
        return skill_data
    
    def learn_skill(self, character, skill_name: str) -> Dict:
        """
        学习新技能
        
        Args:
            character: 角色对象
            skill_name: 技能名称
            
        Returns:
            Dict: 学习结果
        """
        if skill_name not in self.DEFAULT_SKILLS:
            return {"success": False, "message": f"未知技能: {skill_name}"}
        
        skill_data = self.load_skill_data(character)
        
        # 检查是否已经学会
        if skill_name in skill_data:
            return {"success": False, "message": f"已经学会了 {skill_name}"}
        
        # 检查学习要求
        skill_template = self.DEFAULT_SKILLS[skill_name]
        check_result = self._check_skill_requirements(character, skill_template)
        
        if not check_result["can_learn"]:
            return {"success": False, "message": check_result["reason"]}
        
        # 学习技能
        skill_data[skill_name] = {
            "level": 1,
            "experience": 0,
            "last_used": 0,
            "template": skill_template.copy()
        }
        
        # 保存技能数据
        character.attributes.add("xiuxian_skills", skill_data)
        
        logger.log_info(f"{character.key} learned skill: {skill_name}")
        return {"success": True, "message": f"成功学会了 {skill_name}"}
    
    def _check_skill_requirements(self, character, skill_template: Dict) -> Dict:
        """
        检查技能学习要求
        
        Args:
            character: 角色对象
            skill_template: 技能模板
            
        Returns:
            Dict: 检查结果
        """
        requirements = skill_template.get("requirements", {})
        
        # 检查境界要求
        required_realm = requirements.get("realm_level", 0)
        current_realm = getattr(character, 'get_realm_level', lambda: 0)()
        if current_realm < required_realm:
            return {"can_learn": False, "reason": f"需要境界等级 {required_realm}"}
        
        # 检查五行亲和
        required_element = requirements.get("element_affinity")
        if required_element:
            character_element = getattr(character, '五行属性', '土')
            if character_element != required_element:
                return {"can_learn": False, "reason": f"需要 {required_element} 系亲和"}
        
        # 检查前置技能
        prerequisite = requirements.get("prerequisite")
        if prerequisite:
            skill_data = self.load_skill_data(character)
            if prerequisite not in skill_data:
                return {"can_learn": False, "reason": f"需要先学会 {prerequisite}"}
        
        # 检查职业要求
        required_profession = requirements.get("profession")
        if required_profession:
            character_profession = getattr(character, '职业类型', '散修')
            if character_profession != required_profession:
                return {"can_learn": False, "reason": f"需要职业: {required_profession}"}
        
        return {"can_learn": True, "reason": "满足学习条件"}
    
    def check_skill_cooldown(self, character, skill_name: str) -> Dict:
        """
        检查技能冷却时间
        
        Args:
            character: 角色对象
            skill_name: 技能名称
            
        Returns:
            Dict: 冷却检查结果
        """
        skill_data = self.load_skill_data(character)
        
        if skill_name not in skill_data:
            return {"ready": False, "reason": "未学会此技能"}
        
        skill_info = skill_data[skill_name]
        skill_template = skill_info["template"]
        
        current_time = time.time()
        last_used = skill_info.get("last_used", 0)
        cooldown = skill_template.get("cooldown", 0)
        
        if current_time - last_used < cooldown:
            remaining = cooldown - (current_time - last_used)
            return {"ready": False, "reason": f"冷却中，还需 {remaining:.1f} 秒"}
        
        return {"ready": True, "reason": "技能就绪"}
    
    def calculate_skill_damage(self, skill_data: Dict, caster, target=None) -> Dict:
        """
        计算技能伤害
        
        Args:
            skill_data: 技能数据
            caster: 施法者
            target: 目标对象
            
        Returns:
            Dict: 伤害计算结果
        """
        skill_info = skill_data
        skill_template = skill_info["template"]
        skill_level = skill_info["level"]
        
        result = {
            "base_damage": 0,
            "final_damage": 0,
            "modifiers": [],
            "effects": []
        }
        
        # 基础伤害
        base_damage = skill_template.get("base_damage", 0)
        if base_damage > 0:
            # 技能等级加成
            level_scaling = skill_template.get("level_scaling", {})
            damage_per_level = level_scaling.get("damage", 0)
            level_bonus = damage_per_level * (skill_level - 1)
            
            result["base_damage"] = base_damage + level_bonus
            result["modifiers"].append(f"技能等级 {skill_level}: +{level_bonus}")
            
            # 境界加成
            if hasattr(caster, 'get_realm_combat_bonus'):
                realm_bonus = caster.get_realm_combat_bonus()
                result["base_damage"] *= realm_bonus
                result["modifiers"].append(f"境界加成: x{realm_bonus:.1f}")
            
            # 五行相克加成
            if target and hasattr(target, '五行属性'):
                skill_element = skill_template.get("element", "土")
                target_element = getattr(target, '五行属性', '土')
                
                wuxing_bonus = self.wuxing_calc.calculate_elemental_bonus(skill_element, target_element)
                result["base_damage"] *= wuxing_bonus
                result["modifiers"].append(f"五行相克: x{wuxing_bonus:.1f}")
            
            result["final_damage"] = int(result["base_damage"])
        
        # 特殊效果
        special_effects = skill_template.get("special_effects", [])
        for effect in special_effects:
            effect_chance = self._calculate_effect_chance(skill_template, skill_level, effect)
            result["effects"].append({
                "type": effect,
                "chance": effect_chance,
                "description": self._get_effect_description(effect)
            })
        
        return result
    
    def _calculate_effect_chance(self, skill_template: Dict, skill_level: int, effect: str) -> float:
        """
        计算特殊效果触发几率
        
        Args:
            skill_template: 技能模板
            skill_level: 技能等级
            effect: 效果类型
            
        Returns:
            float: 触发几率
        """
        base_chance = 0.2  # 基础20%几率
        
        # 技能等级加成
        level_scaling = skill_template.get("level_scaling", {})
        chance_per_level = level_scaling.get(f"{effect}_chance", 0.05)
        
        return min(base_chance + chance_per_level * (skill_level - 1), 0.8)  # 最高80%
    
    def _get_effect_description(self, effect: str) -> str:
        """
        获取效果描述
        
        Args:
            effect: 效果类型
            
        Returns:
            str: 效果描述
        """
        descriptions = {
            "burn": "造成持续火焰伤害",
            "slow": "降低目标移动和攻击速度",
            "freeze": "冻结目标，无法行动",
            "poison": "造成持续毒素伤害",
            "stun": "眩晕目标，跳过下一回合"
        }
        
        return descriptions.get(effect, "未知效果")
    
    def apply_skill_effects(self, skill_data: Dict, target) -> List[Dict]:
        """
        应用技能效果
        
        Args:
            skill_data: 技能数据
            target: 目标对象
            
        Returns:
            List[Dict]: 应用的效果列表
        """
        applied_effects = []
        skill_template = skill_data["template"]
        
        # 治疗效果
        heal_amount = skill_template.get("heal_amount", 0)
        if heal_amount > 0:
            # 计算实际治疗量
            skill_level = skill_data["level"]
            level_scaling = skill_template.get("level_scaling", {})
            heal_per_level = level_scaling.get("heal_amount", 0)
            
            final_heal = heal_amount + heal_per_level * (skill_level - 1)
            
            applied_effects.append({
                "type": "heal",
                "value": final_heal,
                "target": target.key if target else None
            })
        
        # 防御加成效果
        defense_bonus = skill_template.get("defense_bonus", 0)
        if defense_bonus > 0:
            skill_level = skill_data["level"]
            level_scaling = skill_template.get("level_scaling", {})
            defense_per_level = level_scaling.get("defense_bonus", 0)
            duration = skill_template.get("duration", 1)
            
            final_defense = defense_bonus + defense_per_level * (skill_level - 1)
            
            applied_effects.append({
                "type": "defense_buff",
                "value": final_defense,
                "duration": duration,
                "target": target.key if target else None
            })
        
        return applied_effects
    
    def get_skill_info(self, character, skill_name: str) -> Dict:
        """
        获取技能详细信息
        
        Args:
            character: 角色对象
            skill_name: 技能名称
            
        Returns:
            Dict: 技能信息
        """
        skill_data = self.load_skill_data(character)
        
        if skill_name not in skill_data:
            return {"error": "未学会此技能"}
        
        skill_info = skill_data[skill_name]
        skill_template = skill_info["template"]
        
        # 计算当前等级的属性
        current_stats = self._calculate_current_stats(skill_template, skill_info["level"])
        
        return {
            "name": skill_name,
            "level": skill_info["level"],
            "experience": skill_info["experience"],
            "type": skill_template["type"],
            "grade": skill_template["grade"],
            "element": skill_template["element"],
            "description": skill_template["description"],
            "current_stats": current_stats,
            "requirements": skill_template.get("requirements", {}),
            "max_level": skill_template.get("max_level", 10)
        }
    
    def _calculate_current_stats(self, skill_template: Dict, level: int) -> Dict:
        """
        计算当前等级的技能属性
        
        Args:
            skill_template: 技能模板
            level: 技能等级
            
        Returns:
            Dict: 当前属性
        """
        stats = {}
        level_scaling = skill_template.get("level_scaling", {})
        
        # 计算各项属性
        for stat_name in ["damage", "heal_amount", "defense_bonus", "mp_cost"]:
            base_value = skill_template.get(f"base_{stat_name}", skill_template.get(stat_name, 0))
            scaling = level_scaling.get(stat_name, 0)
            
            if base_value > 0:
                current_value = base_value + scaling * (level - 1)
                stats[stat_name] = current_value
        
        # 其他固定属性
        for attr in ["cooldown", "duration", "range"]:
            if attr in skill_template:
                stats[attr] = skill_template[attr]
        
        return stats


# 全局实例
xiuxian_skill_system = XianxiaSkillSystem()


def get_skill_system() -> XianxiaSkillSystem:
    """获取技能系统实例"""
    return xiuxian_skill_system
