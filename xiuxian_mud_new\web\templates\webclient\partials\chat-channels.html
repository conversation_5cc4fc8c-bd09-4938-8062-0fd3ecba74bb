{% comment %}
聊天频道组件模板

功能：
- 多频道支持（世界/门派/队伍/私聊）
- 消息过滤和搜索功能
- 表情和表达支持
- 频道切换与未读指示器
- 实时消息接收和发送

技术集成：
- 与Evennia Channel系统完美集成
- WebSocket实时消息传输
- 本地存储聊天历史和偏好设置
- 移动端友好的触控操作
- 完整的消息管理功能
{% endcomment %}

<div class="chat-channels" id="chat-channels">
    <!-- 频道头部 -->
    <div class="chat-channels__header">
        <div class="chat-channels__title-section">
            <span class="chat-channels__icon">💬</span>
            <span class="chat-channels__title">聊天频道</span>
            <span class="chat-channels__online-count" id="online-count">在线: 42</span>
        </div>
        <div class="chat-channels__header-controls">
            <button type="button" class="chat-control-btn" id="chat-search-toggle" title="搜索消息">
                <span class="btn-icon">🔍</span>
            </button>
            <button type="button" class="chat-control-btn" id="chat-settings" title="聊天设置">
                <span class="btn-icon">⚙️</span>
            </button>
            <button type="button" class="chat-control-btn" id="chat-minimize" title="最小化">
                <span class="btn-icon">−</span>
            </button>
        </div>
    </div>

    <!-- 频道标签栏 -->
    <div class="chat-channels__tabs" id="chat-tabs">
        <div class="chat-tab active" data-channel="world" id="tab-world">
            <span class="tab-icon">🌍</span>
            <span class="tab-name">世界</span>
            <span class="tab-unread" id="unread-world">3</span>
        </div>
        
        <div class="chat-tab" data-channel="sect" id="tab-sect">
            <span class="tab-icon">🏛️</span>
            <span class="tab-name">门派</span>
            <span class="tab-unread hidden" id="unread-sect">0</span>
        </div>
        
        <div class="chat-tab" data-channel="team" id="tab-team">
            <span class="tab-icon">👥</span>
            <span class="tab-name">队伍</span>
            <span class="tab-unread hidden" id="unread-team">0</span>
        </div>
        
        <div class="chat-tab" data-channel="private" id="tab-private">
            <span class="tab-icon">🔒</span>
            <span class="tab-name">私聊</span>
            <span class="tab-unread" id="unread-private">1</span>
        </div>
        
        <div class="chat-tab" data-channel="system" id="tab-system">
            <span class="tab-icon">⚡</span>
            <span class="tab-name">系统</span>
            <span class="tab-unread hidden" id="unread-system">0</span>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="chat-channels__search hidden" id="chat-search">
        <div class="search-input-group">
            <input type="text" class="search-input" id="chat-search-input" placeholder="搜索消息内容...">
            <button type="button" class="search-btn" id="chat-search-btn">
                <span class="btn-icon">🔍</span>
            </button>
            <button type="button" class="search-clear-btn" id="chat-search-clear">
                <span class="btn-icon">✕</span>
            </button>
        </div>
        <div class="search-filters">
            <select id="search-channel-filter" class="search-filter">
                <option value="all">所有频道</option>
                <option value="world">世界频道</option>
                <option value="sect">门派频道</option>
                <option value="team">队伍频道</option>
                <option value="private">私聊</option>
                <option value="system">系统消息</option>
            </select>
            <select id="search-time-filter" class="search-filter">
                <option value="all">全部时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
            </select>
        </div>
    </div>

    <!-- 消息显示区域 -->
    <div class="chat-channels__content" id="chat-content">
        <!-- 世界频道 -->
        <div class="chat-channel active" data-channel="world" id="channel-world">
            <div class="chat-messages" id="messages-world">
                <!-- 示例消息 -->
                <div class="chat-message" data-type="normal" data-sender="张三丰" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender">张三丰</span>
                        <span class="message-level">[筑基期]</span>
                        <span class="message-time">14:32</span>
                    </div>
                    <div class="message-content">
                        各位道友，今日天象异常，似有大事将发生，大家需多加小心。
                    </div>
                    <div class="message-actions">
                        <button type="button" class="message-action-btn" data-action="reply">回复</button>
                        <button type="button" class="message-action-btn" data-action="private">私聊</button>
                        <button type="button" class="message-action-btn" data-action="report">举报</button>
                    </div>
                </div>

                <div class="chat-message" data-type="system" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender system">系统消息</span>
                        <span class="message-time">14:35</span>
                    </div>
                    <div class="message-content">
                        🌟 恭喜玩家【李逍遥】成功突破到金丹期！
                    </div>
                </div>

                <div class="chat-message" data-type="normal" data-sender="小龙女" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender">小龙女</span>
                        <span class="message-level">[金丹期]</span>
                        <span class="message-time">14:38</span>
                    </div>
                    <div class="message-content">
                        @张三丰 师父说得对，我刚才在古墓中也感受到了异样的灵气波动 😮
                    </div>
                    <div class="message-actions">
                        <button type="button" class="message-action-btn" data-action="reply">回复</button>
                        <button type="button" class="message-action-btn" data-action="private">私聊</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 门派频道 -->
        <div class="chat-channel" data-channel="sect" id="channel-sect">
            <div class="chat-messages" id="messages-sect">
                <div class="chat-message" data-type="normal" data-sender="掌门师兄" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender">掌门师兄</span>
                        <span class="message-level">[元婴期]</span>
                        <span class="message-time">14:20</span>
                    </div>
                    <div class="message-content">
                        各位师弟师妹，明日将有门派任务发布，请大家做好准备。
                    </div>
                </div>
            </div>
        </div>

        <!-- 队伍频道 -->
        <div class="chat-channel" data-channel="team" id="channel-team">
            <div class="chat-messages" id="messages-team">
                <div class="no-messages">
                    <span class="no-messages-icon">👥</span>
                    <span class="no-messages-text">暂无队伍消息</span>
                    <button type="button" class="join-team-btn">寻找队伍</button>
                </div>
            </div>
        </div>

        <!-- 私聊频道 -->
        <div class="chat-channel" data-channel="private" id="channel-private">
            <div class="private-chat-list" id="private-chat-list">
                <div class="private-chat-item active" data-target="王重阳">
                    <div class="chat-avatar">👤</div>
                    <div class="chat-info">
                        <div class="chat-name">王重阳</div>
                        <div class="chat-preview">师兄，有事相商...</div>
                        <div class="chat-time">14:25</div>
                    </div>
                    <div class="chat-unread">1</div>
                </div>
            </div>
            <div class="private-messages" id="private-messages">
                <div class="chat-message" data-type="received" data-sender="王重阳" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender">王重阳</span>
                        <span class="message-time">14:25</span>
                    </div>
                    <div class="message-content">
                        师兄，关于那个秘境的事情，我们需要商量一下。
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统频道 -->
        <div class="chat-channel" data-channel="system" id="channel-system">
            <div class="chat-messages" id="messages-system">
                <div class="chat-message" data-type="system" data-timestamp="1642234567890">
                    <div class="message-header">
                        <span class="message-sender system">系统通知</span>
                        <span class="message-time">14:00</span>
                    </div>
                    <div class="message-content">
                        🎉 欢迎来到仙侠世界！祝您修仙之路顺利！
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息输入区域 -->
    <div class="chat-channels__input-section" id="chat-input-section">
        <div class="input-tools">
            <button type="button" class="input-tool-btn" id="emoji-btn" title="表情">
                <span class="btn-icon">😊</span>
            </button>
            <button type="button" class="input-tool-btn" id="mention-btn" title="@某人">
                <span class="btn-icon">@</span>
            </button>
            <button type="button" class="input-tool-btn" id="image-btn" title="图片">
                <span class="btn-icon">🖼️</span>
            </button>
            <button type="button" class="input-tool-btn" id="voice-btn" title="语音">
                <span class="btn-icon">🎤</span>
            </button>
        </div>
        
        <div class="input-area">
            <textarea class="chat-input" id="chat-input" placeholder="输入消息... (Enter发送，Shift+Enter换行)" rows="2"></textarea>
            <button type="button" class="send-btn" id="chat-send-btn">
                <span class="btn-icon">📤</span>
                <span class="btn-text">发送</span>
            </button>
        </div>
        
        <div class="input-status">
            <span class="typing-indicator hidden" id="typing-indicator">有人正在输入...</span>
            <span class="char-count" id="char-count">0/500</span>
        </div>
    </div>
</div>

<!-- 表情选择器 -->
<div class="emoji-picker hidden" id="emoji-picker">
    <div class="emoji-categories">
        <button type="button" class="emoji-category active" data-category="smileys">😊</button>
        <button type="button" class="emoji-category" data-category="nature">🌸</button>
        <button type="button" class="emoji-category" data-category="objects">⚔️</button>
        <button type="button" class="emoji-category" data-category="symbols">💫</button>
    </div>
    <div class="emoji-grid" id="emoji-grid">
        <!-- 表情网格将由JavaScript动态生成 -->
    </div>
</div>

<!-- 聊天设置对话框 -->
<div class="chat-settings-dialog hidden" id="chat-settings-dialog">
    <div class="chat-settings-dialog__backdrop"></div>
    <div class="chat-settings-dialog__content">
        <div class="chat-settings-dialog__header">
            <h4>聊天设置</h4>
            <button type="button" class="chat-settings-dialog__close">×</button>
        </div>
        <div class="chat-settings-dialog__body">
            <div class="settings-section">
                <h5>频道设置</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="world-notifications" checked>
                        世界频道通知
                    </label>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="sect-notifications" checked>
                        门派频道通知
                    </label>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="private-notifications" checked>
                        私聊通知
                    </label>
                </div>
            </div>
            
            <div class="settings-section">
                <h5>显示设置</h5>
                <div class="setting-item">
                    <label for="message-font-size">字体大小：</label>
                    <select id="message-font-size">
                        <option value="small">小</option>
                        <option value="medium" selected>中</option>
                        <option value="large">大</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="show-timestamps" checked>
                        显示时间戳
                    </label>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="show-levels" checked>
                        显示等级信息
                    </label>
                </div>
            </div>
        </div>
        <div class="chat-settings-dialog__footer">
            <button type="button" class="btn btn-secondary" id="chat-settings-cancel">取消</button>
            <button type="button" class="btn" id="chat-settings-save">保存设置</button>
        </div>
    </div>
</div>

<script>
// 聊天频道管理器
window.ChatChannelsManager = {
    // 配置
    config: {
        maxMessages: 100,
        maxCharacters: 500,
        autoScrollThreshold: 50,
        typingTimeout: 3000,
        reconnectInterval: 5000
    },

    // 状态
    state: {
        currentChannel: 'world',
        isMinimized: false,
        isSearchVisible: false,
        unreadCounts: {
            world: 0,
            sect: 0,
            team: 0,
            private: 0,
            system: 0
        },
        typingUsers: new Set(),
        lastMessageTime: {},
        searchResults: []
    },

    // 表情数据
    emojis: {
        smileys: ['😊', '😂', '😍', '😭', '😡', '😱', '🤔', '😴', '😇', '🤗', '😎', '🙄'],
        nature: ['🌸', '🌺', '🌻', '🌹', '🌷', '🌿', '🍃', '🌙', '⭐', '☀️', '🌈', '❄️'],
        objects: ['⚔️', '🗡️', '🏹', '🛡️', '💎', '📿', '🔮', '📜', '⚗️', '🧪', '🔥', '💧'],
        symbols: ['💫', '✨', '🌟', '💥', '💢', '💯', '❤️', '💔', '💪', '👍', '👎', '🙏']
    },

    // 初始化
    init: function() {
        this.bindEvents();
        this.loadSettings();
        this.initializeEmojis();
        this.connectWebSocket();
        this.startTypingTimer();
        this.restoreChannelState();
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 频道切换
        document.querySelectorAll('.chat-tab').forEach(function(tab) {
            tab.addEventListener('click', function() {
                const channel = this.dataset.channel;
                self.switchChannel(channel);
            });
        });

        // 头部控制按钮
        document.getElementById('chat-search-toggle').addEventListener('click', function() {
            self.toggleSearch();
        });

        document.getElementById('chat-settings').addEventListener('click', function() {
            self.showSettings();
        });

        document.getElementById('chat-minimize').addEventListener('click', function() {
            self.toggleMinimize();
        });

        // 搜索功能
        document.getElementById('chat-search-btn').addEventListener('click', function() {
            self.performSearch();
        });

        document.getElementById('chat-search-clear').addEventListener('click', function() {
            self.clearSearch();
        });

        document.getElementById('chat-search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                self.performSearch();
            }
        });

        // 消息输入
        const chatInput = document.getElementById('chat-input');
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                self.sendMessage();
            }
        });

        chatInput.addEventListener('input', function() {
            self.updateCharCount();
            self.handleTyping();
        });

        // 发送按钮
        document.getElementById('chat-send-btn').addEventListener('click', function() {
            self.sendMessage();
        });

        // 输入工具
        document.getElementById('emoji-btn').addEventListener('click', function() {
            self.toggleEmojiPicker();
        });

        document.getElementById('mention-btn').addEventListener('click', function() {
            self.showMentionList();
        });

        // 表情选择器
        document.querySelectorAll('.emoji-category').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const category = this.dataset.category;
                self.switchEmojiCategory(category);
            });
        });

        // 设置对话框
        document.querySelector('.chat-settings-dialog__close').addEventListener('click', function() {
            self.hideSettings();
        });

        document.getElementById('chat-settings-cancel').addEventListener('click', function() {
            self.hideSettings();
        });

        document.getElementById('chat-settings-save').addEventListener('click', function() {
            self.saveSettings();
        });

        // 点击外部关闭
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.emoji-picker') && !e.target.closest('#emoji-btn')) {
                self.hideEmojiPicker();
            }
        });

        // 消息操作
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('message-action-btn')) {
                const action = e.target.dataset.action;
                const messageElement = e.target.closest('.chat-message');
                self.handleMessageAction(action, messageElement);
            }
        });

        // 私聊列表
        document.addEventListener('click', function(e) {
            if (e.target.closest('.private-chat-item')) {
                const target = e.target.closest('.private-chat-item').dataset.target;
                self.switchPrivateChat(target);
            }
        });
    },

    // 切换频道
    switchChannel: function(channel) {
        // 更新状态
        this.state.currentChannel = channel;

        // 更新标签样式
        document.querySelectorAll('.chat-tab').forEach(function(tab) {
            tab.classList.remove('active');
        });
        document.getElementById('tab-' + channel).classList.add('active');

        // 更新频道内容
        document.querySelectorAll('.chat-channel').forEach(function(channelEl) {
            channelEl.classList.remove('active');
        });
        document.getElementById('channel-' + channel).classList.add('active');

        // 清除未读计数
        this.clearUnreadCount(channel);

        // 滚动到底部
        this.scrollToBottom(channel);

        // 保存状态
        localStorage.setItem('chatCurrentChannel', channel);
    },

    // 清除未读计数
    clearUnreadCount: function(channel) {
        this.state.unreadCounts[channel] = 0;
        const unreadElement = document.getElementById('unread-' + channel);
        if (unreadElement) {
            unreadElement.textContent = '0';
            unreadElement.classList.add('hidden');
        }
    },

    // 增加未读计数
    incrementUnreadCount: function(channel) {
        if (channel === this.state.currentChannel) return;

        this.state.unreadCounts[channel]++;
        const unreadElement = document.getElementById('unread-' + channel);
        if (unreadElement) {
            unreadElement.textContent = this.state.unreadCounts[channel];
            unreadElement.classList.remove('hidden');
        }
    },

    // 发送消息
    sendMessage: function() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message) return;

        if (message.length > this.config.maxCharacters) {
            this.showNotification('消息长度超过限制', 'error');
            return;
        }

        // 构建消息数据
        const messageData = {
            channel: this.state.currentChannel,
            content: message,
            timestamp: Date.now(),
            type: 'normal'
        };

        // 发送到服务器
        this.sendChatCommand('send_message', messageData);

        // 清空输入框
        input.value = '';
        this.updateCharCount();

        // 停止输入指示器
        this.stopTyping();
    },

    // 接收消息
    receiveMessage: function(messageData) {
        const channel = messageData.channel;
        const messagesContainer = document.getElementById('messages-' + channel);

        if (!messagesContainer) return;

        // 创建消息元素
        const messageElement = this.createMessageElement(messageData);

        // 添加到容器
        messagesContainer.appendChild(messageElement);

        // 限制消息数量
        this.limitMessages(messagesContainer);

        // 更新未读计数
        this.incrementUnreadCount(channel);

        // 自动滚动
        if (channel === this.state.currentChannel) {
            this.scrollToBottom(channel);
        }

        // 播放通知音
        this.playNotificationSound(messageData);
    },

    // 创建消息元素
    createMessageElement: function(messageData) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message';
        messageDiv.dataset.type = messageData.type || 'normal';
        messageDiv.dataset.sender = messageData.sender || '';
        messageDiv.dataset.timestamp = messageData.timestamp;

        const timeStr = this.formatTime(messageData.timestamp);

        let messageHTML = '';

        if (messageData.type === 'system') {
            messageHTML = `
                <div class="message-header">
                    <span class="message-sender system">系统消息</span>
                    <span class="message-time">${timeStr}</span>
                </div>
                <div class="message-content">${this.processMessageContent(messageData.content)}</div>
            `;
        } else {
            const levelText = messageData.level ? `[${messageData.level}]` : '';
            messageHTML = `
                <div class="message-header">
                    <span class="message-sender">${messageData.sender}</span>
                    ${levelText ? `<span class="message-level">${levelText}</span>` : ''}
                    <span class="message-time">${timeStr}</span>
                </div>
                <div class="message-content">${this.processMessageContent(messageData.content)}</div>
                <div class="message-actions">
                    <button type="button" class="message-action-btn" data-action="reply">回复</button>
                    <button type="button" class="message-action-btn" data-action="private">私聊</button>
                    ${messageData.sender !== this.getCurrentPlayerName() ? '<button type="button" class="message-action-btn" data-action="report">举报</button>' : ''}
                </div>
            `;
        }

        messageDiv.innerHTML = messageHTML;
        return messageDiv;
    },

    // 处理消息内容
    processMessageContent: function(content) {
        // 处理@提及
        content = content.replace(/@(\w+)/g, '<span class="mention">@$1</span>');

        // 处理表情
        content = this.processEmojis(content);

        // 处理链接
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');

        return content;
    },

    // 处理表情
    processEmojis: function(content) {
        // 这里可以添加自定义表情处理逻辑
        return content;
    },

    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    },

    // 限制消息数量
    limitMessages: function(container) {
        const messages = container.querySelectorAll('.chat-message');
        if (messages.length > this.config.maxMessages) {
            const excess = messages.length - this.config.maxMessages;
            for (let i = 0; i < excess; i++) {
                messages[i].remove();
            }
        }
    },

    // 滚动到底部
    scrollToBottom: function(channel) {
        const messagesContainer = document.getElementById('messages-' + channel);
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    },

    // 切换搜索
    toggleSearch: function() {
        const searchElement = document.getElementById('chat-search');
        this.state.isSearchVisible = !this.state.isSearchVisible;

        if (this.state.isSearchVisible) {
            searchElement.classList.remove('hidden');
            document.getElementById('chat-search-input').focus();
        } else {
            searchElement.classList.add('hidden');
            this.clearSearch();
        }
    },

    // 执行搜索
    performSearch: function() {
        const query = document.getElementById('chat-search-input').value.trim();
        const channelFilter = document.getElementById('search-channel-filter').value;
        const timeFilter = document.getElementById('search-time-filter').value;

        if (!query) return;

        this.state.searchResults = this.searchMessages(query, channelFilter, timeFilter);
        this.displaySearchResults();
    },

    // 搜索消息
    searchMessages: function(query, channelFilter, timeFilter) {
        const results = [];
        const now = Date.now();
        const timeFilters = {
            today: 24 * 60 * 60 * 1000,
            week: 7 * 24 * 60 * 60 * 1000,
            month: 30 * 24 * 60 * 60 * 1000
        };

        const channels = channelFilter === 'all' ?
            ['world', 'sect', 'team', 'private', 'system'] :
            [channelFilter];

        channels.forEach(function(channel) {
            const messagesContainer = document.getElementById('messages-' + channel);
            if (!messagesContainer) return;

            const messages = messagesContainer.querySelectorAll('.chat-message');
            messages.forEach(function(message) {
                const content = message.querySelector('.message-content').textContent;
                const timestamp = parseInt(message.dataset.timestamp);

                // 时间过滤
                if (timeFilter !== 'all' && timeFilters[timeFilter]) {
                    if (now - timestamp > timeFilters[timeFilter]) return;
                }

                // 内容匹配
                if (content.toLowerCase().includes(query.toLowerCase())) {
                    results.push({
                        channel: channel,
                        element: message,
                        content: content,
                        timestamp: timestamp
                    });
                }
            });
        });

        return results.sort(function(a, b) { return b.timestamp - a.timestamp; });
    },

    // 显示搜索结果
    displaySearchResults: function() {
        // 这里可以实现搜索结果的高亮显示
        console.log('搜索结果:', this.state.searchResults);
        this.showNotification(`找到 ${this.state.searchResults.length} 条消息`, 'info');
    },

    // 清除搜索
    clearSearch: function() {
        document.getElementById('chat-search-input').value = '';
        this.state.searchResults = [];
    },

    // 切换表情选择器
    toggleEmojiPicker: function() {
        const emojiPicker = document.getElementById('emoji-picker');
        emojiPicker.classList.toggle('hidden');

        if (!emojiPicker.classList.contains('hidden')) {
            this.positionEmojiPicker();
        }
    },

    // 隐藏表情选择器
    hideEmojiPicker: function() {
        document.getElementById('emoji-picker').classList.add('hidden');
    },

    // 定位表情选择器
    positionEmojiPicker: function() {
        const emojiBtn = document.getElementById('emoji-btn');
        const emojiPicker = document.getElementById('emoji-picker');
        const rect = emojiBtn.getBoundingClientRect();

        emojiPicker.style.position = 'absolute';
        emojiPicker.style.bottom = (window.innerHeight - rect.top + 5) + 'px';
        emojiPicker.style.left = rect.left + 'px';
    },

    // 初始化表情
    initializeEmojis: function() {
        this.switchEmojiCategory('smileys');
    },

    // 切换表情分类
    switchEmojiCategory: function(category) {
        // 更新分类按钮
        document.querySelectorAll('.emoji-category').forEach(function(btn) {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // 更新表情网格
        const emojiGrid = document.getElementById('emoji-grid');
        emojiGrid.innerHTML = '';

        const self = this;
        this.emojis[category].forEach(function(emoji) {
            const emojiBtn = document.createElement('button');
            emojiBtn.type = 'button';
            emojiBtn.className = 'emoji-btn';
            emojiBtn.textContent = emoji;
            emojiBtn.addEventListener('click', function() {
                self.insertEmoji(emoji);
            });
            emojiGrid.appendChild(emojiBtn);
        });
    },

    // 插入表情
    insertEmoji: function(emoji) {
        const input = document.getElementById('chat-input');
        const cursorPos = input.selectionStart;
        const textBefore = input.value.substring(0, cursorPos);
        const textAfter = input.value.substring(input.selectionEnd);

        input.value = textBefore + emoji + textAfter;
        input.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        input.focus();

        this.updateCharCount();
        this.hideEmojiPicker();
    },

    // 显示@列表
    showMentionList: function() {
        // 这里可以实现@用户列表功能
        this.showNotification('@功能开发中', 'info');
    },

    // 更新字符计数
    updateCharCount: function() {
        const input = document.getElementById('chat-input');
        const count = input.value.length;
        const countElement = document.getElementById('char-count');

        countElement.textContent = `${count}/${this.config.maxCharacters}`;

        if (count > this.config.maxCharacters) {
            countElement.classList.add('over-limit');
        } else {
            countElement.classList.remove('over-limit');
        }
    },

    // 处理输入状态
    handleTyping: function() {
        this.sendTypingIndicator();

        // 重置输入超时
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        const self = this;
        this.typingTimeout = setTimeout(function() {
            self.stopTyping();
        }, this.config.typingTimeout);
    },

    // 发送输入指示器
    sendTypingIndicator: function() {
        this.sendChatCommand('typing_start', {
            channel: this.state.currentChannel
        });
    },

    // 停止输入
    stopTyping: function() {
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }

        this.sendChatCommand('typing_stop', {
            channel: this.state.currentChannel
        });
    },

    // 显示设置对话框
    showSettings: function() {
        document.getElementById('chat-settings-dialog').classList.remove('hidden');
    },

    // 隐藏设置对话框
    hideSettings: function() {
        document.getElementById('chat-settings-dialog').classList.add('hidden');
    },

    // 保存设置
    saveSettings: function() {
        const settings = {};

        // 收集设置
        settings.worldNotifications = document.getElementById('world-notifications').checked;
        settings.sectNotifications = document.getElementById('sect-notifications').checked;
        settings.privateNotifications = document.getElementById('private-notifications').checked;
        settings.messageFontSize = document.getElementById('message-font-size').value;
        settings.showTimestamps = document.getElementById('show-timestamps').checked;
        settings.showLevels = document.getElementById('show-levels').checked;

        // 保存到本地存储
        localStorage.setItem('chatSettings', JSON.stringify(settings));

        // 应用设置
        this.applySettings(settings);

        // 发送到服务器
        this.sendChatCommand('update_settings', settings);

        this.hideSettings();
        this.showNotification('设置已保存', 'success');
    },

    // 加载设置
    loadSettings: function() {
        const settings = JSON.parse(localStorage.getItem('chatSettings') || '{}');
        this.applySettings(settings);
        this.populateSettingsForm(settings);
    },

    // 应用设置
    applySettings: function(settings) {
        const chatChannels = document.getElementById('chat-channels');

        // 字体大小
        if (settings.messageFontSize) {
            chatChannels.dataset.fontSize = settings.messageFontSize;
        }

        // 时间戳显示
        if (settings.showTimestamps === false) {
            chatChannels.classList.add('hide-timestamps');
        } else {
            chatChannels.classList.remove('hide-timestamps');
        }

        // 等级显示
        if (settings.showLevels === false) {
            chatChannels.classList.add('hide-levels');
        } else {
            chatChannels.classList.remove('hide-levels');
        }
    },

    // 填充设置表单
    populateSettingsForm: function(settings) {
        if (settings.worldNotifications !== undefined) {
            document.getElementById('world-notifications').checked = settings.worldNotifications;
        }
        if (settings.sectNotifications !== undefined) {
            document.getElementById('sect-notifications').checked = settings.sectNotifications;
        }
        if (settings.privateNotifications !== undefined) {
            document.getElementById('private-notifications').checked = settings.privateNotifications;
        }
        if (settings.messageFontSize) {
            document.getElementById('message-font-size').value = settings.messageFontSize;
        }
        if (settings.showTimestamps !== undefined) {
            document.getElementById('show-timestamps').checked = settings.showTimestamps;
        }
        if (settings.showLevels !== undefined) {
            document.getElementById('show-levels').checked = settings.showLevels;
        }
    },

    // 切换最小化
    toggleMinimize: function() {
        const content = document.getElementById('chat-content');
        const inputSection = document.getElementById('chat-input-section');

        this.state.isMinimized = !this.state.isMinimized;

        if (this.state.isMinimized) {
            content.style.display = 'none';
            inputSection.style.display = 'none';
            document.getElementById('chat-minimize').innerHTML = '<span class="btn-icon">+</span>';
        } else {
            content.style.display = 'block';
            inputSection.style.display = 'block';
            document.getElementById('chat-minimize').innerHTML = '<span class="btn-icon">−</span>';
        }

        localStorage.setItem('chatMinimized', this.state.isMinimized);
    },

    // 处理消息操作
    handleMessageAction: function(action, messageElement) {
        const sender = messageElement.dataset.sender;
        const content = messageElement.querySelector('.message-content').textContent;

        switch (action) {
            case 'reply':
                this.replyToMessage(sender, content);
                break;
            case 'private':
                this.startPrivateChat(sender);
                break;
            case 'report':
                this.reportMessage(messageElement);
                break;
        }
    },

    // 回复消息
    replyToMessage: function(sender, content) {
        const input = document.getElementById('chat-input');
        const replyText = `@${sender} `;
        input.value = replyText;
        input.focus();
        input.setSelectionRange(replyText.length, replyText.length);
    },

    // 开始私聊
    startPrivateChat: function(target) {
        this.switchChannel('private');
        this.switchPrivateChat(target);
    },

    // 切换私聊对象
    switchPrivateChat: function(target) {
        // 更新私聊列表选中状态
        document.querySelectorAll('.private-chat-item').forEach(function(item) {
            item.classList.remove('active');
        });

        const targetItem = document.querySelector(`[data-target="${target}"]`);
        if (targetItem) {
            targetItem.classList.add('active');
        } else {
            // 创建新的私聊项
            this.createPrivateChatItem(target);
        }

        // 加载私聊消息
        this.loadPrivateMessages(target);
    },

    // 创建私聊项
    createPrivateChatItem: function(target) {
        const chatList = document.getElementById('private-chat-list');
        const chatItem = document.createElement('div');
        chatItem.className = 'private-chat-item active';
        chatItem.dataset.target = target;

        chatItem.innerHTML = `
            <div class="chat-avatar">👤</div>
            <div class="chat-info">
                <div class="chat-name">${target}</div>
                <div class="chat-preview">开始对话...</div>
                <div class="chat-time">刚刚</div>
            </div>
            <div class="chat-unread hidden">0</div>
        `;

        chatList.appendChild(chatItem);
    },

    // 加载私聊消息
    loadPrivateMessages: function(target) {
        // 这里应该从服务器加载私聊消息
        const messagesContainer = document.getElementById('private-messages');
        messagesContainer.innerHTML = `
            <div class="chat-message" data-type="system">
                <div class="message-content">开始与 ${target} 的私聊</div>
            </div>
        `;
    },

    // 举报消息
    reportMessage: function(messageElement) {
        const sender = messageElement.dataset.sender;
        const content = messageElement.querySelector('.message-content').textContent;

        if (confirm(`确定要举报用户 ${sender} 的消息吗？`)) {
            this.sendChatCommand('report_message', {
                sender: sender,
                content: content,
                timestamp: messageElement.dataset.timestamp
            });
            this.showNotification('举报已提交', 'success');
        }
    },

    // 连接WebSocket
    connectWebSocket: function() {
        if (!window.Evennia || !window.Evennia.msg) {
            console.warn('Evennia WebSocket not available');
            return;
        }

        // 保存原始消息处理器
        this.originalEvenniaMsg = window.Evennia.msg;

        // 扩展消息处理器
        const self = this;
        window.Evennia.msg = function(cmdname, args, kwargs) {
            // 处理聊天相关消息
            if (cmdname === 'chat_message') {
                self.handleChatMessage(args, kwargs);
            } else if (cmdname === 'typing_indicator') {
                self.handleTypingIndicator(args, kwargs);
            } else if (cmdname === 'online_count') {
                self.updateOnlineCount(args.count);
            }

            // 调用原始处理器
            if (self.originalEvenniaMsg) {
                self.originalEvenniaMsg(cmdname, args, kwargs);
            }
        };
    },

    // 处理聊天消息
    handleChatMessage: function(args, kwargs) {
        const messageData = {
            channel: args.channel,
            sender: args.sender,
            content: args.content,
            timestamp: args.timestamp || Date.now(),
            type: args.type || 'normal',
            level: args.level
        };

        this.receiveMessage(messageData);
    },

    // 处理输入指示器
    handleTypingIndicator: function(args, kwargs) {
        const channel = args.channel;
        const user = args.user;
        const isTyping = args.typing;

        if (isTyping) {
            this.state.typingUsers.add(user);
        } else {
            this.state.typingUsers.delete(user);
        }

        this.updateTypingIndicator(channel);
    },

    // 更新输入指示器
    updateTypingIndicator: function(channel) {
        if (channel !== this.state.currentChannel) return;

        const indicator = document.getElementById('typing-indicator');

        if (this.state.typingUsers.size > 0) {
            const users = Array.from(this.state.typingUsers);
            let text = '';

            if (users.length === 1) {
                text = `${users[0]} 正在输入...`;
            } else if (users.length === 2) {
                text = `${users[0]} 和 ${users[1]} 正在输入...`;
            } else {
                text = `${users[0]} 等 ${users.length} 人正在输入...`;
            }

            indicator.textContent = text;
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    },

    // 更新在线人数
    updateOnlineCount: function(count) {
        document.getElementById('online-count').textContent = `在线: ${count}`;
    },

    // 发送聊天命令
    sendChatCommand: function(command, data) {
        if (window.Evennia && window.Evennia.msg) {
            window.Evennia.msg('chat_command', [command], data);
        }
    },

    // 播放通知音
    playNotificationSound: function(messageData) {
        const settings = JSON.parse(localStorage.getItem('chatSettings') || '{}');
        const channel = messageData.channel;

        let shouldNotify = false;

        switch (channel) {
            case 'world':
                shouldNotify = settings.worldNotifications !== false;
                break;
            case 'sect':
                shouldNotify = settings.sectNotifications !== false;
                break;
            case 'private':
                shouldNotify = settings.privateNotifications !== false;
                break;
        }

        if (shouldNotify && channel !== this.state.currentChannel) {
            // 播放通知音（如果浏览器支持）
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 0.3;
                audio.play().catch(function() {
                    // 忽略播放失败
                });
            } catch (e) {
                // 忽略音频错误
            }
        }
    },

    // 显示通知
    showNotification: function(message, type) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `chat-notification ${type}`;
        notification.textContent = message;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(function() {
            notification.remove();
        }, 3000);
    },

    // 获取当前玩家名称
    getCurrentPlayerName: function() {
        // 这里应该从Evennia获取当前玩家名称
        return window.Evennia && window.Evennia.player_name || '未知玩家';
    },

    // 恢复频道状态
    restoreChannelState: function() {
        // 恢复当前频道
        const savedChannel = localStorage.getItem('chatCurrentChannel');
        if (savedChannel) {
            this.switchChannel(savedChannel);
        }

        // 恢复最小化状态
        const isMinimized = localStorage.getItem('chatMinimized') === 'true';
        if (isMinimized) {
            this.toggleMinimize();
        }
    },

    // 开始输入定时器
    startTypingTimer: function() {
        // 定期清理过期的输入指示器
        const self = this;
        setInterval(function() {
            self.cleanupTypingIndicators();
        }, 5000);
    },

    // 清理输入指示器
    cleanupTypingIndicators: function() {
        // 这里可以添加清理逻辑
    },

    // 销毁组件
    destroy: function() {
        // 恢复原始消息处理器
        if (this.originalEvenniaMsg) {
            window.Evennia.msg = this.originalEvenniaMsg;
        }

        // 清理定时器
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        // 清理事件监听器
        // 由于使用了事件委托，大部分事件会自动清理
    }
};

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.ChatChannelsManager) {
        window.ChatChannelsManager.init();
    }
});
</script>
