"""
仙侠门派频道系统

基于Evennia DefaultChannel实现的门派内部通信系统，包括：
- 门派频道：基于Tags的权限控制
- 门派管理：长老权限和成员管理
- 事件集成：自动发布门派相关事件
- 消息过滤：基于门派等级的消息权限

完全基于Evennia原生组件，与现有系统完美集成。
"""

import time
from typing import Dict, Any, List, Optional
from evennia.comms.comms import DefaultChannel
from evennia.utils import logger
from evennia import search


class SectChannel(DefaultChannel):
    """
    门派频道类
    
    基于Evennia DefaultChannel扩展，实现门派内部通信系统：
    - 基于Tags的成员身份验证
    - 分级权限控制（长老、弟子、外门弟子）
    - 自动事件发布
    - 门派消息历史记录
    """
    
    def at_channel_creation(self):
        """频道创建时的初始化"""
        super().at_channel_creation()
        
        # 设置门派频道的基本权限
        # 只有门派成员可以监听和发送消息
        self.locks.add("listen:tag(sect_member);send:tag(sect_member)")
        # 只有门派长老可以控制频道
        self.locks.add("control:tag(sect_elder)")
        
        # 初始化门派频道属性
        if not hasattr(self.db, 'sect_name'):
            self.db.sect_name = ""
        if not hasattr(self.db, 'sect_level'):
            self.db.sect_level = "普通门派"
        if not hasattr(self.db, 'member_count'):
            self.db.member_count = 0
        if not hasattr(self.db, 'creation_time'):
            self.db.creation_time = time.time()
        if not hasattr(self.db, 'last_activity'):
            self.db.last_activity = time.time()
        
        # 设置频道描述
        if not self.db.desc:
            self.db.desc = f"{self.db.sect_name}门派内部交流频道"
        
        logger.log_info(f"门派频道创建: {self.key} ({self.db.sect_name})")
    
    def at_msg_receive(self, message, from_obj, **kwargs):
        """
        接收消息时的处理
        
        Args:
            message: 消息内容
            from_obj: 发送者
            **kwargs: 额外参数
        """
        # 调用父类方法处理消息
        super().at_msg_receive(message, from_obj, **kwargs)
        
        # 更新最后活动时间
        self.db.last_activity = time.time()
        
        # 发布门派消息事件
        self._publish_sect_message_event(from_obj, message)
        
        # 记录门派活动
        self._record_sect_activity(from_obj, message)
    
    def _publish_sect_message_event(self, sender, message):
        """发布门派消息事件"""
        try:
            from systems.social_events import publish_sect_event, SocialEventType
            
            publish_sect_event(
                self.db.sect_name,
                sender,
                SocialEventType.SECT_MESSAGE.value,
                message=message,
                channel=self.key,
                timestamp=time.time()
            )
        except Exception as e:
            logger.log_err(f"门派消息事件发布失败: {e}")
    
    def _record_sect_activity(self, sender, message):
        """记录门派活动"""
        if not hasattr(self.db, 'activity_log'):
            self.db.activity_log = []
        
        # 保持最近100条活动记录
        if len(self.db.activity_log) >= 100:
            self.db.activity_log = self.db.activity_log[-99:]
        
        activity = {
            "timestamp": time.time(),
            "sender": sender.key if sender else "系统",
            "sender_id": sender.id if sender else None,
            "message": message[:100],  # 限制消息长度
            "type": "message"
        }
        
        self.db.activity_log.append(activity)
    
    def add_sect_member(self, character, rank="弟子"):
        """
        添加门派成员
        
        Args:
            character: 角色对象
            rank: 门派等级（弟子、外门弟子、内门弟子、长老等）
        """
        try:
            # 添加门派成员标签
            character.tags.add("sect_member", category="social_status")
            character.tags.add(f"sect_{self.db.sect_name}", category="sect_affiliation")
            character.tags.add(f"sect_rank_{rank}", category="sect_rank")
            
            # 更新角色的门派归属
            if hasattr(character, '门派归属'):
                character.门派归属 = self.db.sect_name
            
            # 连接到门派频道
            self.connect(character)
            
            # 更新成员数量
            self.db.member_count = len(self.subscriptions.all())
            
            # 发布加入门派事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                self.db.sect_name,
                character,
                SocialEventType.SECT_JOIN.value,
                rank=rank,
                member_count=self.db.member_count
            )
            
            # 向频道发送欢迎消息
            welcome_msg = f"{character.key}加入了{self.db.sect_name}，成为{rank}！"
            self.msg(welcome_msg, senders=[character])
            
            logger.log_info(f"角色 {character.key} 加入门派 {self.db.sect_name} (等级: {rank})")
            return True
            
        except Exception as e:
            logger.log_err(f"添加门派成员失败: {e}")
            return False
    
    def remove_sect_member(self, character, reason="主动退出"):
        """
        移除门派成员
        
        Args:
            character: 角色对象
            reason: 离开原因
        """
        try:
            # 移除门派相关标签
            character.tags.remove("sect_member", category="social_status")
            character.tags.remove(f"sect_{self.db.sect_name}", category="sect_affiliation")
            
            # 移除所有门派等级标签
            for tag in character.tags.get(category="sect_rank"):
                character.tags.remove(tag, category="sect_rank")
            
            # 更新角色的门派归属
            if hasattr(character, '门派归属'):
                character.门派归属 = "无门派"
            
            # 断开频道连接
            self.disconnect(character)
            
            # 更新成员数量
            self.db.member_count = len(self.subscriptions.all())
            
            # 发布离开门派事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                self.db.sect_name,
                character,
                SocialEventType.SECT_LEAVE.value,
                reason=reason,
                member_count=self.db.member_count
            )
            
            # 向频道发送离开消息
            leave_msg = f"{character.key}离开了{self.db.sect_name}（{reason}）"
            self.msg(leave_msg)
            
            logger.log_info(f"角色 {character.key} 离开门派 {self.db.sect_name} (原因: {reason})")
            return True
            
        except Exception as e:
            logger.log_err(f"移除门派成员失败: {e}")
            return False
    
    def promote_member(self, character, new_rank):
        """
        晋升门派成员
        
        Args:
            character: 角色对象
            new_rank: 新等级
        """
        try:
            # 移除旧等级标签
            for tag in character.tags.get(category="sect_rank"):
                character.tags.remove(tag, category="sect_rank")
            
            # 添加新等级标签
            character.tags.add(f"sect_rank_{new_rank}", category="sect_rank")
            
            # 如果晋升为长老，添加长老权限
            if new_rank in ["长老", "太上长老", "掌门"]:
                character.tags.add("sect_elder", category="social_status")
            
            # 发布晋升事件
            from systems.social_events import publish_sect_event, SocialEventType
            publish_sect_event(
                self.db.sect_name,
                character,
                SocialEventType.SECT_PROMOTION.value,
                new_rank=new_rank,
                promotion_time=time.time()
            )
            
            # 向频道发送晋升消息
            promotion_msg = f"恭喜{character.key}晋升为{self.db.sect_name}{new_rank}！"
            self.msg(promotion_msg)
            
            logger.log_info(f"角色 {character.key} 在门派 {self.db.sect_name} 晋升为 {new_rank}")
            return True
            
        except Exception as e:
            logger.log_err(f"门派成员晋升失败: {e}")
            return False
    
    def get_sect_members(self) -> List[Dict[str, Any]]:
        """获取门派成员列表"""
        members = []
        
        for subscriber in self.subscriptions.all():
            if hasattr(subscriber, 'tags'):
                # 获取门派等级
                rank_tags = subscriber.tags.get(category="sect_rank")
                rank = "弟子"  # 默认等级
                if rank_tags:
                    for tag in rank_tags:
                        if tag.startswith("sect_rank_"):
                            rank = tag.replace("sect_rank_", "")
                            break
                
                member_info = {
                    "id": subscriber.id,
                    "name": subscriber.key,
                    "rank": rank,
                    "realm": getattr(subscriber, '修为境界', '未知'),
                    "profession": getattr(subscriber, '职业类型', '散修'),
                    "join_time": getattr(subscriber.db, 'sect_join_time', 0)
                }
                members.append(member_info)
        
        # 按等级和加入时间排序
        rank_order = {"掌门": 0, "太上长老": 1, "长老": 2, "内门弟子": 3, "弟子": 4, "外门弟子": 5}
        members.sort(key=lambda x: (rank_order.get(x["rank"], 6), x["join_time"]))
        
        return members
    
    def get_sect_info(self) -> Dict[str, Any]:
        """获取门派信息"""
        return {
            "name": self.db.sect_name,
            "level": self.db.sect_level,
            "member_count": self.db.member_count,
            "creation_time": self.db.creation_time,
            "last_activity": self.db.last_activity,
            "description": self.db.desc,
            "channel_key": self.key
        }
    
    def send_sect_announcement(self, message, sender=None):
        """发送门派公告"""
        announcement = f"|y[门派公告]|n {message}"
        self.msg(announcement, senders=[sender] if sender else None)
        
        # 记录公告
        self._record_sect_activity(sender, f"[公告] {message}")
        
        # 发布门派公告事件
        from systems.social_events import publish_sect_event, SocialEventType
        publish_sect_event(
            self.db.sect_name,
            sender,
            SocialEventType.SECT_MESSAGE.value,
            message=message,
            message_type="announcement",
            timestamp=time.time()
        )


class SectChannelManager:
    """
    门派频道管理器
    
    提供门派频道的创建、查找、管理等功能
    """
    
    @staticmethod
    def create_sect_channel(sect_name: str, creator=None) -> Optional[SectChannel]:
        """
        创建门派频道
        
        Args:
            sect_name: 门派名称
            creator: 创建者
            
        Returns:
            SectChannel: 创建的门派频道对象
        """
        try:
            from evennia import create_channel
            
            channel_key = f"{sect_name}_channel"
            
            # 检查是否已存在同名频道
            existing = search.search_channel(channel_key)
            if existing:
                logger.log_warn(f"门派频道已存在: {channel_key}")
                return existing[0]
            
            # 创建门派频道
            channel = create_channel(
                key=channel_key,
                aliases=[sect_name[:2]],  # 使用门派名前两个字作为别名
                desc=f"{sect_name}门派内部交流频道",
                typeclass=SectChannel,
                locks="listen:tag(sect_member);send:tag(sect_member);control:tag(sect_elder)",
                keep_log=True,
                tags=[("门派频道", "channel_type"), (sect_name, "sect_name")]
            )
            
            if channel:
                # 设置门派信息
                channel.db.sect_name = sect_name
                channel.db.creator = creator.key if creator else "系统"
                channel.db.creation_time = time.time()
                
                logger.log_info(f"门派频道创建成功: {channel_key}")
                return channel
            else:
                logger.log_err(f"门派频道创建失败: {channel_key}")
                return None
                
        except Exception as e:
            logger.log_err(f"创建门派频道时发生错误: {e}")
            return None
    
    @staticmethod
    def find_sect_channel(sect_name: str) -> Optional[SectChannel]:
        """
        查找门派频道
        
        Args:
            sect_name: 门派名称
            
        Returns:
            SectChannel: 门派频道对象
        """
        try:
            channel_key = f"{sect_name}_channel"
            channels = search.search_channel(channel_key)
            
            if channels:
                return channels[0]
            
            # 尝试通过标签查找
            channels = search.search_channel(tags=[(sect_name, "sect_name")])
            if channels:
                return channels[0]
            
            return None
            
        except Exception as e:
            logger.log_err(f"查找门派频道时发生错误: {e}")
            return None
    
    @staticmethod
    def get_all_sect_channels() -> List[SectChannel]:
        """获取所有门派频道"""
        try:
            channels = search.search_channel(tags=[("门派频道", "channel_type")])
            return [ch for ch in channels if isinstance(ch, SectChannel)]
        except Exception as e:
            logger.log_err(f"获取门派频道列表时发生错误: {e}")
            return []
    
    @staticmethod
    def delete_sect_channel(sect_name: str) -> bool:
        """
        删除门派频道
        
        Args:
            sect_name: 门派名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            channel = SectChannelManager.find_sect_channel(sect_name)
            if channel:
                # 发布门派解散事件
                from systems.social_events import publish_sect_event, SocialEventType
                publish_sect_event(
                    sect_name,
                    None,
                    SocialEventType.SECT_DISSOLUTION.value,
                    dissolution_time=time.time()
                )
                
                # 删除频道
                channel.delete()
                logger.log_info(f"门派频道已删除: {sect_name}")
                return True
            else:
                logger.log_warn(f"未找到门派频道: {sect_name}")
                return False
                
        except Exception as e:
            logger.log_err(f"删除门派频道时发生错误: {e}")
            return False
