"""
三层AI导演架构集成模块

负责三层导演之间的通信、协调和事件总线集成：
- 导演间通信协议
- 事件总线集成
- 决策协调机制
- 性能监控和调试
"""

import time
from typing import Dict, Any, List, Optional
from enum import Enum

from evennia.utils import logger
from evennia.scripts.scripts import DefaultScript

from .base_director import BaseDirector
from systems.event_system import BaseEvent, EventPriority, publish_event, subscribe_to_event


class DirectorLayer(Enum):
    """导演层级"""
    TIANDAO = "tiandao"  # 天道导演
    DILING = "diling"    # 地灵导演
    QILING = "qiling"    # 器灵导演


class DirectorIntegration(DefaultScript):
    """
    三层AI导演架构集成系统
    
    管理三层导演之间的协调和通信
    """
    
    def at_script_creation(self):
        """系统初始化"""
        self.key = "director_integration"
        self.desc = "三层AI导演架构集成系统"
        self.persistent = True
        self.interval = 30  # 30秒检查一次
        
        # 初始化导演注册表
        self.db.registered_directors = {
            DirectorLayer.TIANDAO: None,
            DirectorLayer.DILING: None,
            DirectorLayer.QILING: None
        }
        
        # 初始化通信状态
        self.db.communication_state = {
            "message_queue": [],
            "coordination_requests": [],
            "shared_context": {},
            "last_sync_time": time.time()
        }
        
        # 初始化性能监控
        self.db.performance_metrics = {
            "message_count": 0,
            "coordination_count": 0,
            "sync_frequency": 0,
            "average_response_time": 0.0,
            "error_count": 0
        }
        
        # 订阅导演事件
        self._subscribe_to_director_events()
        
        logger.log_info("三层AI导演架构集成系统已初始化")
    
    def at_repeat(self):
        """定期检查和协调"""
        try:
            # 处理消息队列
            self._process_message_queue()
            
            # 处理协调请求
            self._process_coordination_requests()
            
            # 同步共享上下文
            self._sync_shared_context()
            
            # 更新性能指标
            self._update_performance_metrics()
            
        except Exception as e:
            logger.log_err(f"导演集成系统更新失败: {e}")
            self.db.performance_metrics["error_count"] += 1
    
    def register_director(self, layer: DirectorLayer, director: BaseDirector):
        """注册导演"""
        try:
            self.db.registered_directors[layer] = director
            logger.log_info(f"已注册{layer.value}导演: {director.key}")
            
            # 发布导演注册事件
            self._publish_director_registration_event(layer, director)
            
        except Exception as e:
            logger.log_err(f"注册导演失败: {e}")
    
    def send_message(self, from_layer: DirectorLayer, to_layer: DirectorLayer, 
                    message_type: str, data: Dict[str, Any], priority: str = "normal"):
        """发送导演间消息"""
        try:
            message = {
                "id": f"msg_{int(time.time())}_{len(self.db.communication_state['message_queue'])}",
                "from_layer": from_layer.value,
                "to_layer": to_layer.value,
                "message_type": message_type,
                "data": data,
                "priority": priority,
                "timestamp": time.time(),
                "status": "pending"
            }
            
            self.db.communication_state["message_queue"].append(message)
            self.db.performance_metrics["message_count"] += 1
            
            logger.log_info(f"消息已发送: {from_layer.value} -> {to_layer.value} ({message_type})")
            
        except Exception as e:
            logger.log_err(f"发送消息失败: {e}")
    
    def request_coordination(self, requesting_layer: DirectorLayer, 
                           coordination_type: str, context: Dict[str, Any]):
        """请求导演协调"""
        try:
            request = {
                "id": f"coord_{int(time.time())}_{len(self.db.communication_state['coordination_requests'])}",
                "requesting_layer": requesting_layer.value,
                "coordination_type": coordination_type,
                "context": context,
                "timestamp": time.time(),
                "status": "pending",
                "responses": {}
            }
            
            self.db.communication_state["coordination_requests"].append(request)
            self.db.performance_metrics["coordination_count"] += 1
            
            logger.log_info(f"协调请求已创建: {requesting_layer.value} ({coordination_type})")
            
            # 通知其他导演
            self._notify_coordination_request(request)
            
        except Exception as e:
            logger.log_err(f"请求协调失败: {e}")
    
    def update_shared_context(self, layer: DirectorLayer, context_key: str, 
                            context_value: Any):
        """更新共享上下文"""
        try:
            if layer.value not in self.db.communication_state["shared_context"]:
                self.db.communication_state["shared_context"][layer.value] = {}
            
            self.db.communication_state["shared_context"][layer.value][context_key] = {
                "value": context_value,
                "timestamp": time.time(),
                "layer": layer.value
            }
            
            logger.log_info(f"共享上下文已更新: {layer.value}.{context_key}")
            
        except Exception as e:
            logger.log_err(f"更新共享上下文失败: {e}")
    
    def get_shared_context(self, layer: Optional[DirectorLayer] = None) -> Dict[str, Any]:
        """获取共享上下文"""
        if layer:
            return self.db.communication_state["shared_context"].get(layer.value, {})
        else:
            return self.db.communication_state["shared_context"]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.db.performance_metrics.copy()
    
    def _process_message_queue(self):
        """处理消息队列"""
        queue = self.db.communication_state["message_queue"]
        processed_messages = []
        
        for message in queue:
            if message["status"] == "pending":
                success = self._deliver_message(message)
                if success:
                    message["status"] = "delivered"
                    message["delivered_time"] = time.time()
                else:
                    message["status"] = "failed"
                
                processed_messages.append(message)
        
        # 清理已处理的消息（保留最近100条）
        if len(queue) > 100:
            self.db.communication_state["message_queue"] = queue[-100:]
    
    def _deliver_message(self, message: Dict[str, Any]) -> bool:
        """投递消息"""
        try:
            to_layer = DirectorLayer(message["to_layer"])
            director = self.db.registered_directors.get(to_layer)
            
            if director:
                # 调用导演的消息处理方法
                if hasattr(director, 'handle_inter_director_message'):
                    director.handle_inter_director_message(message)
                    return True
                else:
                    logger.log_warn(f"导演{to_layer.value}不支持消息处理")
                    return False
            else:
                logger.log_warn(f"目标导演{to_layer.value}未注册")
                return False
                
        except Exception as e:
            logger.log_err(f"投递消息失败: {e}")
            return False
    
    def _process_coordination_requests(self):
        """处理协调请求"""
        requests = self.db.communication_state["coordination_requests"]
        
        for request in requests:
            if request["status"] == "pending":
                self._process_single_coordination_request(request)
    
    def _process_single_coordination_request(self, request: Dict[str, Any]):
        """处理单个协调请求"""
        try:
            requesting_layer = DirectorLayer(request["requesting_layer"])
            coordination_type = request["coordination_type"]
            
            # 根据协调类型处理
            if coordination_type == "world_event_coordination":
                self._coordinate_world_event(request)
            elif coordination_type == "resource_allocation":
                self._coordinate_resource_allocation(request)
            elif coordination_type == "conflict_resolution":
                self._coordinate_conflict_resolution(request)
            else:
                logger.log_warn(f"未知的协调类型: {coordination_type}")
                request["status"] = "failed"
                
        except Exception as e:
            logger.log_err(f"处理协调请求失败: {e}")
            request["status"] = "failed"
    
    def _coordinate_world_event(self, request: Dict[str, Any]):
        """协调世界事件"""
        # 通知所有导演参与世界事件协调
        for layer, director in self.db.registered_directors.items():
            if director and layer.value != request["requesting_layer"]:
                response = self._get_director_response(director, request)
                request["responses"][layer.value] = response
        
        # 综合响应并做出协调决策
        coordination_result = self._make_coordination_decision(request)
        request["result"] = coordination_result
        request["status"] = "completed"
        
        logger.log_info(f"世界事件协调完成: {request['id']}")
    
    def _coordinate_resource_allocation(self, request: Dict[str, Any]):
        """协调资源分配"""
        # 简化的资源分配协调
        request["result"] = {"allocation": "balanced", "priority": "normal"}
        request["status"] = "completed"
        
        logger.log_info(f"资源分配协调完成: {request['id']}")
    
    def _coordinate_conflict_resolution(self, request: Dict[str, Any]):
        """协调冲突解决"""
        # 简化的冲突解决协调
        request["result"] = {"resolution": "mediated", "outcome": "peaceful"}
        request["status"] = "completed"
        
        logger.log_info(f"冲突解决协调完成: {request['id']}")
    
    def _get_director_response(self, director: BaseDirector, 
                             request: Dict[str, Any]) -> Dict[str, Any]:
        """获取导演响应"""
        try:
            if hasattr(director, 'handle_coordination_request'):
                return director.handle_coordination_request(request)
            else:
                return {"status": "no_response", "reason": "method_not_implemented"}
        except Exception as e:
            logger.log_err(f"获取导演响应失败: {e}")
            return {"status": "error", "reason": str(e)}
    
    def _make_coordination_decision(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """做出协调决策"""
        # 简化的决策逻辑
        responses = request["responses"]
        
        # 统计响应
        positive_responses = sum(1 for r in responses.values() if r.get("status") == "approved")
        total_responses = len(responses)
        
        if positive_responses >= total_responses * 0.6:  # 60%同意
            return {"decision": "approved", "consensus": "majority"}
        else:
            return {"decision": "rejected", "consensus": "insufficient"}
    
    def _sync_shared_context(self):
        """同步共享上下文"""
        current_time = time.time()
        last_sync = self.db.communication_state["last_sync_time"]
        
        # 每5分钟同步一次
        if current_time - last_sync >= 300:
            self._perform_context_sync()
            self.db.communication_state["last_sync_time"] = current_time
    
    def _perform_context_sync(self):
        """执行上下文同步"""
        # 清理过期的上下文数据（超过1小时）
        current_time = time.time()
        shared_context = self.db.communication_state["shared_context"]
        
        for layer_key, layer_context in shared_context.items():
            expired_keys = []
            for context_key, context_data in layer_context.items():
                if current_time - context_data["timestamp"] > 3600:  # 1小时
                    expired_keys.append(context_key)
            
            for key in expired_keys:
                del layer_context[key]
        
        logger.log_info("共享上下文同步完成")
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        # 计算平均响应时间
        queue = self.db.communication_state["message_queue"]
        delivered_messages = [m for m in queue if m["status"] == "delivered" and "delivered_time" in m]
        
        if delivered_messages:
            total_response_time = sum(
                m["delivered_time"] - m["timestamp"] for m in delivered_messages
            )
            self.db.performance_metrics["average_response_time"] = total_response_time / len(delivered_messages)
        
        # 计算同步频率
        self.db.performance_metrics["sync_frequency"] = 1.0 / 300  # 每5分钟一次
    
    def _subscribe_to_director_events(self):
        """订阅导演事件"""
        # 订阅各种导演事件
        event_types = [
            "director_decision_made",
            "director_action_executed",
            "director_performance_update"
        ]
        
        for event_type in event_types:
            subscribe_to_event(event_type, self._handle_director_event)
    
    def _handle_director_event(self, event: BaseEvent):
        """处理导演事件"""
        try:
            event_type = event.event_type
            source_id = event.source_id
            data = event.data
            
            logger.log_info(f"收到导演事件: {event_type} 来自 {source_id}")
            
            # 根据事件类型进行处理
            if event_type == "director_decision_made":
                self._handle_decision_event(source_id, data)
            elif event_type == "director_action_executed":
                self._handle_action_event(source_id, data)
            elif event_type == "director_performance_update":
                self._handle_performance_event(source_id, data)
                
        except Exception as e:
            logger.log_err(f"处理导演事件失败: {e}")
    
    def _handle_decision_event(self, source_id: str, data: Dict[str, Any]):
        """处理决策事件"""
        # 记录决策到共享上下文
        decision_info = {
            "source": source_id,
            "decision_type": data.get("decision_type"),
            "timestamp": time.time()
        }
        
        self.update_shared_context(
            self._get_layer_by_source_id(source_id),
            "last_decision",
            decision_info
        )
    
    def _handle_action_event(self, source_id: str, data: Dict[str, Any]):
        """处理行动事件"""
        # 记录行动到共享上下文
        action_info = {
            "source": source_id,
            "action_type": data.get("action_type"),
            "timestamp": time.time()
        }
        
        self.update_shared_context(
            self._get_layer_by_source_id(source_id),
            "last_action",
            action_info
        )
    
    def _handle_performance_event(self, source_id: str, data: Dict[str, Any]):
        """处理性能事件"""
        # 更新性能指标
        pass
    
    def _get_layer_by_source_id(self, source_id: str) -> DirectorLayer:
        """根据源ID获取导演层级"""
        if "tiandao" in source_id.lower():
            return DirectorLayer.TIANDAO
        elif "diling" in source_id.lower():
            return DirectorLayer.DILING
        elif "qiling" in source_id.lower():
            return DirectorLayer.QILING
        else:
            return DirectorLayer.QILING  # 默认
    
    def _publish_director_registration_event(self, layer: DirectorLayer, director: BaseDirector):
        """发布导演注册事件"""
        event = BaseEvent(
            event_type="director_registered",
            source_id=self.key,
            data={
                "layer": layer.value,
                "director_key": director.key,
                "timestamp": time.time()
            },
            priority=EventPriority.NORMAL
        )
        
        publish_event(event)
    
    def _notify_coordination_request(self, request: Dict[str, Any]):
        """通知协调请求"""
        event = BaseEvent(
            event_type="coordination_request_created",
            source_id=self.key,
            data={
                "request_id": request["id"],
                "requesting_layer": request["requesting_layer"],
                "coordination_type": request["coordination_type"],
                "timestamp": time.time()
            },
            priority=EventPriority.HIGH
        )
        
        publish_event(event)


def get_director_integration() -> Optional[DirectorIntegration]:
    """获取导演集成系统实例"""
    from evennia import search_script
    
    scripts = search_script("director_integration")
    return scripts[0] if scripts else None
