"""
仙侠社交查询管理系统

基于TagProperty的高性能社交关系查询系统，包括：
- 门派成员查询
- 师徒关系查询
- 社交关系查询
- 复合条件查询
- 查询结果缓存

完全基于Evennia原生组件和现有TagProperty系统。
"""

import time
from typing import Dict, Any, List, Optional, Tuple, Union
from evennia.utils import logger
from evennia import search


class SocialQueryManager:
    """
    社交查询管理器
    
    提供高性能的社交关系查询功能：
    - 基于TagProperty的快速查询
    - 复合条件查询支持
    - 查询结果缓存优化
    - 统一的查询接口
    """
    
    # 查询缓存
    _query_cache = {}
    _cache_timeout = 300  # 5分钟缓存超时
    
    @staticmethod
    def find_sect_members(sect_name: str, include_details: bool = False) -> List[Union[object, Dict[str, Any]]]:
        """
        查找门派成员
        
        Args:
            sect_name: 门派名称
            include_details: 是否包含详细信息
            
        Returns:
            List: 门派成员列表
        """
        try:
            # 检查缓存
            cache_key = f"sect_members_{sect_name}_{include_details}"
            cached_result = SocialQueryManager._get_cached_result(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 使用TagProperty查询门派成员
            from systems.tag_property_system import TagPropertyQueryManager
            members = TagPropertyQueryManager.find_characters_by_sect(sect_name)
            
            if include_details:
                detailed_members = []
                for member in members:
                    member_info = SocialQueryManager._get_character_social_info(member)
                    detailed_members.append(member_info)
                result = detailed_members
            else:
                result = members
            
            # 缓存结果
            SocialQueryManager._cache_result(cache_key, result)
            return result
            
        except Exception as e:
            logger.log_err(f"查找门派成员失败: {e}")
            return []
    
    @staticmethod
    def find_masters_in_sect(sect_name: str) -> List[Dict[str, Any]]:
        """查找门派中的师父"""
        try:
            sect_members = SocialQueryManager.find_sect_members(sect_name)
            masters = []
            
            for member in sect_members:
                if member.tags.has("has_disciples", category="social_status"):
                    from systems.mentorship_manager import MentorshipManager
                    disciples = MentorshipManager.get_disciples(member)
                    
                    master_info = {
                        "master": SocialQueryManager._get_character_social_info(member),
                        "disciple_count": len(disciples),
                        "disciples": [SocialQueryManager._get_character_social_info(d) for d in disciples]
                    }
                    masters.append(master_info)
            
            return masters
            
        except Exception as e:
            logger.log_err(f"查找门派师父失败: {e}")
            return []
    
    @staticmethod
    def find_disciples_in_sect(sect_name: str) -> List[Dict[str, Any]]:
        """查找门派中的弟子"""
        try:
            sect_members = SocialQueryManager.find_sect_members(sect_name)
            disciples = []
            
            for member in sect_members:
                if member.tags.has("has_master", category="social_status"):
                    from systems.mentorship_manager import MentorshipManager
                    master = MentorshipManager.get_master(member)
                    
                    if master:
                        disciple_info = {
                            "disciple": SocialQueryManager._get_character_social_info(member),
                            "master": SocialQueryManager._get_character_social_info(master),
                            "mentorship": MentorshipManager.get_mentorship_info(master, member)
                        }
                        disciples.append(disciple_info)
            
            return disciples
            
        except Exception as e:
            logger.log_err(f"查找门派弟子失败: {e}")
            return []
    
    @staticmethod
    def find_character_relationships(character, relationship_type: str = None) -> List[Dict[str, Any]]:
        """
        查找角色的社交关系
        
        Args:
            character: 角色对象
            relationship_type: 关系类型过滤（可选）
            
        Returns:
            List: 关系列表
        """
        try:
            from systems.social_relationship_manager import SocialRelationshipManager
            
            if relationship_type:
                from systems.social_relationship_manager import RelationshipType
                rel_type = RelationshipType(relationship_type)
                relationships = SocialRelationshipManager.get_relationships_by_type(character, rel_type)
            else:
                all_relationships = SocialRelationshipManager.get_all_relationships(character)
                relationships = list(all_relationships.values())
            
            # 添加目标角色的详细信息
            detailed_relationships = []
            for rel in relationships:
                target_id = rel.get("target_id")
                if target_id:
                    target_chars = search.search_object(f"#{target_id}")
                    if target_chars:
                        target = target_chars[0]
                        rel_info = {
                            "relationship": rel,
                            "target": SocialQueryManager._get_character_social_info(target)
                        }
                        detailed_relationships.append(rel_info)
            
            return detailed_relationships
            
        except Exception as e:
            logger.log_err(f"查找角色关系失败: {e}")
            return []
    
    @staticmethod
    def find_mutual_friends(character1, character2) -> List[Dict[str, Any]]:
        """查找两个角色的共同好友"""
        try:
            friends1 = SocialQueryManager.find_character_relationships(character1, "friend")
            friends2 = SocialQueryManager.find_character_relationships(character2, "friend")
            
            friends1_ids = {rel["target"]["id"] for rel in friends1}
            friends2_ids = {rel["target"]["id"] for rel in friends2}
            
            mutual_ids = friends1_ids.intersection(friends2_ids)
            
            mutual_friends = []
            for friend_rel in friends1:
                if friend_rel["target"]["id"] in mutual_ids:
                    mutual_friends.append(friend_rel["target"])
            
            return mutual_friends
            
        except Exception as e:
            logger.log_err(f"查找共同好友失败: {e}")
            return []
    
    @staticmethod
    def find_characters_by_reputation_range(min_reputation: int, max_reputation: int) -> List[Dict[str, Any]]:
        """根据声望范围查找角色"""
        try:
            # 这个查询可能比较耗时，需要遍历所有角色
            cache_key = f"reputation_range_{min_reputation}_{max_reputation}"
            cached_result = SocialQueryManager._get_cached_result(cache_key)
            if cached_result is not None:
                return cached_result
            
            all_characters = search.search_object_tag("character", category="typeclass")
            matching_characters = []
            
            for char in all_characters:
                if hasattr(char, 'attributes'):
                    from systems.social_relationship_manager import ReputationManager
                    reputation = ReputationManager.get_reputation(char)
                    
                    if min_reputation <= reputation <= max_reputation:
                        char_info = SocialQueryManager._get_character_social_info(char)
                        char_info["reputation"] = reputation
                        char_info["reputation_level"] = ReputationManager.get_reputation_level(reputation)
                        matching_characters.append(char_info)
            
            # 按声望排序
            matching_characters.sort(key=lambda x: x["reputation"], reverse=True)
            
            # 缓存结果
            SocialQueryManager._cache_result(cache_key, matching_characters)
            return matching_characters
            
        except Exception as e:
            logger.log_err(f"根据声望查找角色失败: {e}")
            return []
    
    @staticmethod
    def complex_social_query(filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        复合社交查询
        
        Args:
            filters: 查询过滤条件
                - sect: 门派名称
                - realm: 修为境界
                - has_master: 是否有师父
                - has_disciples: 是否有弟子
                - relationship_with: 与指定角色的关系
                - min_reputation: 最小声望
                - max_reputation: 最大声望
                
        Returns:
            List: 匹配的角色列表
        """
        try:
            # 生成缓存键
            cache_key = f"complex_query_{hash(str(sorted(filters.items())))}"
            cached_result = SocialQueryManager._get_cached_result(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 开始查询
            candidates = None
            
            # 门派过滤
            if "sect" in filters:
                candidates = SocialQueryManager.find_sect_members(filters["sect"])
            
            # 修为境界过滤
            if "realm" in filters:
                from systems.tag_property_system import TagPropertyQueryManager
                realm_chars = TagPropertyQueryManager.find_characters_by_realm(filters["realm"])
                candidates = SocialQueryManager._intersect_candidates(candidates, realm_chars)
            
            # 如果没有初始候选者，使用所有角色
            if candidates is None:
                candidates = search.search_object_tag("character", category="typeclass")
            
            # 应用其他过滤条件
            filtered_results = []
            for char in candidates:
                if SocialQueryManager._matches_filters(char, filters):
                    char_info = SocialQueryManager._get_character_social_info(char)
                    
                    # 添加额外信息
                    if "min_reputation" in filters or "max_reputation" in filters:
                        from systems.social_relationship_manager import ReputationManager
                        char_info["reputation"] = ReputationManager.get_reputation(char)
                        char_info["reputation_level"] = ReputationManager.get_reputation_level(char_info["reputation"])
                    
                    filtered_results.append(char_info)
            
            # 缓存结果
            SocialQueryManager._cache_result(cache_key, filtered_results)
            return filtered_results
            
        except Exception as e:
            logger.log_err(f"复合社交查询失败: {e}")
            return []
    
    @staticmethod
    def _matches_filters(character, filters: Dict[str, Any]) -> bool:
        """检查角色是否匹配过滤条件"""
        try:
            # 师父过滤
            if "has_master" in filters:
                has_master = character.tags.has("has_master", category="social_status")
                if filters["has_master"] != has_master:
                    return False
            
            # 弟子过滤
            if "has_disciples" in filters:
                has_disciples = character.tags.has("has_disciples", category="social_status")
                if filters["has_disciples"] != has_disciples:
                    return False
            
            # 声望过滤
            if "min_reputation" in filters or "max_reputation" in filters:
                from systems.social_relationship_manager import ReputationManager
                reputation = ReputationManager.get_reputation(character)
                
                if "min_reputation" in filters and reputation < filters["min_reputation"]:
                    return False
                if "max_reputation" in filters and reputation > filters["max_reputation"]:
                    return False
            
            # 关系过滤
            if "relationship_with" in filters:
                target_char = filters["relationship_with"]
                from systems.social_relationship_manager import SocialRelationshipManager
                relationship = SocialRelationshipManager.get_relationship(character, target_char)
                
                if "relationship_type" in filters:
                    if not relationship or relationship.get("type") != filters["relationship_type"]:
                        return False
                else:
                    if not relationship:
                        return False
            
            return True
            
        except Exception as e:
            logger.log_err(f"过滤条件检查失败: {e}")
            return False
    
    @staticmethod
    def _intersect_candidates(candidates1: Optional[List], candidates2: List) -> List:
        """求两个候选列表的交集"""
        if candidates1 is None:
            return candidates2
        
        candidates1_ids = {char.id for char in candidates1}
        return [char for char in candidates2 if char.id in candidates1_ids]
    
    @staticmethod
    def _get_character_social_info(character) -> Dict[str, Any]:
        """获取角色的社交信息摘要"""
        try:
            # 获取师徒信息
            has_master = character.tags.has("has_master", category="social_status")
            has_disciples = character.tags.has("has_disciples", category="social_status")
            
            # 获取门派等级
            sect_rank = "弟子"  # 默认等级
            rank_tags = character.tags.get(category="sect_rank")
            if rank_tags:
                for tag in rank_tags:
                    if tag.startswith("sect_rank_"):
                        sect_rank = tag.replace("sect_rank_", "")
                        break
            
            return {
                "id": character.id,
                "name": character.key,
                "realm": getattr(character, '修为境界', '未知'),
                "sect": getattr(character, '门派归属', '无门派'),
                "sect_rank": sect_rank,
                "profession": getattr(character, '职业类型', '散修'),
                "has_master": has_master,
                "has_disciples": has_disciples,
                "social_status": character.tags.get(category="social_status")
            }
            
        except Exception as e:
            logger.log_err(f"获取角色社交信息失败: {e}")
            return {
                "id": character.id,
                "name": character.key,
                "error": str(e)
            }
    
    @staticmethod
    def _get_cached_result(cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        if cache_key in SocialQueryManager._query_cache:
            cached_data = SocialQueryManager._query_cache[cache_key]
            if time.time() - cached_data["timestamp"] < SocialQueryManager._cache_timeout:
                return cached_data["result"]
            else:
                # 缓存过期，删除
                del SocialQueryManager._query_cache[cache_key]
        return None
    
    @staticmethod
    def _cache_result(cache_key: str, result: Any):
        """缓存查询结果"""
        SocialQueryManager._query_cache[cache_key] = {
            "result": result,
            "timestamp": time.time()
        }
        
        # 清理过期缓存
        SocialQueryManager._cleanup_cache()
    
    @staticmethod
    def _cleanup_cache():
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for key, data in SocialQueryManager._query_cache.items():
            if current_time - data["timestamp"] >= SocialQueryManager._cache_timeout:
                expired_keys.append(key)
        
        for key in expired_keys:
            del SocialQueryManager._query_cache[key]
    
    @staticmethod
    def clear_cache():
        """清空所有缓存"""
        SocialQueryManager._query_cache.clear()
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(SocialQueryManager._query_cache),
            "cache_timeout": SocialQueryManager._cache_timeout,
            "cached_queries": list(SocialQueryManager._query_cache.keys())
        }


# 便利查询函数

def find_sect_elders(sect_name: str) -> List[Dict[str, Any]]:
    """查找门派长老"""
    filters = {
        "sect": sect_name
    }
    members = SocialQueryManager.complex_social_query(filters)
    return [m for m in members if "长老" in m.get("sect_rank", "")]


def find_masterless_disciples(sect_name: str = None) -> List[Dict[str, Any]]:
    """查找没有师父的弟子"""
    filters = {
        "has_master": False
    }
    if sect_name:
        filters["sect"] = sect_name
    
    return SocialQueryManager.complex_social_query(filters)


def find_famous_characters(min_reputation: int = 1000) -> List[Dict[str, Any]]:
    """查找知名角色"""
    return SocialQueryManager.find_characters_by_reputation_range(min_reputation, 10000)


def find_notorious_characters(max_reputation: int = -1000) -> List[Dict[str, Any]]:
    """查找臭名昭著的角色"""
    return SocialQueryManager.find_characters_by_reputation_range(-10000, max_reputation)
