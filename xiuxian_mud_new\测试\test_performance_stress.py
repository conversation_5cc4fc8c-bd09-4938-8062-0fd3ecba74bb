"""
性能压力测试套件

测试系统在高负载下的性能表现：
- TagProperty查询性能压力测试
- 事件系统吞吐量测试
- AI导演决策延迟测试
- 内存使用压力测试
- 并发用户模拟测试
"""

import time
import threading
import unittest
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import (
    XianxiaIntegrationTest, create_test_character, create_test_room, 
    create_test_npc, cleanup_test_objects, measure_operation_performance
)
from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..systems.tag_property_system import TagPropertyQueryManager
from ..systems.query_interfaces import AIDirectorQueryInterface


class TagPropertyPerformanceStressTest(XianxiaIntegrationTest):
    """TagProperty查询性能压力测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建大量测试数据
        self.test_objects = []
        self.query_manager = TagPropertyQueryManager()
        
        # 创建1000个测试对象用于压力测试
        log_info("创建大量测试数据...")
        for i in range(1000):
            if i % 100 == 0:
                log_info(f"已创建 {i} 个测试对象")
            
            obj = create_test_character(f"stress_test_char_{i}")
            
            # 设置多样化的标签
            obj.tags.add(f"level_{i % 10}", category="level")
            obj.tags.add(f"sect_{i % 5}", category="sect")
            obj.tags.add(f"element_{i % 3}", category="element")
            obj.tags.add(f"region_{i % 20}", category="region")
            obj.tags.add(f"status_{i % 7}", category="status")
            
            self.test_objects.append(obj)
        
        log_info("测试数据创建完成")
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_high_volume_query_performance(self):
        """测试大量查询的性能"""
        log_info("开始高容量查询性能测试")
        
        # 定义查询测试用例
        query_cases = [
            ("level", "level_5"),
            ("sect", "sect_2"),
            ("element", "element_1"),
            ("region", "region_10"),
            ("status", "status_3")
        ]
        
        # 执行性能测试
        for category, tag_value in query_cases:
            # 预热查询
            for _ in range(10):
                self.query_manager.find_objects_by_tag(tag_value, category)
            
            # 正式性能测试
            start_time = time.time()
            results = []
            
            for _ in range(100):  # 执行100次查询
                result = self.query_manager.find_objects_by_tag(tag_value, category)
                results.append(result)
            
            end_time = time.time()
            
            # 计算性能指标
            total_time = end_time - start_time
            avg_query_time = total_time / 100 * 1000  # 毫秒
            
            # 验证性能要求
            self.assertLess(avg_query_time, 10, 
                          f"查询 {category}:{tag_value} 平均耗时过长: {avg_query_time:.2f}ms")
            
            # 验证结果正确性
            expected_count = len([obj for obj in self.test_objects 
                                if obj.tags.get(category=category) == tag_value])
            actual_count = len(results[0]) if results[0] else 0
            
            self.assertEqual(actual_count, expected_count, 
                           f"查询结果数量不正确: 期望{expected_count}, 实际{actual_count}")
            
            log_info(f"查询 {category}:{tag_value} - 平均耗时: {avg_query_time:.2f}ms, "
                    f"结果数量: {actual_count}")
        
        log_info("高容量查询性能测试完成")
    
    def test_concurrent_query_performance(self):
        """测试并发查询性能"""
        log_info("开始并发查询性能测试")
        
        def concurrent_query_worker(worker_id, query_count):
            """并发查询工作线程"""
            results = []
            errors = 0
            
            for i in range(query_count):
                try:
                    # 随机查询
                    category = ["level", "sect", "element"][i % 3]
                    tag_value = f"{category}_{i % 5}"
                    
                    start_time = time.time()
                    result = self.query_manager.find_objects_by_tag(tag_value, category)
                    end_time = time.time()
                    
                    query_time = (end_time - start_time) * 1000
                    results.append(query_time)
                    
                    if query_time > 50:  # 超过50ms认为性能问题
                        errors += 1
                        
                except Exception as e:
                    errors += 1
                    log_err(f"并发查询错误: {e}")
            
            return {
                "worker_id": worker_id,
                "total_queries": query_count,
                "avg_time": sum(results) / len(results) if results else 0,
                "max_time": max(results) if results else 0,
                "errors": errors
            }
        
        # 启动并发查询
        num_workers = 10
        queries_per_worker = 50
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = []
            
            start_time = time.time()
            
            for worker_id in range(num_workers):
                future = executor.submit(concurrent_query_worker, worker_id, queries_per_worker)
                futures.append(future)
            
            # 收集结果
            all_results = []
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=30)
                    all_results.append(result)
                except Exception as e:
                    log_err(f"并发查询工作线程失败: {e}")
            
            end_time = time.time()
        
        # 分析并发性能
        total_time = end_time - start_time
        total_queries = sum(r["total_queries"] for r in all_results)
        total_errors = sum(r["errors"] for r in all_results)
        
        avg_throughput = total_queries / total_time  # 查询/秒
        error_rate = total_errors / total_queries * 100 if total_queries > 0 else 0
        
        # 验证并发性能
        self.assertGreater(avg_throughput, 50, f"并发查询吞吐量过低: {avg_throughput:.2f} 查询/秒")
        self.assertLess(error_rate, 5, f"并发查询错误率过高: {error_rate:.2f}%")
        
        log_info(f"并发查询性能: {avg_throughput:.2f} 查询/秒, 错误率: {error_rate:.2f}%")
        log_info("并发查询性能测试完成")
    
    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        log_info("开始内存使用压力测试")
        
        # 记录初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量查询操作
        query_iterations = 1000
        memory_samples = []
        
        for i in range(query_iterations):
            # 执行查询
            category = ["level", "sect", "element", "region", "status"][i % 5]
            tag_value = f"{category}_{i % 10}"
            
            result = self.query_manager.find_objects_by_tag(tag_value, category)
            
            # 每100次查询记录一次内存使用
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                
                if i % 200 == 0:
                    log_info(f"查询 {i}/{query_iterations}, 内存使用: {current_memory:.2f}MB")
        
        # 强制垃圾回收
        gc.collect()
        time.sleep(1)
        
        final_memory = process.memory_info().rss / 1024 / 1024
        
        # 分析内存使用
        memory_growth = final_memory - initial_memory
        max_memory = max(memory_samples) if memory_samples else final_memory
        
        # 验证内存使用合理
        self.assertLess(memory_growth, 100, f"内存增长过大: {memory_growth:.2f}MB")
        self.assertLess(max_memory - initial_memory, 150, 
                       f"峰值内存使用过高: {max_memory - initial_memory:.2f}MB")
        
        log_info(f"内存使用测试: 初始{initial_memory:.2f}MB, "
                f"最终{final_memory:.2f}MB, 增长{memory_growth:.2f}MB")
        log_info("内存使用压力测试完成")


class EventSystemThroughputTest(XianxiaIntegrationTest):
    """事件系统吞吐量测试"""
    
    def setUp(self):
        super().setUp()
        
        # 初始化事件系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_event_publishing_throughput(self):
        """测试事件发布吞吐量"""
        log_info("开始事件发布吞吐量测试")
        
        event_bus_scripts = search_script("xianxia_event_bus")
        self.assertTrue(event_bus_scripts, "事件总线未找到")
        
        event_bus = event_bus_scripts[0]
        
        # 生成大量测试事件
        test_events = []
        for i in range(1000):
            event = BaseEvent(
                event_type="throughput_test_event",
                event_data={
                    "event_id": i,
                    "test_data": f"test_data_{i}",
                    "timestamp": time.time()
                },
                priority=["LOW", "MEDIUM", "HIGH"][i % 3]
            )
            test_events.append(event)
        
        # 测试发布性能
        start_time = time.time()
        
        for event in test_events:
            event_bus.publish_event(event)
        
        end_time = time.time()
        
        # 计算吞吐量
        total_time = end_time - start_time
        throughput = len(test_events) / total_time  # 事件/秒
        
        # 验证吞吐量要求
        self.assertGreater(throughput, 100, f"事件发布吞吐量过低: {throughput:.2f} 事件/秒")
        
        log_info(f"事件发布吞吐量: {throughput:.2f} 事件/秒")
        log_info("事件发布吞吐量测试完成")
    
    def test_event_processing_latency(self):
        """测试事件处理延迟"""
        log_info("开始事件处理延迟测试")
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            # 测试不同优先级事件的处理延迟
            priorities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
            latency_results = {}
            
            for priority in priorities:
                latencies = []
                
                for i in range(20):  # 每个优先级测试20个事件
                    event = BaseEvent(
                        event_type="latency_test_event",
                        event_data={
                            "test_id": f"{priority}_{i}",
                            "publish_time": time.time()
                        },
                        priority=priority
                    )
                    
                    publish_time = time.time()
                    event_bus.publish_event(event)
                    
                    # 等待处理完成（简化测试，实际应该监控处理完成）
                    time.sleep(0.1)
                    
                    # 估算处理延迟
                    estimated_latency = 0.1 * 1000  # 100ms（简化）
                    latencies.append(estimated_latency)
                
                avg_latency = sum(latencies) / len(latencies)
                latency_results[priority] = avg_latency
                
                # 验证延迟要求
                max_allowed_latency = {"LOW": 1000, "MEDIUM": 500, "HIGH": 200, "CRITICAL": 100}
                self.assertLess(avg_latency, max_allowed_latency[priority],
                              f"{priority}优先级事件延迟过高: {avg_latency:.2f}ms")
            
            log_info("事件处理延迟结果:")
            for priority, latency in latency_results.items():
                log_info(f"  {priority}: {latency:.2f}ms")
        
        log_info("事件处理延迟测试完成")
    
    def test_event_queue_capacity(self):
        """测试事件队列容量"""
        log_info("开始事件队列容量测试")
        
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            # 快速发布大量事件测试队列容量
            burst_size = 5000
            events_published = 0
            
            start_time = time.time()
            
            for i in range(burst_size):
                try:
                    event = BaseEvent(
                        event_type="capacity_test_event",
                        event_data={"burst_id": i},
                        priority="LOW"
                    )
                    
                    event_bus.publish_event(event)
                    events_published += 1
                    
                    # 不等待，快速发布
                    if i % 1000 == 0:
                        log_info(f"已发布 {i} 个事件")
                        
                except Exception as e:
                    log_err(f"事件发布失败: {e}")
                    break
            
            end_time = time.time()
            
            # 验证队列容量
            self.assertGreater(events_published, burst_size * 0.9,
                             f"事件队列容量不足，仅发布了 {events_published}/{burst_size} 个事件")
            
            burst_throughput = events_published / (end_time - start_time)
            log_info(f"突发事件发布: {events_published} 个事件, 吞吐量: {burst_throughput:.2f} 事件/秒")
        
        log_info("事件队列容量测试完成")


class AIDirectorDecisionLatencyTest(XianxiaIntegrationTest):
    """AI导演决策延迟测试"""
    
    def setUp(self):
        super().setUp()
        
        # 初始化AI导演系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(3)
        
        # 获取导演引用
        self.tiandao = search_script("tiandao_director_script")[0] if search_script("tiandao_director_script") else None
        self.diling = search_script("diling_director_script")[0] if search_script("diling_director_script") else None
        self.qiling = search_script("qiling_director_script")[0] if search_script("qiling_director_script") else None
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_ai_director_response_time(self):
        """测试AI导演响应时间"""
        log_info("开始AI导演响应时间测试")
        
        directors = [
            ("tiandao", self.tiandao),
            ("diling", self.diling), 
            ("qiling", self.qiling)
        ]
        
        for director_name, director in directors:
            if not director:
                continue
                
            response_times = []
            
            for i in range(10):  # 每个导演测试10次
                # 创建需要决策的事件
                decision_event = BaseEvent(
                    event_type="decision_required_event",
                    event_data={
                        "decision_type": "test_decision",
                        "urgency": "normal",
                        "context": f"test_context_{i}"
                    },
                    priority="MEDIUM"
                )
                
                # 记录决策开始时间
                start_time = time.time()
                
                # 触发决策（通过事件系统）
                event_bus_scripts = search_script("xianxia_event_bus")
                if event_bus_scripts:
                    event_bus = event_bus_scripts[0]
                    event_bus.publish_event(decision_event)
                
                # 等待决策完成（简化测试）
                time.sleep(0.5)
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # 毫秒
                response_times.append(response_time)
            
            # 分析响应时间
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            # 验证响应时间要求
            max_allowed_time = {"tiandao": 2000, "diling": 1000, "qiling": 500}  # 毫秒
            self.assertLess(avg_response_time, max_allowed_time[director_name],
                          f"{director_name}导演平均响应时间过长: {avg_response_time:.2f}ms")
            
            log_info(f"{director_name}导演响应时间: 平均{avg_response_time:.2f}ms, "
                    f"最大{max_response_time:.2f}ms")
        
        log_info("AI导演响应时间测试完成")
    
    def test_decision_quality_under_pressure(self):
        """测试压力下的决策质量"""
        log_info("开始压力下决策质量测试")
        
        # 创建高压力场景：大量并发决策请求
        decision_requests = []
        for i in range(50):
            request = BaseEvent(
                event_type="high_pressure_decision",
                event_data={
                    "decision_id": i,
                    "complexity": "high",
                    "time_pressure": "urgent"
                },
                priority="HIGH"
            )
            decision_requests.append(request)
        
        # 快速发布所有决策请求
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            
            start_time = time.time()
            
            for request in decision_requests:
                event_bus.publish_event(request)
                time.sleep(0.01)  # 很短间隔
            
            # 等待所有决策完成
            time.sleep(10)
            
            end_time = time.time()
            
            # 验证决策质量（简化测试）
            if hasattr(self.qiling, 'decision_quality_log'):
                quality_log = self.qiling.decision_quality_log
                
                if quality_log:
                    avg_quality = sum(q.get('quality_score', 0) for q in quality_log) / len(quality_log)
                    self.assertGreater(avg_quality, 0.6, f"压力下决策质量下降: {avg_quality:.2f}")
                    
                    log_info(f"压力下决策质量: {avg_quality:.2f}")
            
            total_time = end_time - start_time
            decision_throughput = len(decision_requests) / total_time
            
            log_info(f"高压力决策吞吐量: {decision_throughput:.2f} 决策/秒")
        
        log_info("压力下决策质量测试完成")


class ConcurrentUserSimulationTest(XianxiaIntegrationTest):
    """并发用户模拟测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建模拟用户环境
        self.simulated_users = []
        self.user_rooms = []
        
        # 创建100个模拟用户
        for i in range(100):
            room = create_test_room(f"user_room_{i}")
            user = create_test_character(f"simulated_user_{i}", room)
            
            self.user_rooms.append(room)
            self.simulated_users.append(user)
        
        # 初始化系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(3)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_concurrent_user_activities(self):
        """测试并发用户活动"""
        log_info("开始并发用户活动测试")
        
        def simulate_user_session(user, session_duration=30):
            """模拟单个用户会话"""
            session_start = time.time()
            activities_performed = 0
            errors = 0
            
            while time.time() - session_start < session_duration:
                try:
                    # 随机选择活动
                    activity_type = ["cultivation", "social", "exploration", "combat"][
                        int(time.time() * 1000) % 4
                    ]
                    
                    # 执行活动
                    activity_event = BaseEvent(
                        event_type=f"user_{activity_type}",
                        event_data={
                            "user_id": user.id,
                            "activity": activity_type,
                            "timestamp": time.time()
                        },
                        priority="MEDIUM"
                    )
                    
                    event_bus_scripts = search_script("xianxia_event_bus")
                    if event_bus_scripts:
                        event_bus = event_bus_scripts[0]
                        event_bus.publish_event(activity_event)
                        activities_performed += 1
                    
                    # 随机间隔
                    time.sleep(0.1 + (int(time.time() * 1000) % 5) * 0.1)
                    
                except Exception as e:
                    errors += 1
                    log_err(f"用户活动错误: {e}")
            
            return {
                "user_id": user.id,
                "activities": activities_performed,
                "errors": errors,
                "session_time": time.time() - session_start
            }
        
        # 启动并发用户会话
        num_concurrent_users = 50  # 同时50个用户
        session_duration = 20  # 20秒会话
        
        with ThreadPoolExecutor(max_workers=num_concurrent_users) as executor:
            futures = []
            
            test_start_time = time.time()
            
            for i in range(num_concurrent_users):
                user = self.simulated_users[i]
                future = executor.submit(simulate_user_session, user, session_duration)
                futures.append(future)
            
            # 收集结果
            session_results = []
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=session_duration + 10)
                    session_results.append(result)
                except Exception as e:
                    log_err(f"用户会话失败: {e}")
            
            test_end_time = time.time()
        
        # 分析并发用户测试结果
        total_activities = sum(r["activities"] for r in session_results)
        total_errors = sum(r["errors"] for r in session_results)
        total_test_time = test_end_time - test_start_time
        
        activity_throughput = total_activities / total_test_time  # 活动/秒
        error_rate = total_errors / total_activities * 100 if total_activities > 0 else 0
        
        # 验证并发用户性能
        self.assertGreater(activity_throughput, 20, f"并发用户活动吞吐量过低: {activity_throughput:.2f} 活动/秒")
        self.assertLess(error_rate, 10, f"并发用户错误率过高: {error_rate:.2f}%")
        
        # 验证系统资源使用
        self.assert_performance_within_limits(cpu_limit=85, memory_limit=80)
        
        log_info(f"并发用户测试结果: {num_concurrent_users}个用户, "
                f"总活动{total_activities}个, 吞吐量{activity_throughput:.2f}活动/秒, "
                f"错误率{error_rate:.2f}%")
        
        log_info("并发用户活动测试完成")


if __name__ == '__main__':
    # 运行性能压力测试
    unittest.main(verbosity=2)
