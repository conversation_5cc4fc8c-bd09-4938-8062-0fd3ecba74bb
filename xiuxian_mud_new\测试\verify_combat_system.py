"""
仙侠战斗系统验证脚本

验证战斗系统的完整性和正确性，包括：
- 系统组件完整性检查
- 配置文件验证
- 依赖关系检查
- 性能基准测试
- 功能完整性验证
"""

import os
import sys
import time
import importlib
import traceback
from typing import Dict, List, Tuple, Any

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)


class CombatSystemVerifier:
    """战斗系统验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.results = {
            "passed": 0,
            "failed": 0,
            "warnings": 0,
            "details": []
        }
        self.start_time = time.time()
    
    def log_result(self, test_name: str, status: str, message: str = "", details: str = ""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "details": details,
            "timestamp": time.time()
        }
        
        self.results["details"].append(result)
        
        if status == "PASS":
            self.results["passed"] += 1
            print(f"✓ {test_name}: {message}")
        elif status == "FAIL":
            self.results["failed"] += 1
            print(f"✗ {test_name}: {message}")
            if details:
                print(f"  详情: {details}")
        elif status == "WARN":
            self.results["warnings"] += 1
            print(f"⚠ {test_name}: {message}")
    
    def verify_file_structure(self) -> bool:
        """验证文件结构完整性"""
        print("\n=== 文件结构验证 ===")
        
        required_files = [
            "typeclasses/combat_characters.py",
            "systems/wuxing_calculator.py",
            "systems/xiuxian_skill_system.py",
            "systems/xiuxian_combat_handler.py",
            "systems/combat_event_publisher.py",
            "systems/realm_calculator.py",
            "systems/xiuxian_combat_menus.py",
            "commands/xiuxian_combat_commands.py",
            "测试/test_xiuxian_combat_system.py"
        ]
        
        all_exist = True
        
        for file_path in required_files:
            full_path = os.path.join(project_root, file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                self.log_result(
                    f"文件存在检查: {file_path}",
                    "PASS",
                    f"文件存在 ({file_size} bytes)"
                )
            else:
                self.log_result(
                    f"文件存在检查: {file_path}",
                    "FAIL",
                    "文件不存在"
                )
                all_exist = False
        
        return all_exist
    
    def verify_imports(self) -> bool:
        """验证模块导入"""
        print("\n=== 模块导入验证 ===")
        
        modules_to_test = [
            ("systems.wuxing_calculator", "WuxingCalculator"),
            ("systems.xiuxian_skill_system", "XianxiaSkillSystem"),
            ("systems.realm_calculator", "RealmCalculator"),
            ("systems.combat_event_publisher", "CombatEventPublisher"),
            ("systems.xiuxian_combat_menus", "start_xiuxian_combat_menu"),
            ("typeclasses.combat_characters", "XianxiaCombatCharacter")
        ]
        
        all_imported = True
        
        for module_name, class_name in modules_to_test:
            try:
                module = importlib.import_module(module_name)
                if hasattr(module, class_name):
                    self.log_result(
                        f"模块导入: {module_name}.{class_name}",
                        "PASS",
                        "导入成功"
                    )
                else:
                    self.log_result(
                        f"模块导入: {module_name}.{class_name}",
                        "FAIL",
                        f"类/函数 {class_name} 不存在"
                    )
                    all_imported = False
            except Exception as e:
                self.log_result(
                    f"模块导入: {module_name}",
                    "FAIL",
                    "导入失败",
                    str(e)
                )
                all_imported = False
        
        return all_imported
    
    def verify_wuxing_system(self) -> bool:
        """验证五行系统"""
        print("\n=== 五行系统验证 ===")
        
        try:
            from systems.wuxing_calculator import WuxingCalculator
            
            calc = WuxingCalculator()
            
            # 测试基础相克关系
            test_cases = [
                ("金", "木", ">", "金克木"),
                ("木", "土", ">", "木克土"),
                ("土", "水", ">", "土克水"),
                ("水", "火", ">", "水克火"),
                ("火", "金", ">", "火克金"),
                ("金", "金", "=", "同元素平衡")
            ]
            
            all_correct = True
            
            for attacker, defender, expected, description in test_cases:
                bonus = calc.calculate_elemental_bonus(attacker, defender)
                
                if expected == ">" and bonus > 1.0:
                    self.log_result(f"五行相克: {description}", "PASS", f"加成: {bonus:.2f}")
                elif expected == "=" and bonus == 1.0:
                    self.log_result(f"五行平衡: {description}", "PASS", f"无加成: {bonus:.2f}")
                else:
                    self.log_result(f"五行关系: {description}", "FAIL", f"期望{expected}1.0，实际{bonus:.2f}")
                    all_correct = False
            
            # 测试缓存功能
            start_time = time.time()
            for _ in range(1000):
                calc.calculate_elemental_bonus("金", "木")
            cache_time = time.time() - start_time
            
            self.log_result(
                "五行计算性能",
                "PASS" if cache_time < 0.1 else "WARN",
                f"1000次计算耗时: {cache_time:.4f}s"
            )
            
            return all_correct
            
        except Exception as e:
            self.log_result("五行系统验证", "FAIL", "系统错误", str(e))
            return False
    
    def verify_realm_system(self) -> bool:
        """验证境界系统"""
        print("\n=== 境界系统验证 ===")
        
        try:
            from systems.realm_calculator import RealmCalculator
            
            calc = RealmCalculator()
            
            # 测试境界等级转换
            realm_tests = [
                ("练气", 0),
                ("筑基", 1),
                ("金丹", 2),
                ("仙人", 9)
            ]
            
            all_correct = True
            
            for realm_name, expected_level in realm_tests:
                actual_level = calc.get_realm_level(realm_name)
                if actual_level == expected_level:
                    self.log_result(f"境界等级: {realm_name}", "PASS", f"等级: {actual_level}")
                else:
                    self.log_result(f"境界等级: {realm_name}", "FAIL", f"期望{expected_level}，实际{actual_level}")
                    all_correct = False
            
            # 测试境界差异计算
            diff_result = calc.calculate_realm_difference("金丹", "练气")
            if diff_result["level_difference"] == 2 and diff_result["combat_bonus"] > 1.0:
                self.log_result("境界差异计算", "PASS", f"差异: {diff_result['level_difference']}, 加成: {diff_result['combat_bonus']:.2f}")
            else:
                self.log_result("境界差异计算", "FAIL", "计算结果不正确")
                all_correct = False
            
            # 测试突破进度
            progress_result = calc.calculate_breakthrough_progress(500, "练气")
            if progress_result["progress"] == 0.5:
                self.log_result("突破进度计算", "PASS", f"进度: {progress_result['progress']*100:.1f}%")
            else:
                self.log_result("突破进度计算", "FAIL", f"期望50%，实际{progress_result['progress']*100:.1f}%")
                all_correct = False
            
            return all_correct
            
        except Exception as e:
            self.log_result("境界系统验证", "FAIL", "系统错误", str(e))
            return False
    
    def verify_skill_system(self) -> bool:
        """验证技能系统"""
        print("\n=== 技能系统验证 ===")
        
        try:
            from systems.xiuxian_skill_system import XianxiaSkillSystem
            from unittest.mock import Mock
            
            skill_system = XianxiaSkillSystem()
            
            # 创建模拟角色
            mock_character = Mock()
            mock_character.key = "测试角色"
            mock_character.修为境界 = "练气"
            mock_character.五行属性 = "金"
            mock_character.get_realm_level = Mock(return_value=0)
            mock_character.attributes = Mock()
            mock_character.attributes.get = Mock(return_value={})
            mock_character.attributes.add = Mock()
            
            # 测试技能数据加载
            skill_data = skill_system.load_skill_data(mock_character)
            if "基础剑法" in skill_data:
                self.log_result("技能数据加载", "PASS", f"加载了{len(skill_data)}个技能")
            else:
                self.log_result("技能数据加载", "FAIL", "未找到默认技能")
                return False
            
            # 测试技能学习
            mock_character.get_realm_level.return_value = 1
            learn_result = skill_system.learn_skill(mock_character, "御剑术")
            if learn_result.get("success", False):
                self.log_result("技能学习", "PASS", "成功学习御剑术")
            else:
                self.log_result("技能学习", "FAIL", learn_result.get("message", "未知错误"))
            
            # 测试冷却检查
            import time
            skill_data_with_cooldown = {
                "基础剑法": {
                    "last_used": time.time() - 10,
                    "template": {"cooldown": 5}
                }
            }
            mock_character.attributes.get.return_value = skill_data_with_cooldown
            
            cooldown_result = skill_system.check_skill_cooldown(mock_character, "基础剑法")
            if cooldown_result["ready"]:
                self.log_result("技能冷却检查", "PASS", "冷却时间正确计算")
            else:
                self.log_result("技能冷却检查", "FAIL", "冷却时间计算错误")
            
            return True
            
        except Exception as e:
            self.log_result("技能系统验证", "FAIL", "系统错误", str(e))
            return False
    
    def verify_event_system(self) -> bool:
        """验证事件系统"""
        print("\n=== 事件系统验证 ===")
        
        try:
            from systems.combat_event_publisher import CombatEventPublisher
            from unittest.mock import Mock, patch
            
            publisher = CombatEventPublisher()
            
            # 创建模拟对象
            mock_combatant = Mock()
            mock_combatant.id = 1
            mock_combatant.key = "测试角色"
            mock_combatant.修为境界 = "练气"
            mock_combatant.五行属性 = "金"
            mock_combatant.职业类型 = "剑修"
            mock_combatant.hp = 100
            mock_combatant.max_hp = 100
            mock_combatant.db = Mock()
            mock_combatant.db.spiritual_power = 100
            
            # 测试事件发布（模拟）
            with patch('systems.combat_event_publisher.publish_event') as mock_publish:
                mock_publish.return_value = True
                
                # 测试战斗开始事件
                result = publisher.publish_combat_start([mock_combatant])
                if result:
                    self.log_result("战斗开始事件", "PASS", "事件发布成功")
                else:
                    self.log_result("战斗开始事件", "FAIL", "事件发布失败")
                
                # 测试五行相克事件
                result = publisher.publish_wuxing_interaction(
                    mock_combatant, mock_combatant, "火", "金", 1.3, "火"
                )
                if result:
                    self.log_result("五行相克事件", "PASS", "事件发布成功")
                else:
                    self.log_result("五行相克事件", "FAIL", "事件发布失败")
            
            return True
            
        except Exception as e:
            self.log_result("事件系统验证", "FAIL", "系统错误", str(e))
            return False
    
    def verify_menu_system(self) -> bool:
        """验证菜单系统"""
        print("\n=== 菜单系统验证 ===")
        
        try:
            from systems.xiuxian_combat_menus import XIUXIAN_COMBAT_MENU_NODES
            
            # 检查菜单节点完整性
            required_nodes = [
                "xiuxian_combat_menu_start",
                "select_attack_target",
                "select_skill",
                "execute_attack",
                "execute_skill"
            ]
            
            all_nodes_exist = True
            
            for node_name in required_nodes:
                if node_name in XIUXIAN_COMBAT_MENU_NODES:
                    self.log_result(f"菜单节点: {node_name}", "PASS", "节点存在")
                else:
                    self.log_result(f"菜单节点: {node_name}", "FAIL", "节点缺失")
                    all_nodes_exist = False
            
            # 测试菜单函数调用（基础检查）
            from unittest.mock import Mock
            
            mock_caller = Mock()
            mock_caller.ndb = Mock()
            mock_caller.ndb.combat_handler = None
            
            try:
                # 测试主菜单函数
                main_menu_func = XIUXIAN_COMBAT_MENU_NODES["xiuxian_combat_menu_start"]
                text, options = main_menu_func(mock_caller, "")
                
                if text and "不在战斗中" in text:
                    self.log_result("菜单函数调用", "PASS", "主菜单函数正常")
                else:
                    self.log_result("菜单函数调用", "FAIL", "主菜单函数异常")
                    all_nodes_exist = False
                    
            except Exception as e:
                self.log_result("菜单函数调用", "FAIL", "函数调用错误", str(e))
                all_nodes_exist = False
            
            return all_nodes_exist
            
        except Exception as e:
            self.log_result("菜单系统验证", "FAIL", "系统错误", str(e))
            return False
    
    def verify_integration(self) -> bool:
        """验证系统集成"""
        print("\n=== 系统集成验证 ===")
        
        try:
            from systems.wuxing_calculator import get_wuxing_calculator
            from systems.xiuxian_skill_system import get_skill_system
            from systems.realm_calculator import get_realm_calculator
            
            # 测试全局实例获取
            wuxing_calc = get_wuxing_calculator()
            skill_system = get_skill_system()
            realm_calc = get_realm_calculator()
            
            if wuxing_calc and skill_system and realm_calc:
                self.log_result("全局实例获取", "PASS", "所有系统实例正常")
            else:
                self.log_result("全局实例获取", "FAIL", "部分系统实例获取失败")
                return False
            
            # 测试系统间协作
            # 模拟一个完整的技能使用流程
            from unittest.mock import Mock
            
            # 创建模拟角色
            character = Mock()
            character.修为境界 = "筑基"
            character.五行属性 = "火"
            character.get_realm_level = Mock(return_value=1)
            character.get_realm_combat_bonus = Mock(return_value=1.3)
            
            target = Mock()
            target.五行属性 = "金"  # 火克金
            
            # 测试五行相克计算
            wuxing_bonus = wuxing_calc.calculate_elemental_bonus("火", "金")
            
            # 测试境界加成计算
            realm_diff = realm_calc.calculate_realm_difference("筑基", "练气")
            
            if wuxing_bonus > 1.0 and realm_diff["combat_bonus"] > 1.0:
                self.log_result("系统协作测试", "PASS", f"五行加成: {wuxing_bonus:.2f}, 境界加成: {realm_diff['combat_bonus']:.2f}")
            else:
                self.log_result("系统协作测试", "FAIL", "系统协作异常")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("系统集成验证", "FAIL", "集成错误", str(e))
            return False
    
    def run_performance_tests(self) -> bool:
        """运行性能测试"""
        print("\n=== 性能测试 ===")
        
        try:
            from systems.wuxing_calculator import get_wuxing_calculator
            from systems.realm_calculator import get_realm_calculator
            
            wuxing_calc = get_wuxing_calculator()
            realm_calc = get_realm_calculator()
            
            # 五行计算性能测试
            start_time = time.time()
            for _ in range(10000):
                wuxing_calc.calculate_elemental_bonus("金", "木")
            wuxing_time = time.time() - start_time
            
            if wuxing_time < 1.0:
                self.log_result("五行计算性能", "PASS", f"10000次计算: {wuxing_time:.4f}s")
            else:
                self.log_result("五行计算性能", "WARN", f"性能较慢: {wuxing_time:.4f}s")
            
            # 境界计算性能测试
            start_time = time.time()
            for _ in range(1000):
                realm_calc.calculate_realm_difference("金丹", "练气")
            realm_time = time.time() - start_time
            
            if realm_time < 1.0:
                self.log_result("境界计算性能", "PASS", f"1000次计算: {realm_time:.4f}s")
            else:
                self.log_result("境界计算性能", "WARN", f"性能较慢: {realm_time:.4f}s")
            
            return True
            
        except Exception as e:
            self.log_result("性能测试", "FAIL", "测试错误", str(e))
            return False
    
    def generate_report(self) -> str:
        """生成验证报告"""
        total_time = time.time() - self.start_time
        
        report = f"""
=== 仙侠战斗系统验证报告 ===

验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {total_time:.2f}秒

测试结果统计:
✓ 通过: {self.results['passed']}
✗ 失败: {self.results['failed']}
⚠ 警告: {self.results['warnings']}

总体状态: {'通过' if self.results['failed'] == 0 else '失败'}

详细结果:
"""
        
        for detail in self.results["details"]:
            status_symbol = "✓" if detail["status"] == "PASS" else "✗" if detail["status"] == "FAIL" else "⚠"
            report += f"{status_symbol} {detail['test']}: {detail['message']}\n"
            if detail["details"]:
                report += f"   详情: {detail['details']}\n"
        
        return report
    
    def run_all_verifications(self) -> bool:
        """运行所有验证"""
        print("开始仙侠战斗系统验证...")
        
        verifications = [
            self.verify_file_structure,
            self.verify_imports,
            self.verify_wuxing_system,
            self.verify_realm_system,
            self.verify_skill_system,
            self.verify_event_system,
            self.verify_menu_system,
            self.verify_integration,
            self.run_performance_tests
        ]
        
        all_passed = True
        
        for verification in verifications:
            try:
                result = verification()
                if not result:
                    all_passed = False
            except Exception as e:
                self.log_result(verification.__name__, "FAIL", "验证异常", str(e))
                all_passed = False
        
        return all_passed


def main():
    """主函数"""
    verifier = CombatSystemVerifier()
    
    print("仙侠战斗系统验证脚本")
    print("=" * 50)
    
    success = verifier.run_all_verifications()
    
    print("\n" + "=" * 50)
    print(verifier.generate_report())
    
    if success:
        print("🎉 所有验证通过！战斗系统已准备就绪。")
        return 0
    else:
        print("❌ 验证失败，请检查上述错误并修复。")
        return 1


if __name__ == "__main__":
    exit(main())
