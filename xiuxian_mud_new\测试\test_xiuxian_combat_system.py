"""
仙侠战斗系统测试套件

测试仙侠战斗系统的各个组件，包括：
- XianxiaCombatCharacter
- WuxingCalculator
- XianxiaSkillSystem
- XianxiaCombatHandler
- RealmCalculator
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from systems.wuxing_calculator import WuxingCalculator, WuxingElement
from systems.xiuxian_skill_system import XianxiaSkillSystem, SkillType, SkillGrade
from systems.realm_calculator import RealmCalculator
from systems.combat_event_publisher import CombatEventPublisher


class TestWuxingCalculator(unittest.TestCase):
    """测试五行计算器"""
    
    def setUp(self):
        """测试前准备"""
        self.wuxing_calc = WuxingCalculator()
    
    def test_basic_wuxing_relationships(self):
        """测试基础五行相克关系"""
        # 金克木
        bonus = self.wuxing_calc.calculate_elemental_bonus("金", "木")
        self.assertGreater(bonus, 1.0, "金应该克制木")
        
        # 木克土
        bonus = self.wuxing_calc.calculate_elemental_bonus("木", "土")
        self.assertGreater(bonus, 1.0, "木应该克制土")
        
        # 土克水
        bonus = self.wuxing_calc.calculate_elemental_bonus("土", "水")
        self.assertGreater(bonus, 1.0, "土应该克制水")
        
        # 水克火
        bonus = self.wuxing_calc.calculate_elemental_bonus("水", "火")
        self.assertGreater(bonus, 1.0, "水应该克制火")
        
        # 火克金
        bonus = self.wuxing_calc.calculate_elemental_bonus("火", "金")
        self.assertGreater(bonus, 1.0, "火应该克制金")
    
    def test_reverse_wuxing_relationships(self):
        """测试反向五行关系（被克）"""
        # 木被金克
        bonus = self.wuxing_calc.calculate_elemental_bonus("木", "金")
        self.assertLess(bonus, 1.0, "木应该被金克制")
        
        # 土被木克
        bonus = self.wuxing_calc.calculate_elemental_bonus("土", "木")
        self.assertLess(bonus, 1.0, "土应该被木克制")
    
    def test_same_element_balance(self):
        """测试相同五行平衡"""
        for element in ["金", "木", "水", "火", "土"]:
            bonus = self.wuxing_calc.calculate_elemental_bonus(element, element)
            self.assertEqual(bonus, 1.0, f"{element}对{element}应该平衡")
    
    def test_invalid_elements(self):
        """测试无效五行"""
        bonus = self.wuxing_calc.calculate_elemental_bonus("无效", "金")
        self.assertEqual(bonus, 1.0, "无效五行应该返回平衡")
    
    def test_element_info(self):
        """测试五行信息获取"""
        info = self.wuxing_calc.get_element_info("金")
        self.assertIn("克制", info)
        self.assertIn("被克", info)
        self.assertEqual(info["克制"], "木")
        self.assertEqual(info["被克"], "火")
    
    def test_caching_performance(self):
        """测试缓存性能"""
        # 第一次计算
        start_time = time.time()
        bonus1 = self.wuxing_calc.calculate_elemental_bonus("金", "木")
        first_time = time.time() - start_time
        
        # 第二次计算（应该使用缓存）
        start_time = time.time()
        bonus2 = self.wuxing_calc.calculate_elemental_bonus("金", "木")
        second_time = time.time() - start_time
        
        self.assertEqual(bonus1, bonus2, "缓存结果应该一致")
        # 注意：在测试环境中时间差可能很小，这里只是验证功能


class TestXianxiaSkillSystem(unittest.TestCase):
    """测试仙侠技能系统"""
    
    def setUp(self):
        """测试前准备"""
        self.skill_system = XianxiaSkillSystem()
        self.mock_character = Mock()
        self.mock_character.attributes = Mock()
        self.mock_character.key = "测试角色"
        self.mock_character.修为境界 = "练气"
        self.mock_character.五行属性 = "金"
    
    def test_skill_initialization(self):
        """测试技能初始化"""
        # 模拟没有技能数据的角色
        self.mock_character.attributes.get.return_value = {}
        
        skill_data = self.skill_system.load_skill_data(self.mock_character)
        
        # 应该初始化默认技能
        self.assertIn("基础剑法", skill_data)
        self.assertIn("灵力护体", skill_data)
    
    def test_skill_learning(self):
        """测试技能学习"""
        # 设置角色属性
        self.mock_character.get_realm_level = Mock(return_value=2)
        self.mock_character.attributes.get.return_value = {}
        self.mock_character.attributes.add = Mock()
        
        # 尝试学习御剑术
        result = self.skill_system.learn_skill(self.mock_character, "御剑术")
        
        self.assertTrue(result["success"], "应该能够学习御剑术")
    
    def test_skill_requirements_check(self):
        """测试技能需求检查"""
        skill_template = {
            "requirements": {
                "realm_level": 5,
                "element_affinity": "火"
            }
        }
        
        # 境界不足
        self.mock_character.get_realm_level = Mock(return_value=2)
        result = self.skill_system._check_skill_requirements(self.mock_character, skill_template)
        self.assertFalse(result["can_learn"], "境界不足应该无法学习")
        
        # 五行不匹配
        self.mock_character.get_realm_level = Mock(return_value=6)
        result = self.skill_system._check_skill_requirements(self.mock_character, skill_template)
        self.assertFalse(result["can_learn"], "五行不匹配应该无法学习")
    
    def test_skill_damage_calculation(self):
        """测试技能伤害计算"""
        skill_data = {
            "level": 3,
            "template": {
                "base_damage": 30,
                "element": "火",
                "level_scaling": {
                    "damage": 5
                }
            }
        }
        
        # 模拟施法者
        caster = Mock()
        caster.get_realm_combat_bonus = Mock(return_value=1.2)
        
        # 模拟目标
        target = Mock()
        target.五行属性 = "金"  # 火克金
        
        result = self.skill_system.calculate_skill_damage(skill_data, caster, target)
        
        self.assertGreater(result["final_damage"], result["base_damage"], "应该有五行相克加成")
    
    def test_skill_cooldown(self):
        """测试技能冷却"""
        import time
        
        # 模拟技能数据
        skill_data = {
            "基础剑法": {
                "last_used": time.time() - 5,  # 5秒前使用
                "template": {
                    "cooldown": 3  # 3秒冷却
                }
            }
        }
        
        self.mock_character.attributes.get.return_value = skill_data
        
        result = self.skill_system.check_skill_cooldown(self.mock_character, "基础剑法")
        self.assertTrue(result["ready"], "冷却时间已过，技能应该就绪")


class TestRealmCalculator(unittest.TestCase):
    """测试境界计算器"""
    
    def setUp(self):
        """测试前准备"""
        self.realm_calc = RealmCalculator()
    
    def test_realm_level_conversion(self):
        """测试境界等级转换"""
        self.assertEqual(self.realm_calc.get_realm_level("练气"), 0)
        self.assertEqual(self.realm_calc.get_realm_level("筑基"), 1)
        self.assertEqual(self.realm_calc.get_realm_level("仙人"), 9)
        
        self.assertEqual(self.realm_calc.get_realm_name(0), "练气")
        self.assertEqual(self.realm_calc.get_realm_name(9), "仙人")
    
    def test_realm_difference_calculation(self):
        """测试境界差异计算"""
        result = self.realm_calc.calculate_realm_difference("金丹", "练气")
        
        self.assertEqual(result["level_difference"], 2)
        self.assertGreater(result["combat_bonus"], 1.0)
        self.assertIn("压制", result["suppression_level"])
    
    def test_breakthrough_progress(self):
        """测试突破进度计算"""
        result = self.realm_calc.calculate_breakthrough_progress(500, "练气")
        
        self.assertIn("progress", result)
        self.assertIn("can_breakthrough", result)
        self.assertEqual(result["progress"], 0.5)  # 500/1000 = 50%
    
    def test_cultivation_efficiency(self):
        """测试修炼效率计算"""
        result = self.realm_calc.calculate_cultivation_efficiency("练气", talent=1.5, environment_bonus=2.0)
        
        self.assertIn("total_efficiency", result)
        self.assertIn("exp_per_hour", result)
        self.assertGreater(result["total_efficiency"], 1.0)
    
    def test_realm_abilities(self):
        """测试境界特殊能力"""
        abilities = self.realm_calc.get_realm_abilities("金丹")
        self.assertIn("金丹护体", abilities)
        self.assertIn("神识外放", abilities)
    
    def test_realm_comparison(self):
        """测试境界比较"""
        result = self.realm_calc.compare_realms("元婴", "筑基")
        
        self.assertEqual(result["comparison"]["higher_realm"], "元婴")
        self.assertGreater(result["comparison"]["level_difference"], 0)


class TestCombatEventPublisher(unittest.TestCase):
    """测试战斗事件发布器"""
    
    def setUp(self):
        """测试前准备"""
        self.event_publisher = CombatEventPublisher()
    
    @patch('systems.combat_event_publisher.publish_event')
    def test_combat_start_event(self, mock_publish):
        """测试战斗开始事件发布"""
        mock_publish.return_value = True
        
        # 模拟战斗者
        combatant1 = Mock()
        combatant1.id = 1
        combatant1.key = "角色1"
        combatant1.修为境界 = "练气"
        combatant1.五行属性 = "金"
        combatant1.职业类型 = "剑修"
        combatant1.hp = 100
        combatant1.max_hp = 100
        combatant1.db = Mock()
        combatant1.db.spiritual_power = 100
        
        combatants = [combatant1]
        
        result = self.event_publisher.publish_combat_start(combatants)
        
        self.assertTrue(result, "战斗开始事件应该发布成功")
        mock_publish.assert_called_once()
    
    @patch('systems.combat_event_publisher.publish_event')
    def test_skill_cast_event(self, mock_publish):
        """测试技能施放事件发布"""
        mock_publish.return_value = True
        
        # 模拟施法者
        caster = Mock()
        caster.id = 1
        caster.key = "施法者"
        caster.修为境界 = "筑基"
        caster.五行属性 = "火"
        caster.db = Mock()
        caster.db.xiuxian_skills = {
            "烈火掌": {
                "template": {
                    "type": "攻击",
                    "element": "火",
                    "grade": "黄级",
                    "mp_cost": 15
                },
                "level": 2
            }
        }
        
        # 模拟目标
        target = Mock()
        target.id = 2
        target.key = "目标"
        target.修为境界 = "练气"
        target.五行属性 = "金"
        
        effects = [{"type": "damage", "value": 45}]
        
        result = self.event_publisher.publish_skill_cast(caster, "烈火掌", target, effects)
        
        self.assertTrue(result, "技能施放事件应该发布成功")
        mock_publish.assert_called_once()
    
    @patch('systems.combat_event_publisher.publish_event')
    def test_wuxing_interaction_event(self, mock_publish):
        """测试五行相克事件发布"""
        mock_publish.return_value = True
        
        # 模拟攻击者和目标
        attacker = Mock()
        attacker.id = 1
        attacker.key = "攻击者"
        attacker.修为境界 = "筑基"
        
        target = Mock()
        target.id = 2
        target.key = "目标"
        target.修为境界 = "练气"
        
        result = self.event_publisher.publish_wuxing_interaction(
            attacker, target, "火", "金", 1.3, "火"
        )
        
        self.assertTrue(result, "五行相克事件应该发布成功")
        mock_publish.assert_called_once()
    
    def test_event_stats(self):
        """测试事件统计"""
        # 初始统计应该为空
        stats = self.event_publisher.get_event_stats()
        self.assertEqual(stats["total_published"], 0)
        
        # 模拟发布事件后统计应该更新
        # 这里需要实际发布事件来测试，但由于依赖外部系统，暂时跳过


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.wuxing_calc = WuxingCalculator()
        self.skill_system = XianxiaSkillSystem()
        self.realm_calc = RealmCalculator()
    
    def test_skill_wuxing_integration(self):
        """测试技能与五行系统集成"""
        # 创建模拟角色
        character = Mock()
        character.修为境界 = "筑基"
        character.五行属性 = "火"
        character.get_realm_level = Mock(return_value=1)
        character.get_realm_combat_bonus = Mock(return_value=1.3)
        
        # 创建模拟目标
        target = Mock()
        target.五行属性 = "金"  # 火克金
        
        # 模拟技能数据
        skill_data = {
            "level": 2,
            "template": {
                "base_damage": 30,
                "element": "火",
                "level_scaling": {"damage": 5}
            }
        }
        
        # 计算技能伤害
        result = self.skill_system.calculate_skill_damage(skill_data, character, target)
        
        # 验证五行相克加成
        self.assertGreater(result["final_damage"], 30, "应该有五行相克和境界加成")
    
    def test_realm_combat_integration(self):
        """测试境界与战斗系统集成"""
        # 测试境界差异对战斗的影响
        diff_result = self.realm_calc.calculate_realm_difference("金丹", "练气")
        
        self.assertGreater(diff_result["combat_bonus"], 1.0, "高境界应该有战斗加成")
        self.assertEqual(diff_result["level_difference"], 2, "境界差异应该正确计算")


if __name__ == '__main__':
    # 导入time模块用于测试
    import time
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestWuxingCalculator,
        TestXianxiaSkillSystem,
        TestRealmCalculator,
        TestCombatEventPublisher,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n测试完成:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 返回退出码
    exit_code = 0 if result.wasSuccessful() else 1
    exit(exit_code)
