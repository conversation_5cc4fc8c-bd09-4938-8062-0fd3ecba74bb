/**
 * 仙侠MUD移动端交互脚本
 * 
 * 功能：
 * - 移动端特定的触控交互优化
 * - 手势识别和处理
 * - 移动端界面适配和响应式调整
 * - 虚拟键盘处理和输入优化
 * - 移动端性能优化
 * - 触控友好的UI组件增强
 * 
 * 技术特性：
 * - 触控事件处理和手势识别
 * - 移动端视口管理
 * - 软键盘适配
 * - 触控反馈和动画
 * - 移动端特定的UI模式
 */

// 移动端管理器
window.XiuxianMobile = {
    // 配置
    config: {
        touchThreshold: 10, // 触控阈值
        swipeThreshold: 50, // 滑动阈值
        longPressDelay: 500, // 长按延迟
        doubleTapDelay: 300, // 双击延迟
        vibrationEnabled: true, // 震动反馈
        gesturesEnabled: true, // 手势识别
        autoHideKeyboard: true // 自动隐藏键盘
    },

    // 状态
    state: {
        isMobile: false,
        isTablet: false,
        orientation: 'portrait',
        keyboardVisible: false,
        touchStartTime: 0,
        touchStartPos: { x: 0, y: 0 },
        lastTap: 0,
        activeTouch: null,
        gestures: {
            swipeLeft: [],
            swipeRight: [],
            swipeUp: [],
            swipeDown: [],
            longPress: [],
            doubleTap: []
        }
    },

    // 初始化
    init: function() {
        this.detectDevice();
        
        if (this.state.isMobile || this.state.isTablet) {
            this.setupMobileOptimizations();
            this.bindTouchEvents();
            this.setupGestureRecognition();
            this.setupKeyboardHandling();
            this.setupViewportManagement();
            this.optimizeForMobile();
            
            console.log('XiuxianMobile initialized for mobile device');
        }
    },

    // 检测设备类型
    detectDevice: function() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);
        
        this.state.isMobile = isMobile && !isTablet;
        this.state.isTablet = isTablet;
        
        // 添加设备类到body
        if (this.state.isMobile) {
            document.body.classList.add('mobile-device');
        }
        if (this.state.isTablet) {
            document.body.classList.add('tablet-device');
        }
        
        // 检测方向
        this.updateOrientation();
    },

    // 更新设备方向
    updateOrientation: function() {
        const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
        
        if (this.state.orientation !== orientation) {
            document.body.classList.remove('orientation-' + this.state.orientation);
            document.body.classList.add('orientation-' + orientation);
            this.state.orientation = orientation;
            
            // 触发方向改变事件
            if (window.XiuxianClient) {
                window.XiuxianClient.eventBus.emit('mobile:orientation_changed', {
                    orientation: orientation
                });
            }
        }
    },

    // 设置移动端优化
    setupMobileOptimizations: function() {
        // 禁用双击缩放
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            viewport.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }

        // 禁用文本选择（在某些情况下）
        document.addEventListener('selectstart', function(e) {
            if (e.target.closest('.no-select')) {
                e.preventDefault();
            }
        });

        // 禁用上下文菜单（长按菜单）
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.no-context-menu')) {
                e.preventDefault();
            }
        });

        // 优化滚动性能
        document.addEventListener('touchstart', function() {}, { passive: true });
        document.addEventListener('touchmove', function() {}, { passive: true });
    },

    // 绑定触控事件
    bindTouchEvents: function() {
        const self = this;

        // 触控开始
        document.addEventListener('touchstart', function(e) {
            self.handleTouchStart(e);
        }, { passive: false });

        // 触控移动
        document.addEventListener('touchmove', function(e) {
            self.handleTouchMove(e);
        }, { passive: false });

        // 触控结束
        document.addEventListener('touchend', function(e) {
            self.handleTouchEnd(e);
        }, { passive: false });

        // 触控取消
        document.addEventListener('touchcancel', function(e) {
            self.handleTouchCancel(e);
        }, { passive: false });
    },

    // 处理触控开始
    handleTouchStart: function(e) {
        const touch = e.touches[0];
        this.state.touchStartTime = Date.now();
        this.state.touchStartPos = { x: touch.clientX, y: touch.clientY };
        this.state.activeTouch = touch;

        // 添加触控反馈
        this.addTouchFeedback(e.target);

        // 检测双击
        this.checkDoubleTap(touch);

        // 开始长按检测
        this.startLongPressDetection(e);
    },

    // 处理触控移动
    handleTouchMove: function(e) {
        if (!this.state.activeTouch) return;

        const touch = e.touches[0];
        const deltaX = touch.clientX - this.state.touchStartPos.x;
        const deltaY = touch.clientY - this.state.touchStartPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // 如果移动距离超过阈值，取消长按
        if (distance > this.config.touchThreshold) {
            this.cancelLongPress();
        }

        // 检测滑动手势
        this.detectSwipeGesture(deltaX, deltaY, distance);
    },

    // 处理触控结束
    handleTouchEnd: function(e) {
        const touchDuration = Date.now() - this.state.touchStartTime;
        
        // 移除触控反馈
        this.removeTouchFeedback();

        // 取消长按检测
        this.cancelLongPress();

        // 处理点击事件
        if (touchDuration < this.config.longPressDelay) {
            this.handleTap(e);
        }

        this.state.activeTouch = null;
    },

    // 处理触控取消
    handleTouchCancel: function(e) {
        this.removeTouchFeedback();
        this.cancelLongPress();
        this.state.activeTouch = null;
    },

    // 添加触控反馈
    addTouchFeedback: function(element) {
        // 添加触控高亮效果
        element.classList.add('touch-active');
        
        // 震动反馈
        if (this.config.vibrationEnabled && navigator.vibrate) {
            navigator.vibrate(10);
        }
    },

    // 移除触控反馈
    removeTouchFeedback: function() {
        // 移除所有触控高亮
        document.querySelectorAll('.touch-active').forEach(function(el) {
            el.classList.remove('touch-active');
        });
    },

    // 检测双击
    checkDoubleTap: function(touch) {
        const now = Date.now();
        const timeDiff = now - this.state.lastTap;
        
        if (timeDiff < this.config.doubleTapDelay) {
            this.handleDoubleTap(touch);
        }
        
        this.state.lastTap = now;
    },

    // 处理双击
    handleDoubleTap: function(touch) {
        const element = document.elementFromPoint(touch.clientX, touch.clientY);
        
        // 触发双击事件
        this.triggerGestureEvent('doubleTap', {
            element: element,
            position: { x: touch.clientX, y: touch.clientY }
        });

        // 震动反馈
        if (this.config.vibrationEnabled && navigator.vibrate) {
            navigator.vibrate([50, 50, 50]);
        }
    },

    // 开始长按检测
    startLongPressDetection: function(e) {
        const self = this;
        
        this.longPressTimer = setTimeout(function() {
            self.handleLongPress(e);
        }, this.config.longPressDelay);
    },

    // 取消长按检测
    cancelLongPress: function() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    },

    // 处理长按
    handleLongPress: function(e) {
        const touch = this.state.activeTouch;
        if (!touch) return;

        const element = document.elementFromPoint(touch.clientX, touch.clientY);
        
        // 触发长按事件
        this.triggerGestureEvent('longPress', {
            element: element,
            position: { x: touch.clientX, y: touch.clientY },
            originalEvent: e
        });

        // 震动反馈
        if (this.config.vibrationEnabled && navigator.vibrate) {
            navigator.vibrate(100);
        }
    },

    // 检测滑动手势
    detectSwipeGesture: function(deltaX, deltaY, distance) {
        if (distance < this.config.swipeThreshold) return;

        let direction = '';
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            direction = deltaX > 0 ? 'swipeRight' : 'swipeLeft';
        } else {
            // 垂直滑动
            direction = deltaY > 0 ? 'swipeDown' : 'swipeUp';
        }

        if (direction) {
            this.triggerGestureEvent(direction, {
                deltaX: deltaX,
                deltaY: deltaY,
                distance: distance
            });
        }
    },

    // 处理点击
    handleTap: function(e) {
        // 这里可以添加特殊的点击处理逻辑
        // 例如：防止意外点击、点击延迟等
    },

    // 触发手势事件
    triggerGestureEvent: function(gestureType, data) {
        if (!this.config.gesturesEnabled) return;

        // 触发自定义事件
        const event = new CustomEvent('xiuxian:gesture', {
            detail: {
                type: gestureType,
                data: data
            }
        });
        
        document.dispatchEvent(event);

        // 通过事件总线通知
        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('mobile:gesture', {
                type: gestureType,
                data: data
            });
        }
    },

    // 设置手势识别
    setupGestureRecognition: function() {
        const self = this;

        // 监听自定义手势事件
        document.addEventListener('xiuxian:gesture', function(e) {
            const gestureType = e.detail.type;
            const gestureData = e.detail.data;
            
            // 处理特定手势
            switch (gestureType) {
                case 'swipeLeft':
                    self.handleSwipeLeft(gestureData);
                    break;
                case 'swipeRight':
                    self.handleSwipeRight(gestureData);
                    break;
                case 'swipeUp':
                    self.handleSwipeUp(gestureData);
                    break;
                case 'swipeDown':
                    self.handleSwipeDown(gestureData);
                    break;
                case 'longPress':
                    self.handleLongPressGesture(gestureData);
                    break;
                case 'doubleTap':
                    self.handleDoubleTapGesture(gestureData);
                    break;
            }
        });
    },

    // 处理左滑手势
    handleSwipeLeft: function(data) {
        // 可以用于切换到下一个标签页或面板
        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('ui:swipe_left', data);
        }
    },

    // 处理右滑手势
    handleSwipeRight: function(data) {
        // 可以用于切换到上一个标签页或面板
        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('ui:swipe_right', data);
        }
    },

    // 处理上滑手势
    handleSwipeUp: function(data) {
        // 可以用于显示更多选项或刷新
        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('ui:swipe_up', data);
        }
    },

    // 处理下滑手势
    handleSwipeDown: function(data) {
        // 可以用于隐藏面板或返回
        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('ui:swipe_down', data);
        }
    },

    // 处理长按手势
    handleLongPressGesture: function(data) {
        // 显示上下文菜单或详细信息
        if (data.element) {
            // 检查元素是否支持长按操作
            if (data.element.classList.contains('long-press-enabled')) {
                this.showContextMenu(data.element, data.position);
            }
        }
    },

    // 处理双击手势
    handleDoubleTapGesture: function(data) {
        // 可以用于快速操作或缩放
        if (data.element) {
            if (data.element.classList.contains('double-tap-enabled')) {
                this.handleQuickAction(data.element);
            }
        }
    },

    // 显示上下文菜单
    showContextMenu: function(element, position) {
        // 创建上下文菜单
        const menu = document.createElement('div');
        menu.className = 'mobile-context-menu';
        menu.style.left = position.x + 'px';
        menu.style.top = position.y + 'px';

        // 添加菜单项
        const actions = this.getContextActions(element);
        actions.forEach(function(action) {
            const item = document.createElement('div');
            item.className = 'context-menu-item';
            item.textContent = action.label;
            item.addEventListener('click', function() {
                action.handler();
                menu.remove();
            });
            menu.appendChild(item);
        });

        document.body.appendChild(menu);

        // 自动移除菜单
        setTimeout(function() {
            if (menu.parentElement) {
                menu.remove();
            }
        }, 5000);
    },

    // 获取上下文操作
    getContextActions: function(element) {
        const actions = [];

        // 根据元素类型添加不同的操作
        if (element.classList.contains('player-item')) {
            actions.push(
                { label: '私聊', handler: function() { /* 私聊逻辑 */ } },
                { label: '查看', handler: function() { /* 查看逻辑 */ } },
                { label: '添加好友', handler: function() { /* 添加好友逻辑 */ } }
            );
        } else if (element.classList.contains('chat-message')) {
            actions.push(
                { label: '回复', handler: function() { /* 回复逻辑 */ } },
                { label: '复制', handler: function() { /* 复制逻辑 */ } },
                { label: '举报', handler: function() { /* 举报逻辑 */ } }
            );
        }

        return actions;
    },

    // 处理快速操作
    handleQuickAction: function(element) {
        // 根据元素类型执行快速操作
        if (element.classList.contains('skill-item')) {
            // 快速修炼技能
            this.quickCultivateSkill(element);
        } else if (element.classList.contains('technique-item')) {
            // 快速修炼功法
            this.quickCultivateTechnique(element);
        }
    },

    // 快速修炼技能
    quickCultivateSkill: function(element) {
        const skillName = element.querySelector('.skill-name').textContent;
        if (window.XiuxianClient) {
            window.XiuxianClient.sendCommand('quick_cultivate_skill', { skill: skillName });
            window.XiuxianClient.showNotification('开始修炼 ' + skillName, 'info');
        }
    },

    // 快速修炼功法
    quickCultivateTechnique: function(element) {
        const techniqueName = element.querySelector('.technique-name').textContent;
        if (window.XiuxianClient) {
            window.XiuxianClient.sendCommand('quick_cultivate_technique', { technique: techniqueName });
            window.XiuxianClient.showNotification('开始修炼 ' + techniqueName, 'info');
        }
    },

    // 设置键盘处理
    setupKeyboardHandling: function() {
        const self = this;

        // 监听输入框焦点
        document.addEventListener('focusin', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                self.handleKeyboardShow(e.target);
            }
        });

        document.addEventListener('focusout', function(e) {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                self.handleKeyboardHide(e.target);
            }
        });

        // 监听视口变化（键盘显示/隐藏）
        window.addEventListener('resize', function() {
            self.handleViewportChange();
        });
    },

    // 处理键盘显示
    handleKeyboardShow: function(input) {
        this.state.keyboardVisible = true;
        document.body.classList.add('keyboard-visible');

        // 滚动到输入框
        setTimeout(function() {
            input.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);

        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('mobile:keyboard_show', { input: input });
        }
    },

    // 处理键盘隐藏
    handleKeyboardHide: function(input) {
        this.state.keyboardVisible = false;
        document.body.classList.remove('keyboard-visible');

        if (window.XiuxianClient) {
            window.XiuxianClient.eventBus.emit('mobile:keyboard_hide', { input: input });
        }
    },

    // 处理视口变化
    handleViewportChange: function() {
        // 更新方向
        this.updateOrientation();

        // 检测键盘状态
        const heightDiff = window.screen.height - window.innerHeight;
        const keyboardVisible = heightDiff > 150; // 阈值可调整

        if (keyboardVisible !== this.state.keyboardVisible) {
            this.state.keyboardVisible = keyboardVisible;

            if (keyboardVisible) {
                document.body.classList.add('keyboard-visible');
            } else {
                document.body.classList.remove('keyboard-visible');
            }
        }
    },

    // 设置视口管理
    setupViewportManagement: function() {
        // 防止iOS Safari地址栏影响
        const setViewportHeight = function() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', vh + 'px');
        };

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', function() {
            setTimeout(setViewportHeight, 100);
        });
    },

    // 移动端优化
    optimizeForMobile: function() {
        // 优化点击延迟
        this.eliminateClickDelay();

        // 优化滚动性能
        this.optimizeScrolling();

        // 优化动画性能
        this.optimizeAnimations();

        // 设置移动端特定样式
        this.applyMobileStyles();
    },

    // 消除点击延迟
    eliminateClickDelay: function() {
        // 使用FastClick或类似技术
        document.addEventListener('touchend', function(e) {
            e.preventDefault();

            const touch = e.changedTouches[0];
            const element = document.elementFromPoint(touch.clientX, touch.clientY);

            if (element && element.click) {
                element.click();
            }
        });
    },

    // 优化滚动性能
    optimizeScrolling: function() {
        // 添加滚动优化类
        document.querySelectorAll('.scrollable').forEach(function(element) {
            element.style.webkitOverflowScrolling = 'touch';
            element.style.overflowScrolling = 'touch';
        });
    },

    // 优化动画性能
    optimizeAnimations: function() {
        // 使用硬件加速
        document.querySelectorAll('.animated').forEach(function(element) {
            element.style.transform = 'translateZ(0)';
            element.style.willChange = 'transform';
        });
    },

    // 应用移动端样式
    applyMobileStyles: function() {
        // 增大触控目标
        const style = document.createElement('style');
        style.textContent = `
            .mobile-device .btn,
            .mobile-device .clickable {
                min-height: 44px;
                min-width: 44px;
            }

            .mobile-device .touch-active {
                background-color: rgba(0, 0, 0, 0.1);
                transform: scale(0.95);
                transition: all 0.1s ease;
            }

            .mobile-context-menu {
                position: fixed;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                min-width: 120px;
            }

            .context-menu-item {
                padding: 12px 16px;
                border-bottom: 1px solid #eee;
                cursor: pointer;
                font-size: 16px;
            }

            .context-menu-item:last-child {
                border-bottom: none;
            }

            .context-menu-item:hover {
                background-color: #f5f5f5;
            }

            .keyboard-visible .main-content {
                padding-bottom: 0;
            }
        `;

        document.head.appendChild(style);
    },

    // 注册手势处理器
    registerGestureHandler: function(gestureType, handler) {
        if (!this.state.gestures[gestureType]) {
            this.state.gestures[gestureType] = [];
        }
        this.state.gestures[gestureType].push(handler);
    },

    // 移除手势处理器
    removeGestureHandler: function(gestureType, handler) {
        if (this.state.gestures[gestureType]) {
            const index = this.state.gestures[gestureType].indexOf(handler);
            if (index > -1) {
                this.state.gestures[gestureType].splice(index, 1);
            }
        }
    },

    // 获取设备信息
    getDeviceInfo: function() {
        return {
            isMobile: this.state.isMobile,
            isTablet: this.state.isTablet,
            orientation: this.state.orientation,
            keyboardVisible: this.state.keyboardVisible,
            userAgent: navigator.userAgent,
            screenSize: {
                width: window.screen.width,
                height: window.screen.height
            },
            viewportSize: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    }
};

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.XiuxianMobile) {
        window.XiuxianMobile.init();
    }
});

// 导出到全局
window.xiuxianMobile = window.XiuxianMobile;
