"""
简化的小说生成系统验证脚本

不依赖Evennia环境，仅验证文件结构和基本语法。
"""

import os
import sys
import ast
import json
from datetime import datetime

def verify_file_exists(file_path):
    """验证文件是否存在"""
    return os.path.exists(file_path)

def verify_python_syntax(file_path):
    """验证Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def verify_class_exists(file_path, class_name):
    """验证类是否存在"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                return True
        return False
    except Exception:
        return False

def verify_function_exists(file_path, function_name):
    """验证函数是否存在"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == function_name:
                return True
        return False
    except Exception:
        return False

def main():
    """主验证函数"""
    print("小说生成系统简化验证脚本")
    print("=" * 50)
    
    # 基础路径
    base_path = os.path.dirname(os.path.dirname(__file__))
    
    # 验证结果
    results = {
        "verification_time": datetime.now().isoformat(),
        "tests": []
    }
    
    # 1. 验证文件结构
    print("\n=== 验证文件结构 ===")
    
    required_files = [
        "xiuxian_mud_new/scripts/novel_generator_script.py",
        "xiuxian_mud_new/systems/novel_event_collector.py", 
        "xiuxian_mud_new/systems/narrative_context_manager.py",
        "xiuxian_mud_new/systems/novel_content_generator.py",
        "xiuxian_mud_new/commands/novel_commands.py",
        "测试/test_novel_generation_system.py"
    ]
    
    file_check_passed = True
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if verify_file_exists(full_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 缺失")
            file_check_passed = False
    
    results["tests"].append({
        "name": "文件结构检查",
        "status": "PASS" if file_check_passed else "FAIL"
    })
    
    # 2. 验证Python语法
    print("\n=== 验证Python语法 ===")
    
    syntax_check_passed = True
    for file_path in required_files:
        if file_path.endswith('.py'):
            full_path = os.path.join(base_path, file_path)
            if verify_file_exists(full_path):
                is_valid, error = verify_python_syntax(full_path)
                if is_valid:
                    print(f"✓ {file_path} 语法正确")
                else:
                    print(f"✗ {file_path} 语法错误: {error}")
                    syntax_check_passed = False
    
    results["tests"].append({
        "name": "Python语法检查",
        "status": "PASS" if syntax_check_passed else "FAIL"
    })
    
    # 3. 验证核心类存在
    print("\n=== 验证核心类存在 ===")
    
    class_checks = [
        ("xiuxian_mud_new/scripts/novel_generator_script.py", "NovelGeneratorScript"),
        ("xiuxian_mud_new/scripts/novel_generator_script.py", "NovelEventHandler"),
        ("xiuxian_mud_new/systems/novel_event_collector.py", "NovelEventCollector"),
        ("xiuxian_mud_new/systems/narrative_context_manager.py", "NarrativeContextManager"),
        ("xiuxian_mud_new/systems/novel_content_generator.py", "NovelContentGenerator"),
    ]
    
    class_check_passed = True
    for file_path, class_name in class_checks:
        full_path = os.path.join(base_path, file_path)
        if verify_file_exists(full_path):
            if verify_class_exists(full_path, class_name):
                print(f"✓ {class_name} 类存在于 {file_path}")
            else:
                print(f"✗ {class_name} 类缺失于 {file_path}")
                class_check_passed = False
    
    results["tests"].append({
        "name": "核心类检查",
        "status": "PASS" if class_check_passed else "FAIL"
    })
    
    # 4. 验证关键方法存在
    print("\n=== 验证关键方法存在 ===")
    
    method_checks = [
        ("xiuxian_mud_new/scripts/novel_generator_script.py", "generate_novel_chapter"),
        ("xiuxian_mud_new/scripts/novel_generator_script.py", "on_event_trigger"),
        ("xiuxian_mud_new/systems/novel_event_collector.py", "collect_event"),
        ("xiuxian_mud_new/systems/novel_event_collector.py", "evaluate_significance"),
        ("xiuxian_mud_new/systems/narrative_context_manager.py", "build_narrative_context"),
        ("xiuxian_mud_new/systems/novel_content_generator.py", "generate_chapter"),
        ("xiuxian_mud_new/systems/novel_content_generator.py", "generate_event_narrative"),
    ]
    
    method_check_passed = True
    for file_path, method_name in method_checks:
        full_path = os.path.join(base_path, file_path)
        if verify_file_exists(full_path):
            if verify_function_exists(full_path, method_name):
                print(f"✓ {method_name} 方法存在于 {file_path}")
            else:
                print(f"✗ {method_name} 方法缺失于 {file_path}")
                method_check_passed = False
    
    results["tests"].append({
        "name": "关键方法检查",
        "status": "PASS" if method_check_passed else "FAIL"
    })
    
    # 5. 验证命令类存在
    print("\n=== 验证命令类存在 ===")
    
    command_classes = [
        "CmdNovelStatus",
        "CmdNovelGenerate", 
        "CmdNovelConfig",
        "CmdNovelChapters",
        "CmdNovelRead"
    ]
    
    command_check_passed = True
    commands_file = os.path.join(base_path, "xiuxian_mud_new/commands/novel_commands.py")
    
    if verify_file_exists(commands_file):
        for class_name in command_classes:
            if verify_class_exists(commands_file, class_name):
                print(f"✓ {class_name} 命令类存在")
            else:
                print(f"✗ {class_name} 命令类缺失")
                command_check_passed = False
    else:
        command_check_passed = False
    
    results["tests"].append({
        "name": "命令类检查",
        "status": "PASS" if command_check_passed else "FAIL"
    })
    
    # 6. 验证测试文件完整性
    print("\n=== 验证测试文件完整性 ===")
    
    test_file = os.path.join(base_path, "测试/test_novel_generation_system.py")
    test_check_passed = True
    
    if verify_file_exists(test_file):
        test_classes = [
            "TestNovelEventCollector",
            "TestNarrativeContextManager", 
            "TestNovelContentGenerator",
            "TestNovelGeneratorScript",
            "TestNovelEventHandler"
        ]
        
        for class_name in test_classes:
            if verify_class_exists(test_file, class_name):
                print(f"✓ {class_name} 测试类存在")
            else:
                print(f"✗ {class_name} 测试类缺失")
                test_check_passed = False
    else:
        test_check_passed = False
        print("✗ 测试文件不存在")
    
    results["tests"].append({
        "name": "测试文件检查",
        "status": "PASS" if test_check_passed else "FAIL"
    })
    
    # 计算总体结果
    passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
    total_tests = len(results["tests"])
    all_passed = passed_tests == total_tests
    
    results["overall_status"] = "PASS" if all_passed else "FAIL"
    results["passed_tests"] = passed_tests
    results["total_tests"] = total_tests
    
    # 保存结果
    report_path = os.path.join(base_path, "测试", "simple_verification_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 显示总结
    print(f"\n=== 验证总结 ===")
    print(f"总体状态: {'✓ 通过' if all_passed else '✗ 失败'}")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if not all_passed:
        print("\n失败的测试:")
        for test in results["tests"]:
            if test["status"] != "PASS":
                print(f"  - {test['name']}: {test['status']}")
    
    print(f"\n验证报告已保存到: {report_path}")
    
    if all_passed:
        print("\n🎉 基础验证通过！小说生成系统文件结构和语法正确。")
        print("注意：完整功能测试需要在Evennia环境中运行。")
        return True
    else:
        print("\n❌ 验证失败，请检查上述错误。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
