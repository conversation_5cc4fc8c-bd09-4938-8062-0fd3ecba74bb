"""
五行相克计算器

实现传统五行相克理论的高性能计算系统。
支持五行属性查询、相克关系计算、季节影响等功能。
"""

from typing import Dict, Optional, Tuple
from enum import Enum
from evennia.utils import logger
import time


class WuxingElement(Enum):
    """五行元素枚举"""
    METAL = "金"    # 金
    WOOD = "木"     # 木
    WATER = "水"    # 水
    FIRE = "火"     # 火
    EARTH = "土"    # 土


class WuxingCalculator:
    """
    五行相克计算器
    
    实现传统五行理论：
    - 相生：金生水，水生木，木生火，火生土，土生金
    - 相克：金克木，木克土，土克水，水克火，火克金
    
    提供高性能的五行相克计算和属性查询功能。
    """
    
    # 五行相克关系矩阵
    WUXING_MATRIX = {
        WuxingElement.METAL: {
            "克制": WuxingElement.WOOD,
            "被克": WuxingElement.FIRE,
            "相生": WuxingElement.WATER,
            "生我": WuxingElement.EARTH
        },
        WuxingElement.WOOD: {
            "克制": WuxingElement.EARTH,
            "被克": WuxingElement.METAL,
            "相生": WuxingElement.FIRE,
            "生我": WuxingElement.WATER
        },
        WuxingElement.WATER: {
            "克制": WuxingElement.FIRE,
            "被克": WuxingElement.EARTH,
            "相生": WuxingElement.WOOD,
            "生我": WuxingElement.METAL
        },
        WuxingElement.FIRE: {
            "克制": WuxingElement.METAL,
            "被克": WuxingElement.WATER,
            "相生": WuxingElement.EARTH,
            "生我": WuxingElement.WOOD
        },
        WuxingElement.EARTH: {
            "克制": WuxingElement.WATER,
            "被克": WuxingElement.WOOD,
            "相生": WuxingElement.METAL,
            "生我": WuxingElement.FIRE
        }
    }
    
    # 五行战斗加成配置
    COMBAT_MODIFIERS = {
        "相克": 1.5,      # 相克时伤害加成50%
        "被克": 0.7,      # 被克时伤害减少30%
        "相生": 1.2,      # 相生时效果加成20%
        "生我": 1.1,      # 生我时恢复加成10%
        "平衡": 1.0       # 无关系时无加成
    }
    
    # 季节对五行的影响
    SEASONAL_MODIFIERS = {
        "春": {WuxingElement.WOOD: 1.3, WuxingElement.METAL: 0.8},
        "夏": {WuxingElement.FIRE: 1.3, WuxingElement.WATER: 0.8},
        "秋": {WuxingElement.METAL: 1.3, WuxingElement.WOOD: 0.8},
        "冬": {WuxingElement.WATER: 1.3, WuxingElement.FIRE: 0.8},
        "长夏": {WuxingElement.EARTH: 1.3}  # 土旺于四季之末
    }
    
    def __init__(self):
        """初始化五行计算器"""
        self._cache = {}  # 计算结果缓存
        self._cache_timeout = 300  # 缓存5分钟
        logger.log_info("WuxingCalculator initialized")
    
    def get_element_enum(self, element_str: str) -> Optional[WuxingElement]:
        """
        将字符串转换为五行枚举
        
        Args:
            element_str: 五行字符串（金、木、水、火、土）
            
        Returns:
            WuxingElement: 对应的五行枚举，无效时返回None
        """
        element_map = {
            "金": WuxingElement.METAL,
            "木": WuxingElement.WOOD,
            "水": WuxingElement.WATER,
            "火": WuxingElement.FIRE,
            "土": WuxingElement.EARTH
        }
        return element_map.get(element_str)
    
    def calculate_elemental_bonus(self, attacker_element: str, defender_element: str) -> float:
        """
        计算五行相克的战斗加成
        
        Args:
            attacker_element: 攻击者的五行属性
            defender_element: 防御者的五行属性
            
        Returns:
            float: 伤害加成倍数
        """
        # 生成缓存键
        cache_key = f"combat_{attacker_element}_{defender_element}"
        
        # 检查缓存
        if cache_key in self._cache:
            cache_data = self._cache[cache_key]
            if time.time() - cache_data["timestamp"] < self._cache_timeout:
                return cache_data["value"]
        
        # 转换为枚举
        attacker_enum = self.get_element_enum(attacker_element)
        defender_enum = self.get_element_enum(defender_element)
        
        if not attacker_enum or not defender_enum:
            return self.COMBAT_MODIFIERS["平衡"]
        
        # 计算相克关系
        relationship = self.get_element_relationship(attacker_enum, defender_enum)
        bonus = self.COMBAT_MODIFIERS.get(relationship, self.COMBAT_MODIFIERS["平衡"])
        
        # 缓存结果
        self._cache[cache_key] = {
            "value": bonus,
            "timestamp": time.time()
        }
        
        return bonus
    
    def get_element_relationship(self, element1: WuxingElement, element2: WuxingElement) -> str:
        """
        获取两个五行元素之间的关系
        
        Args:
            element1: 第一个五行元素
            element2: 第二个五行元素
            
        Returns:
            str: 关系类型（相克、被克、相生、生我、平衡）
        """
        if element1 == element2:
            return "平衡"
        
        relations = self.WUXING_MATRIX.get(element1, {})
        
        if relations.get("克制") == element2:
            return "相克"
        elif relations.get("被克") == element2:
            return "被克"
        elif relations.get("相生") == element2:
            return "相生"
        elif relations.get("生我") == element2:
            return "生我"
        else:
            return "平衡"
    
    def get_element_from_skill(self, skill_name: str) -> str:
        """
        从技能名称推断五行属性
        
        Args:
            skill_name: 技能名称
            
        Returns:
            str: 推断的五行属性
        """
        # 技能名称关键词映射
        element_keywords = {
            "金": ["剑", "刀", "金", "锐", "斩", "切", "刺"],
            "木": ["木", "藤", "叶", "花", "树", "林", "生"],
            "水": ["水", "冰", "雪", "霜", "寒", "冻", "流"],
            "火": ["火", "炎", "焰", "烧", "热", "爆", "雷"],
            "土": ["土", "石", "岩", "山", "地", "护", "盾"]
        }
        
        for element, keywords in element_keywords.items():
            for keyword in keywords:
                if keyword in skill_name:
                    return element
        
        return "土"  # 默认返回土属性
    
    def apply_seasonal_modifiers(self, element: str, season: str) -> float:
        """
        应用季节对五行的影响
        
        Args:
            element: 五行属性
            season: 季节（春、夏、秋、冬、长夏）
            
        Returns:
            float: 季节加成倍数
        """
        element_enum = self.get_element_enum(element)
        if not element_enum:
            return 1.0
        
        seasonal_mods = self.SEASONAL_MODIFIERS.get(season, {})
        return seasonal_mods.get(element_enum, 1.0)
    
    def calculate_comprehensive_bonus(self, attacker_element: str, defender_element: str, 
                                    season: str = None, special_conditions: Dict = None) -> Dict:
        """
        计算综合的五行加成
        
        Args:
            attacker_element: 攻击者五行属性
            defender_element: 防御者五行属性
            season: 当前季节
            special_conditions: 特殊条件字典
            
        Returns:
            Dict: 详细的加成计算结果
        """
        result = {
            "base_bonus": 1.0,
            "seasonal_bonus": 1.0,
            "special_bonus": 1.0,
            "final_bonus": 1.0,
            "relationship": "平衡",
            "details": []
        }
        
        # 基础五行相克加成
        base_bonus = self.calculate_elemental_bonus(attacker_element, defender_element)
        result["base_bonus"] = base_bonus
        
        # 获取关系类型
        attacker_enum = self.get_element_enum(attacker_element)
        defender_enum = self.get_element_enum(defender_element)
        if attacker_enum and defender_enum:
            result["relationship"] = self.get_element_relationship(attacker_enum, defender_enum)
        
        # 季节加成
        if season:
            seasonal_bonus = self.apply_seasonal_modifiers(attacker_element, season)
            result["seasonal_bonus"] = seasonal_bonus
            if seasonal_bonus != 1.0:
                result["details"].append(f"季节({season})对{attacker_element}的影响: {seasonal_bonus:.1f}x")
        
        # 特殊条件加成
        if special_conditions:
            special_bonus = self._calculate_special_bonus(special_conditions)
            result["special_bonus"] = special_bonus
            if special_bonus != 1.0:
                result["details"].append(f"特殊条件加成: {special_bonus:.1f}x")
        
        # 计算最终加成
        result["final_bonus"] = base_bonus * result["seasonal_bonus"] * result["special_bonus"]
        
        # 添加基础关系说明
        if result["relationship"] != "平衡":
            result["details"].insert(0, f"{attacker_element}{result['relationship']}{defender_element}: {base_bonus:.1f}x")
        
        return result
    
    def _calculate_special_bonus(self, special_conditions: Dict) -> float:
        """
        计算特殊条件加成
        
        Args:
            special_conditions: 特殊条件字典
            
        Returns:
            float: 特殊条件加成倍数
        """
        bonus = 1.0
        
        # 地形加成
        terrain = special_conditions.get("terrain")
        if terrain:
            terrain_bonuses = {
                "森林": {"木": 1.2},
                "沙漠": {"火": 1.2, "土": 1.1},
                "湖泊": {"水": 1.2},
                "山地": {"土": 1.2, "金": 1.1},
                "雷区": {"火": 1.3}
            }
            
            element = special_conditions.get("element")
            if terrain in terrain_bonuses and element in terrain_bonuses[terrain]:
                bonus *= terrain_bonuses[terrain][element]
        
        # 时辰加成（简化版）
        hour = special_conditions.get("hour")
        if hour:
            # 子时(23-1)水旺，午时(11-13)火旺等
            hour_elements = {
                23: "水", 0: "水", 1: "水",
                11: "火", 12: "火", 13: "火"
            }
            
            if hour in hour_elements:
                hour_element = hour_elements[hour]
                if special_conditions.get("element") == hour_element:
                    bonus *= 1.1
        
        return bonus
    
    def get_element_info(self, element: str) -> Dict:
        """
        获取五行元素的详细信息
        
        Args:
            element: 五行属性
            
        Returns:
            Dict: 五行元素信息
        """
        element_enum = self.get_element_enum(element)
        if not element_enum:
            return {"error": "无效的五行属性"}
        
        relations = self.WUXING_MATRIX[element_enum]
        
        return {
            "element": element,
            "克制": relations["克制"].value,
            "被克": relations["被克"].value,
            "相生": relations["相生"].value,
            "生我": relations["生我"].value,
            "特性": self._get_element_characteristics(element_enum)
        }
    
    def _get_element_characteristics(self, element: WuxingElement) -> Dict:
        """
        获取五行元素的特性描述
        
        Args:
            element: 五行元素
            
        Returns:
            Dict: 元素特性
        """
        characteristics = {
            WuxingElement.METAL: {
                "性质": "坚硬、锐利、收敛",
                "方位": "西",
                "季节": "秋",
                "颜色": "白、金",
                "战斗特点": "攻击力强，穿透性好"
            },
            WuxingElement.WOOD: {
                "性质": "生长、柔韧、向上",
                "方位": "东",
                "季节": "春",
                "颜色": "青、绿",
                "战斗特点": "恢复能力强，持续伤害"
            },
            WuxingElement.WATER: {
                "性质": "流动、寒冷、向下",
                "方位": "北",
                "季节": "冬",
                "颜色": "黑、蓝",
                "战斗特点": "控制能力强，减速效果"
            },
            WuxingElement.FIRE: {
                "性质": "炎热、向上、光明",
                "方位": "南",
                "季节": "夏",
                "颜色": "红、橙",
                "战斗特点": "爆发伤害高，范围攻击"
            },
            WuxingElement.EARTH: {
                "性质": "厚重、稳定、中央",
                "方位": "中",
                "季节": "长夏",
                "颜色": "黄、棕",
                "战斗特点": "防御力强，生命值高"
            }
        }
        
        return characteristics.get(element, {})
    
    def clear_cache(self):
        """清空计算缓存"""
        self._cache.clear()
        logger.log_info("WuxingCalculator cache cleared")


# 全局实例
wuxing_calculator = WuxingCalculator()


def get_wuxing_calculator() -> WuxingCalculator:
    """获取五行计算器实例"""
    return wuxing_calculator
