"""
性能监控仪表板

基于Evennia Web界面的实时性能监控仪表板，提供：
- 实时性能指标展示
- 性能趋势图表
- 优化状态监控
- 配置管理界面
- 性能报告生成

功能模块：
- 系统性能监控
- TagProperty缓存状态
- 事件总线性能
- Handler内存使用
- AI导演优化状态
"""

import json
import time
import statistics
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from evennia.web.website.views import EvenniaView
from evennia.utils.logger import log_info, log_err
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

# 导入性能监控组件
from .performance_monitor import get_performance_monitor, PerformanceAnalyzer
from .performance_config import get_performance_config_manager, get_performance_config
from .tag_property_cache import CachedTagPropertyQueryManager
from .event_performance_optimizer import get_event_optimizer
from .handler_memory_optimizer import get_handler_memory_optimizer
from .ai_director_performance_optimizer import get_ai_director_optimizer


class PerformanceDashboardData:
    """性能仪表板数据收集器"""
    
    def __init__(self):
        self.last_update = 0
        self.cache_duration = 5  # 5秒缓存
        self._cached_data = None
        
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        current_time = time.time()
        
        # 检查缓存
        if (self._cached_data and 
            current_time - self.last_update < self.cache_duration):
            return self._cached_data
            
        # 收集新数据
        data = {
            "timestamp": current_time,
            "system_overview": self._get_system_overview(),
            "tagproperty_performance": self._get_tagproperty_performance(),
            "event_bus_performance": self._get_event_bus_performance(),
            "handler_memory_status": self._get_handler_memory_status(),
            "ai_director_performance": self._get_ai_director_performance(),
            "optimization_status": self._get_optimization_status(),
            "performance_trends": self._get_performance_trends(),
            "alerts": self._get_performance_alerts()
        }
        
        # 更新缓存
        self._cached_data = data
        self.last_update = current_time
        
        return data
        
    def _get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        try:
            monitor = get_performance_monitor()
            if monitor:
                latest_metrics = monitor.get_latest_metrics()
                if latest_metrics and latest_metrics.system_metrics:
                    return {
                        "cpu_usage": latest_metrics.system_metrics.get("cpu_percent", 0),
                        "memory_usage": latest_metrics.system_metrics.get("memory_percent", 0),
                        "active_sessions": latest_metrics.system_metrics.get("active_sessions", 0),
                        "uptime_hours": latest_metrics.system_metrics.get("uptime_hours", 0),
                        "status": "healthy" if latest_metrics.system_metrics.get("cpu_percent", 0) < 80 else "warning"
                    }
        except Exception as e:
            log_err(f"获取系统概览失败: {e}")
            
        return {
            "cpu_usage": 0, "memory_usage": 0, "active_sessions": 0,
            "uptime_hours": 0, "status": "unknown"
        }
        
    def _get_tagproperty_performance(self) -> Dict[str, Any]:
        """获取TagProperty性能数据"""
        try:
            cache_stats = CachedTagPropertyQueryManager.get_cache_stats()
            return {
                "cache_hit_rate": cache_stats.get("overall_hit_rate", 0),
                "total_queries": cache_stats.get("total_queries", 0),
                "cache_size": cache_stats.get("total_cache_size", 0),
                "avg_query_time": cache_stats.get("avg_query_time", 0),
                "optimization_level": "high" if cache_stats.get("overall_hit_rate", 0) > 0.8 else "medium"
            }
        except Exception as e:
            log_err(f"获取TagProperty性能数据失败: {e}")
            return {
                "cache_hit_rate": 0, "total_queries": 0, "cache_size": 0,
                "avg_query_time": 0, "optimization_level": "unknown"
            }
            
    def _get_event_bus_performance(self) -> Dict[str, Any]:
        """获取事件总线性能数据"""
        try:
            optimizer = get_event_optimizer()
            if optimizer:
                stats = optimizer.get_optimization_stats()
                return {
                    "events_processed": stats.get("events_processed", 0),
                    "compression_ratio": stats.get("compression_ratio", 0),
                    "parallel_batches": stats.get("parallel_batches_processed", 0),
                    "avg_processing_time": stats.get("avg_processing_time", 0),
                    "throughput_improvement": stats.get("throughput_improvement", 0)
                }
        except Exception as e:
            log_err(f"获取事件总线性能数据失败: {e}")
            
        return {
            "events_processed": 0, "compression_ratio": 0, "parallel_batches": 0,
            "avg_processing_time": 0, "throughput_improvement": 0
        }
        
    def _get_handler_memory_status(self) -> Dict[str, Any]:
        """获取Handler内存状态"""
        try:
            optimizer = get_handler_memory_optimizer()
            if optimizer:
                report = optimizer.get_optimization_report()
                return {
                    "memory_saved_mb": report.get("total_memory_saved_mb", 0),
                    "optimization_enabled": report.get("optimization_enabled", False),
                    "active_handlers": report.get("active_handlers", 0),
                    "memory_efficiency": report.get("intelligent_optimization", {}).get("efficiency_improvement", 0),
                    "predictions_made": report.get("intelligent_optimization", {}).get("predictions_made", 0)
                }
        except Exception as e:
            log_err(f"获取Handler内存状态失败: {e}")
            
        return {
            "memory_saved_mb": 0, "optimization_enabled": False, "active_handlers": 0,
            "memory_efficiency": 0, "predictions_made": 0
        }
        
    def _get_ai_director_performance(self) -> Dict[str, Any]:
        """获取AI导演性能数据"""
        try:
            optimizer = get_ai_director_optimizer()
            if optimizer:
                stats = optimizer.get_optimization_stats()
                return {
                    "decisions_optimized": stats.get("decisions_optimized", 0),
                    "cache_hit_rate": stats.get("cache_hit_rate", 0),
                    "parallel_processing_rate": stats.get("parallel_processing_rate", 0),
                    "avg_time_saved": stats.get("avg_time_saved_per_decision", 0),
                    "decisions_per_minute": stats.get("decisions_per_minute", 0)
                }
        except Exception as e:
            log_err(f"获取AI导演性能数据失败: {e}")
            
        return {
            "decisions_optimized": 0, "cache_hit_rate": 0, "parallel_processing_rate": 0,
            "avg_time_saved": 0, "decisions_per_minute": 0
        }
        
    def _get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        config = get_performance_config()
        
        return {
            "tagproperty_cache_enabled": config.tagproperty_cache.auto_optimization,
            "event_compression_enabled": config.event_bus_optimization.enable_compression,
            "memory_optimization_enabled": config.handler_memory.enable_intelligent_optimization,
            "ai_adaptive_scheduling_enabled": config.ai_director_optimization.enable_adaptive_scheduling,
            "monitoring_enabled": config.performance_monitor.monitoring_enabled,
            "overall_optimization_level": self._calculate_optimization_level(config)
        }
        
    def _get_performance_trends(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取性能趋势数据"""
        try:
            monitor = get_performance_monitor()
            if monitor:
                analyzer = monitor.performance_analyzer
                
                # 获取最近1小时的数据
                recent_metrics = analyzer.get_recent_metrics(hours=1)
                
                trends = {
                    "cpu_trend": [],
                    "memory_trend": [],
                    "query_time_trend": [],
                    "event_throughput_trend": []
                }
                
                for metrics in recent_metrics[-20:]:  # 最近20个数据点
                    timestamp = metrics.timestamp
                    
                    if metrics.system_metrics:
                        trends["cpu_trend"].append({
                            "timestamp": timestamp,
                            "value": metrics.system_metrics.get("cpu_percent", 0)
                        })
                        trends["memory_trend"].append({
                            "timestamp": timestamp,
                            "value": metrics.system_metrics.get("memory_percent", 0)
                        })
                        
                    if metrics.tagproperty_metrics:
                        trends["query_time_trend"].append({
                            "timestamp": timestamp,
                            "value": metrics.tagproperty_metrics.get("avg_query_time", 0) * 1000  # 转换为毫秒
                        })
                        
                    if metrics.event_metrics:
                        trends["event_throughput_trend"].append({
                            "timestamp": timestamp,
                            "value": metrics.event_metrics.get("events_per_second", 0)
                        })
                        
                return trends
                
        except Exception as e:
            log_err(f"获取性能趋势失败: {e}")
            
        return {"cpu_trend": [], "memory_trend": [], "query_time_trend": [], "event_throughput_trend": []}
        
    def _get_performance_alerts(self) -> List[Dict[str, Any]]:
        """获取性能警报"""
        alerts = []
        config = get_performance_config()
        
        try:
            # 检查系统指标
            system_overview = self._get_system_overview()
            
            if system_overview["cpu_usage"] > config.performance_monitor.alert_thresholds["cpu_usage"]:
                alerts.append({
                    "type": "warning",
                    "category": "system",
                    "message": f"CPU使用率过高: {system_overview['cpu_usage']:.1f}%",
                    "timestamp": time.time()
                })
                
            if system_overview["memory_usage"] > config.performance_monitor.alert_thresholds["memory_usage"]:
                alerts.append({
                    "type": "warning",
                    "category": "system",
                    "message": f"内存使用率过高: {system_overview['memory_usage']:.1f}%",
                    "timestamp": time.time()
                })
                
            # 检查TagProperty性能
            tagprop_perf = self._get_tagproperty_performance()
            if tagprop_perf["cache_hit_rate"] < 0.7:
                alerts.append({
                    "type": "info",
                    "category": "tagproperty",
                    "message": f"TagProperty缓存命中率较低: {tagprop_perf['cache_hit_rate']:.1%}",
                    "timestamp": time.time()
                })
                
        except Exception as e:
            log_err(f"生成性能警报失败: {e}")
            
        return alerts
        
    def _calculate_optimization_level(self, config) -> str:
        """计算总体优化级别"""
        enabled_count = 0
        total_count = 5
        
        if config.tagproperty_cache.auto_optimization:
            enabled_count += 1
        if config.event_bus_optimization.enable_compression:
            enabled_count += 1
        if config.handler_memory.enable_intelligent_optimization:
            enabled_count += 1
        if config.ai_director_optimization.enable_adaptive_scheduling:
            enabled_count += 1
        if config.performance_monitor.monitoring_enabled:
            enabled_count += 1
            
        ratio = enabled_count / total_count
        
        if ratio >= 0.8:
            return "high"
        elif ratio >= 0.5:
            return "medium"
        else:
            return "low"


class PerformanceDashboardView(EvenniaView):
    """性能仪表板视图"""
    
    def __init__(self):
        super().__init__()
        self.dashboard_data = PerformanceDashboardData()
        
    def get(self, request):
        """渲染仪表板页面"""
        try:
            context = {
                "page_title": "性能监控仪表板",
                "dashboard_data": self.dashboard_data.get_dashboard_data()
            }
            return render(request, 'performance_dashboard.html', context)
        except Exception as e:
            log_err(f"渲染性能仪表板失败: {e}")
            return HttpResponse("仪表板暂时不可用", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PerformanceDashboardAPIView(EvenniaView):
    """性能仪表板API视图"""
    
    def __init__(self):
        super().__init__()
        self.dashboard_data = PerformanceDashboardData()
        
    def get(self, request):
        """获取仪表板数据API"""
        try:
            data = self.dashboard_data.get_dashboard_data()
            return JsonResponse(data)
        except Exception as e:
            log_err(f"获取仪表板数据失败: {e}")
            return JsonResponse({"error": "数据获取失败"}, status=500)
            
    def post(self, request):
        """更新配置API"""
        try:
            data = json.loads(request.body)
            action = data.get("action")
            
            if action == "update_config":
                config_updates = data.get("config_updates", {})
                config_manager = get_performance_config_manager()
                config_manager.update_config(config_updates)
                
                return JsonResponse({"status": "success", "message": "配置已更新"})
                
            elif action == "optimize_cache":
                CachedTagPropertyQueryManager.optimize_cache()
                return JsonResponse({"status": "success", "message": "缓存已优化"})
                
            elif action == "clear_cache":
                CachedTagPropertyQueryManager.invalidate_cache()
                return JsonResponse({"status": "success", "message": "缓存已清理"})
                
            else:
                return JsonResponse({"error": "未知操作"}, status=400)
                
        except Exception as e:
            log_err(f"处理仪表板API请求失败: {e}")
            return JsonResponse({"error": "请求处理失败"}, status=500)


class PerformanceReportView(EvenniaView):
    """性能报告视图"""
    
    def get(self, request):
        """生成性能报告"""
        try:
            monitor = get_performance_monitor()
            if monitor:
                report = monitor.performance_analyzer.generate_performance_report()
                
                context = {
                    "page_title": "性能报告",
                    "report": report,
                    "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                return render(request, 'performance_report.html', context)
            else:
                return HttpResponse("性能监控未启用", status=503)
                
        except Exception as e:
            log_err(f"生成性能报告失败: {e}")
            return HttpResponse("报告生成失败", status=500)


# 全局仪表板数据实例
_dashboard_data = PerformanceDashboardData()


def get_dashboard_data() -> Dict[str, Any]:
    """获取仪表板数据（便捷函数）"""
    return _dashboard_data.get_dashboard_data()


def get_performance_summary() -> Dict[str, Any]:
    """获取性能摘要"""
    data = get_dashboard_data()
    
    return {
        "system_status": data["system_overview"]["status"],
        "optimization_level": data["optimization_status"]["overall_optimization_level"],
        "cache_hit_rate": data["tagproperty_performance"]["cache_hit_rate"],
        "memory_saved_mb": data["handler_memory_status"]["memory_saved_mb"],
        "active_optimizations": sum([
            data["optimization_status"]["tagproperty_cache_enabled"],
            data["optimization_status"]["event_compression_enabled"],
            data["optimization_status"]["memory_optimization_enabled"],
            data["optimization_status"]["ai_adaptive_scheduling_enabled"]
        ]),
        "alert_count": len(data["alerts"])
    }
