"""
宏集成测试套件

测试多系统协同场景：
- 修炼突破完整事件链测试
- 战斗事件多系统协同测试
- 世界变化传播测试
- 社交互动影响测试
- NPC智能响应测试
"""

import time
import unittest
from unittest.mock import Mock, patch

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import (
    XianxiaIntegrationTest, create_test_character, create_test_room, 
    create_test_npc, cleanup_test_objects
)
from ..systems.event_system import XianxiaEventBus, BaseEvent
from ..systems.tag_property_system import TagPropertyQueryManager
from ..typeclasses.characters import XianxiaCharacter
from ..typeclasses.rooms import XianxiaRoom
from ..typeclasses.npcs import XianxiaNPC


class CultivationBreakthroughEventChainTest(XianxiaIntegrationTest):
    """修炼突破完整事件链测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试环境
        self.test_room = create_test_room("cultivation_test_room")
        self.test_char = create_test_character("cultivation_char", self.test_room)
        self.test_npc = create_test_npc("cultivation_npc", self.test_room)
        
        # 设置角色初始境界
        self.test_char.tags.add("练气一层", category="realm")
        self.test_char.tags.add("100", category="cultivation_progress")
        
        # 初始化所有系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_complete_cultivation_breakthrough_chain(self):
        """测试完整的修炼突破事件链"""
        log_info("开始测试完整修炼突破事件链")
        
        # 1. 触发修炼突破事件
        breakthrough_event = BaseEvent(
            event_type="cultivation_breakthrough",
            event_data={
                "character_id": self.test_char.id,
                "character_name": self.test_char.key,
                "old_realm": "练气一层",
                "new_realm": "练气二层",
                "location": self.test_room.id,
                "breakthrough_type": "natural",
                "spiritual_energy_consumed": 1000
            },
            priority="HIGH"
        )
        
        # 开始事件追踪
        trace_id = self.event_tracker.start_trace(breakthrough_event)
        
        # 发布突破事件
        event_bus_scripts = search_script("xianxia_event_bus")
        self.assertTrue(event_bus_scripts, "事件总线未找到")
        
        event_bus = event_bus_scripts[0]
        event_bus.publish_event(breakthrough_event)
        
        # 2. 等待事件传播和处理
        time.sleep(5)
        
        # 3. 验证角色系统更新
        # 检查角色境界是否更新
        realm_tags = self.test_char.tags.get(category="realm", return_list=True)
        self.assertIn("练气二层", realm_tags, "角色境界未更新")
        
        # 4. 验证AI导演系统响应
        # 检查天道导演是否记录了突破事件
        tiandao_scripts = search_script("tiandao_director_script")
        if tiandao_scripts:
            tiandao = tiandao_scripts[0]
            if hasattr(tiandao, 'recent_breakthrough_events'):
                recent_events = tiandao.recent_breakthrough_events
                self.assertTrue(any(e.get('character_id') == self.test_char.id for e in recent_events),
                              "天道导演未记录突破事件")
        
        # 5. 验证小说生成系统响应
        # 检查是否生成了突破相关的叙事内容
        novel_scripts = search_script("novel_generator_script")
        if novel_scripts:
            novel_generator = novel_scripts[0]
            if hasattr(novel_generator, 'recent_narratives'):
                narratives = novel_generator.recent_narratives
                breakthrough_narratives = [n for n in narratives if "突破" in n.get('content', '')]
                self.assertTrue(breakthrough_narratives, "小说生成系统未响应突破事件")
        
        # 6. 验证NPC系统响应
        # 检查NPC是否对突破事件有反应
        if hasattr(self.test_npc, 'recent_reactions'):
            reactions = self.test_npc.recent_reactions
            breakthrough_reactions = [r for r in reactions if r.get('trigger') == 'cultivation_breakthrough']
            self.assertTrue(breakthrough_reactions, "NPC未对突破事件产生反应")
        
        # 7. 验证世界系统响应
        # 检查环境是否因突破而发生变化
        from ..systems.world_events import world_event_system
        if hasattr(world_event_system, 'recent_environmental_changes'):
            env_changes = world_event_system.recent_environmental_changes
            breakthrough_changes = [c for c in env_changes if c.get('trigger') == 'cultivation_breakthrough']
            self.assertTrue(breakthrough_changes, "世界系统未响应突破事件")
        
        # 完成事件追踪
        self.event_tracker.complete_trace(trace_id, True)
        
        # 验证整个事件链的性能
        self.assert_event_chain_success(trace_id, timeout=10.0)
        
        log_info("完整修炼突破事件链测试完成")
    
    def test_breakthrough_cascade_effects(self):
        """测试突破的级联效应"""
        log_info("开始测试突破级联效应")
        
        # 模拟高级突破（可能引发更多连锁反应）
        major_breakthrough_event = BaseEvent(
            event_type="major_cultivation_breakthrough",
            event_data={
                "character_id": self.test_char.id,
                "old_realm": "练气九层",
                "new_realm": "筑基一层",
                "breakthrough_type": "tribulation",
                "location": self.test_room.id,
                "spiritual_energy_surge": True,
                "heavenly_phenomenon": "thunder_clouds"
            },
            priority="CRITICAL"
        )
        
        # 发布重大突破事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(major_breakthrough_event)
            
            time.sleep(8)  # 等待更长时间处理复杂事件
            
            # 验证级联效应
            # 1. 检查是否触发了天象变化
            tiandao_scripts = search_script("tiandao_director_script")
            if tiandao_scripts:
                tiandao = tiandao_scripts[0]
                if hasattr(tiandao, 'recent_heavenly_phenomena'):
                    phenomena = tiandao.recent_heavenly_phenomena
                    self.assertTrue(phenomena, "重大突破未触发天象变化")
            
            # 2. 检查是否影响了周围环境
            spiritual_energy_level = self.test_room.tags.get(category="spiritual_level")
            # 重大突破应该提升周围灵气浓度
            
            # 3. 检查是否引起了其他角色的关注
            if hasattr(self.test_npc, 'attention_events'):
                attention = self.test_npc.attention_events
                major_events = [e for e in attention if e.get('event_type') == 'major_cultivation_breakthrough']
                self.assertTrue(major_events, "NPC未关注重大突破事件")
        
        log_info("突破级联效应测试完成")


class CombatEventMultiSystemTest(XianxiaIntegrationTest):
    """战斗事件多系统协同测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建战斗测试环境
        self.combat_room = create_test_room("combat_arena")
        self.fighter1 = create_test_character("fighter1", self.combat_room)
        self.fighter2 = create_test_character("fighter2", self.combat_room)
        self.spectator_npc = create_test_npc("spectator_npc", self.combat_room)
        
        # 设置战斗者属性
        self.fighter1.tags.add("筑基三层", category="realm")
        self.fighter2.tags.add("筑基二层", category="realm")
        
        # 设置竞技场环境
        self.combat_room.tags.add("arena", category="location_type")
        self.combat_room.tags.add("neutral", category="spiritual_element")
        
        # 初始化系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_combat_multi_system_coordination(self):
        """测试战斗的多系统协调"""
        log_info("开始测试战斗多系统协调")
        
        # 1. 发起战斗事件
        combat_start_event = BaseEvent(
            event_type="combat_initiated",
            event_data={
                "participants": [self.fighter1.id, self.fighter2.id],
                "location": self.combat_room.id,
                "combat_type": "arena_duel",
                "stakes": "honor",
                "spectators": [self.spectator_npc.id]
            },
            priority="HIGH"
        )
        
        # 开始事件追踪
        trace_id = self.event_tracker.start_trace(combat_start_event)
        
        # 发布战斗开始事件
        event_bus_scripts = search_script("xianxia_event_bus")
        self.assertTrue(event_bus_scripts, "事件总线未找到")
        
        event_bus = event_bus_scripts[0]
        event_bus.publish_event(combat_start_event)
        
        # 2. 模拟战斗过程中的事件
        time.sleep(2)
        
        # 发布战斗技能使用事件
        skill_event = BaseEvent(
            event_type="combat_skill_used",
            event_data={
                "user_id": self.fighter1.id,
                "target_id": self.fighter2.id,
                "skill_name": "烈火剑法",
                "damage": 150,
                "location": self.combat_room.id,
                "element": "fire"
            },
            priority="MEDIUM"
        )
        
        event_bus.publish_event(skill_event)
        time.sleep(2)
        
        # 3. 验证战斗系统响应
        # 检查战斗处理器是否正确处理了战斗事件
        from ..systems.xiuxian_combat_handler import XianxiaCombatHandler
        
        # 4. 验证世界系统响应
        # 检查环境是否因战斗而发生变化
        from ..systems.world_events import world_event_system
        if hasattr(world_event_system, 'recent_combat_effects'):
            combat_effects = world_event_system.recent_combat_effects
            fire_effects = [e for e in combat_effects if e.get('element') == 'fire']
            self.assertTrue(fire_effects, "世界系统未响应火系技能效果")
        
        # 5. 验证NPC系统响应
        # 检查观战NPC是否有反应
        if hasattr(self.spectator_npc, 'combat_observations'):
            observations = self.spectator_npc.combat_observations
            self.assertTrue(observations, "观战NPC未观察到战斗")
        
        # 6. 验证AI导演系统响应
        # 检查地灵导演是否关注了竞技场战斗
        diling_scripts = search_script("diling_director_script")
        if diling_scripts:
            diling = diling_scripts[0]
            if hasattr(diling, 'arena_combat_monitoring'):
                monitoring = diling.arena_combat_monitoring
                self.assertTrue(monitoring, "地灵导演未监控竞技场战斗")
        
        # 7. 验证社交系统响应
        # 战斗结果应该影响参与者的声望
        from ..systems.social_relationship_manager import social_relationship_manager
        if hasattr(social_relationship_manager, 'combat_reputation_changes'):
            rep_changes = social_relationship_manager.combat_reputation_changes
            participant_changes = [c for c in rep_changes if c.get('character_id') in [self.fighter1.id, self.fighter2.id]]
            self.assertTrue(participant_changes, "战斗未影响参与者声望")
        
        # 完成事件追踪
        self.event_tracker.complete_trace(trace_id, True)
        self.assert_event_chain_success(trace_id, timeout=15.0)
        
        log_info("战斗多系统协调测试完成")
    
    def test_combat_outcome_propagation(self):
        """测试战斗结果传播"""
        log_info("开始测试战斗结果传播")
        
        # 模拟战斗结束事件
        combat_end_event = BaseEvent(
            event_type="combat_concluded",
            event_data={
                "winner_id": self.fighter1.id,
                "loser_id": self.fighter2.id,
                "victory_type": "decisive",
                "duration": 300,  # 5分钟
                "location": self.combat_room.id,
                "damage_dealt": {"fighter1": 500, "fighter2": 300},
                "techniques_used": ["烈火剑法", "寒冰掌", "雷电拳"]
            },
            priority="HIGH"
        )
        
        # 发布战斗结束事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(combat_end_event)
            
            time.sleep(5)
            
            # 验证结果传播到各系统
            # 1. 小说生成系统应该记录战斗故事
            novel_scripts = search_script("novel_generator_script")
            if novel_scripts:
                novel_generator = novel_scripts[0]
                if hasattr(novel_generator, 'combat_narratives'):
                    narratives = novel_generator.combat_narratives
                    self.assertTrue(narratives, "小说生成系统未记录战斗故事")
            
            # 2. 社交系统应该更新关系
            from ..systems.social_relationship_manager import social_relationship_manager
            winner_reputation = social_relationship_manager.get_character_reputation(self.fighter1)
            loser_reputation = social_relationship_manager.get_character_reputation(self.fighter2)
            
            # 胜者声望应该提升，败者可能下降
            self.assertIsNotNone(winner_reputation, "胜者声望未更新")
            self.assertIsNotNone(loser_reputation, "败者声望未更新")
        
        log_info("战斗结果传播测试完成")


class WorldChangesPropagationTest(XianxiaIntegrationTest):
    """世界变化传播测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建测试区域
        self.central_room = create_test_room("central_plaza")
        self.nearby_room1 = create_test_room("nearby_location1")
        self.nearby_room2 = create_test_room("nearby_location2")
        
        # 创建测试角色和NPC
        self.test_char = create_test_character("world_test_char", self.central_room)
        self.npc1 = create_test_npc("world_npc1", self.nearby_room1)
        self.npc2 = create_test_npc("world_npc2", self.nearby_room2)
        
        # 设置地点关系
        from ..systems.world_location_manager import world_location_manager
        world_location_manager.establish_location_relationship(
            self.central_room, self.nearby_room1, "adjacent", distance=100
        )
        world_location_manager.establish_location_relationship(
            self.central_room, self.nearby_room2, "adjacent", distance=150
        )
        
        # 初始化系统
        from ..scripts.ai_directors.director_initialization import initialize_director_system
        self.directors = initialize_director_system()
        
        time.sleep(2)
    
    def tearDown(self):
        cleanup_test_objects()
        super().tearDown()
    
    def test_spiritual_energy_surge_propagation(self):
        """测试灵气激增的传播效应"""
        log_info("开始测试灵气激增传播")
        
        # 1. 触发灵气激增事件
        energy_surge_event = BaseEvent(
            event_type="spiritual_energy_surge",
            event_data={
                "epicenter": self.central_room.id,
                "intensity": "extreme",
                "radius": 200,  # 影响半径
                "duration": 3600,  # 持续1小时
                "element": "pure",
                "cause": "ancient_formation_activation"
            },
            priority="CRITICAL"
        )
        
        # 开始事件追踪
        trace_id = self.event_tracker.start_trace(energy_surge_event)
        
        # 发布灵气激增事件
        event_bus_scripts = search_script("xianxia_event_bus")
        self.assertTrue(event_bus_scripts, "事件总线未找到")
        
        event_bus = event_bus_scripts[0]
        event_bus.publish_event(energy_surge_event)
        
        # 2. 等待事件传播
        time.sleep(6)
        
        # 3. 验证中心区域变化
        central_energy = self.central_room.tags.get(category="spiritual_level")
        # 应该提升到极高级别
        
        # 4. 验证邻近区域受影响
        nearby1_energy = self.nearby_room1.tags.get(category="spiritual_level")
        nearby2_energy = self.nearby_room2.tags.get(category="spiritual_level")
        # 邻近区域也应该受到影响，但程度较轻
        
        # 5. 验证角色系统响应
        # 角色应该感受到灵气变化
        if hasattr(self.test_char, 'environmental_awareness'):
            awareness = self.test_char.environmental_awareness
            energy_awareness = [a for a in awareness if a.get('type') == 'spiritual_energy_change']
            self.assertTrue(energy_awareness, "角色未感知到灵气变化")
        
        # 6. 验证NPC系统响应
        # NPC应该对灵气变化有反应
        if hasattr(self.npc1, 'environmental_reactions'):
            reactions = self.npc1.environmental_reactions
            energy_reactions = [r for r in reactions if r.get('trigger') == 'spiritual_energy_surge']
            self.assertTrue(energy_reactions, "NPC未对灵气激增产生反应")
        
        # 7. 验证AI导演系统响应
        # 天道导演应该监控这种重大世界变化
        tiandao_scripts = search_script("tiandao_director_script")
        if tiandao_scripts:
            tiandao = tiandao_scripts[0]
            if hasattr(tiandao, 'world_event_monitoring'):
                monitoring = tiandao.world_event_monitoring
                surge_monitoring = [m for m in monitoring if m.get('event_type') == 'spiritual_energy_surge']
                self.assertTrue(surge_monitoring, "天道导演未监控灵气激增")
        
        # 完成事件追踪
        self.event_tracker.complete_trace(trace_id, True)
        self.assert_event_chain_success(trace_id, timeout=12.0)
        
        log_info("灵气激增传播测试完成")
    
    def test_weather_change_effects(self):
        """测试天气变化效应"""
        log_info("开始测试天气变化效应")
        
        # 触发天气变化事件
        weather_event = BaseEvent(
            event_type="weather_change",
            event_data={
                "region": "test_region",
                "old_weather": "clear",
                "new_weather": "thunderstorm",
                "intensity": "severe",
                "affected_locations": [self.central_room.id, self.nearby_room1.id, self.nearby_room2.id],
                "duration": 1800  # 30分钟
            },
            priority="MEDIUM"
        )
        
        # 发布天气事件
        event_bus_scripts = search_script("xianxia_event_bus")
        if event_bus_scripts:
            event_bus = event_bus_scripts[0]
            event_bus.publish_event(weather_event)
            
            time.sleep(4)
            
            # 验证天气变化的影响
            # 1. 检查环境描述是否更新
            central_weather = self.central_room.tags.get(category="weather")
            # 应该反映雷暴天气
            
            # 2. 检查是否影响了战斗系统
            # 雷暴天气应该增强雷系技能
            
            # 3. 检查NPC行为是否调整
            # NPC可能会寻找避雨场所
            if hasattr(self.npc1, 'weather_behaviors'):
                behaviors = self.npc1.weather_behaviors
                storm_behaviors = [b for b in behaviors if b.get('weather') == 'thunderstorm']
                self.assertTrue(storm_behaviors, "NPC未调整雷暴天气行为")
        
        log_info("天气变化效应测试完成")


if __name__ == '__main__':
    # 运行宏集成测试
    unittest.main(verbosity=2)
