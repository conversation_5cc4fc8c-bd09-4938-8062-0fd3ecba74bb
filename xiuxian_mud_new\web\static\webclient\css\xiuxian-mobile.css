/*
仙侠MUD移动端适配样式

设计理念：
- 移动端优先的响应式设计
- 触控友好的交互元素
- 优化的小屏幕布局
- 手势操作支持

技术特点：
- 媒体查询断点设计
- 触控目标尺寸优化
- 滑动和手势支持
- 性能优化的移动端样式
*/

/* ===== 移动端变量覆盖 ===== */
@media (max-width: 768px) {
    :root {
        /* 移动端专用尺寸 */
        --mobile-touch-target: 44px;      /* 最小触控目标 */
        --mobile-padding: 12px;           /* 移动端内边距 */
        --mobile-margin: 8px;             /* 移动端外边距 */
        --mobile-border-radius: 8px;      /* 移动端圆角 */
        
        /* 移动端字体尺寸 */
        --mobile-font-xs: 12px;
        --mobile-font-sm: 14px;
        --mobile-font-md: 16px;
        --mobile-font-lg: 18px;
        --mobile-font-xl: 20px;
        
        /* 移动端间距 */
        --mobile-space-xs: 4px;
        --mobile-space-sm: 8px;
        --mobile-space-md: 12px;
        --mobile-space-lg: 16px;
        --mobile-space-xl: 24px;
    }
}

/* ===== 平板端适配 (768px - 1024px) ===== */
@media (min-width: 768px) and (max-width: 1024px) {
    /* 平板端布局调整 */
    .webclient-main {
        margin: var(--mobile-margin);
        border-radius: var(--mobile-border-radius);
    }
    
    /* 平板端消息区域 */
    .msg-container {
        margin: var(--mobile-margin);
        padding: var(--mobile-padding);
        font-size: var(--mobile-font-sm);
    }
    
    /* 平板端输入区域 */
    .input-container {
        margin: var(--mobile-margin);
        padding: var(--mobile-padding);
    }
    
    .input-field {
        font-size: var(--mobile-font-md);
        padding: var(--mobile-space-md);
    }
    
    /* 平板端按钮 */
    .btn {
        padding: var(--mobile-space-md) var(--mobile-space-lg);
        font-size: var(--mobile-font-md);
        min-height: var(--mobile-touch-target);
    }
}

/* ===== 手机端适配 (320px - 767px) ===== */
@media (max-width: 767px) {
    /* 基础布局重置 */
    html, body {
        font-size: var(--mobile-font-sm);
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* 主容器移动端布局 */
    .webclient-main,
    #webclient {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }
    
    /* 顶部标题栏移动端优化 */
    .webclient-header {
        padding: var(--mobile-space-md) var(--mobile-space-lg);
        font-size: var(--mobile-font-lg);
        position: sticky;
        top: 0;
        z-index: 100;
        flex-shrink: 0;
    }
    
    /* 消息显示区域移动端优化 */
    .msg-container,
    #messagewindow {
        margin: var(--mobile-space-sm);
        padding: var(--mobile-space-md);
        border-radius: var(--mobile-border-radius);
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 消息行移动端优化 */
    .msg,
    .msg-line {
        padding: var(--mobile-space-sm) var(--mobile-space-md);
        margin: var(--mobile-space-xs) 0;
        font-size: var(--mobile-font-sm);
        line-height: 1.4;
        word-break: break-word;
        hyphens: auto;
    }
    
    /* 特殊消息类型移动端优化 */
    .msg-channel,
    .msg-system,
    .msg-error,
    .msg-success {
        padding-left: var(--mobile-space-lg);
        border-left-width: 2px;
    }
    
    /* AI导演消息移动端优化 */
    .msg-ai-director {
        padding: var(--mobile-space-md);
        margin: var(--mobile-space-md) 0;
        border-radius: var(--mobile-border-radius);
        font-size: var(--mobile-font-sm);
    }
    
    .msg-ai-director::before {
        font-size: var(--mobile-font-sm);
        margin-bottom: var(--mobile-space-xs);
    }
    
    /* 输入区域移动端优化 */
    .input-container,
    #inputcontainer {
        margin: var(--mobile-space-sm);
        padding: var(--mobile-space-md);
        border-radius: var(--mobile-border-radius);
        flex-shrink: 0;
        position: sticky;
        bottom: 0;
        background: var(--xiuxian-bg-secondary);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    }
    
    /* 输入框移动端优化 */
    .input-field,
    #inputfield {
        font-size: var(--mobile-font-md);
        padding: var(--mobile-space-md);
        border-radius: var(--mobile-border-radius);
        min-height: var(--mobile-touch-target);
        -webkit-appearance: none;
        -webkit-border-radius: var(--mobile-border-radius);
    }
    
    /* 防止iOS缩放 */
    .input-field:focus,
    #inputfield:focus {
        font-size: var(--mobile-font-md);
    }
    
    /* 按钮移动端优化 */
    .btn,
    button {
        padding: var(--mobile-space-md) var(--mobile-space-lg);
        font-size: var(--mobile-font-md);
        min-height: var(--mobile-touch-target);
        min-width: var(--mobile-touch-target);
        border-radius: var(--mobile-border-radius);
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* 按钮间距 */
    .btn + .btn {
        margin-left: var(--mobile-space-sm);
    }
    
    /* 连接状态指示器移动端位置 */
    .connection-status {
        top: var(--mobile-space-sm);
        right: var(--mobile-space-sm);
        padding: var(--mobile-space-xs) var(--mobile-space-sm);
        font-size: var(--mobile-font-xs);
        border-radius: 12px;
    }
    
    /* 工具提示移动端优化 */
    .tooltip::after {
        font-size: var(--mobile-font-xs);
        padding: var(--mobile-space-xs) var(--mobile-space-sm);
        border-radius: var(--mobile-space-xs);
    }
}

/* ===== 超小屏幕适配 (320px - 480px) ===== */
@media (max-width: 480px) {
    /* 进一步压缩间距 */
    .msg-container {
        margin: var(--mobile-space-xs);
        padding: var(--mobile-space-sm);
    }
    
    .input-container {
        margin: var(--mobile-space-xs);
        padding: var(--mobile-space-sm);
    }
    
    /* 更小的字体 */
    .msg,
    .msg-line {
        font-size: var(--mobile-font-xs);
        padding: var(--mobile-space-xs) var(--mobile-space-sm);
    }
    
    /* 紧凑的标题栏 */
    .webclient-header {
        padding: var(--mobile-space-sm) var(--mobile-space-md);
        font-size: var(--mobile-font-md);
    }
    
    .webclient-header::before {
        display: none; /* 隐藏emoji节省空间 */
    }
}

/* ===== 触控优化 ===== */

/* 触控反馈 */
.btn:active,
button:active,
.input-field:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* 长按防止选择 */
.btn,
button,
.webclient-header {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* 滚动优化 */
.msg-container,
#messagewindow {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* ===== 手势支持 ===== */

/* 滑动区域 */
.swipe-area {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
}

/* 下拉刷新提示 */
.pull-to-refresh {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--mobile-space-md);
    background: var(--xiuxian-bg-secondary);
    border-radius: var(--mobile-border-radius);
    color: var(--xiuxian-text-secondary);
    font-size: var(--mobile-font-sm);
    opacity: 0;
    transition: opacity var(--xiuxian-transition-fast);
}

.pull-to-refresh.active {
    opacity: 1;
}

/* ===== 移动端专用组件 ===== */

/* 移动端快捷操作栏 */
.mobile-quick-actions {
    display: none;
}

@media (max-width: 767px) {
    .mobile-quick-actions {
        display: flex;
        justify-content: space-around;
        padding: var(--mobile-space-sm);
        background: var(--xiuxian-bg-secondary);
        border-top: 1px solid var(--xiuxian-border);
        position: sticky;
        bottom: 0;
        z-index: 50;
    }
    
    .mobile-quick-actions .btn {
        flex: 1;
        margin: 0 var(--mobile-space-xs);
        padding: var(--mobile-space-sm);
        font-size: var(--mobile-font-xs);
        min-height: 36px;
    }
}

/* 移动端侧边栏 */
.mobile-sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background: var(--xiuxian-bg-primary);
    border-right: 1px solid var(--xiuxian-border);
    z-index: 1000;
    transition: left var(--xiuxian-transition-normal);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.mobile-sidebar.open {
    left: 0;
}

.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--xiuxian-transition-normal);
}

.mobile-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    position: absolute;
    left: var(--mobile-space-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--xiuxian-bg-primary);
    font-size: var(--mobile-font-lg);
    padding: var(--mobile-space-xs);
    cursor: pointer;
}

@media (max-width: 767px) {
    .mobile-menu-btn {
        display: block;
    }
}

/* ===== 性能优化 ===== */

/* 硬件加速 */
.webclient-main,
.msg-container,
.input-container,
.mobile-sidebar {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* 减少重绘 */
@media (max-width: 767px) {
    * {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* 禁用不必要的动画 */
    .gold-glow {
        animation: none;
    }
    
    /* 简化阴影效果 */
    .webclient-main,
    .msg-container,
    .input-container {
        box-shadow: var(--xiuxian-shadow-sm);
    }
}

/* ===== 可访问性支持 ===== */

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --xiuxian-text-primary: #ffffff;
        --xiuxian-bg-primary: #000000;
        --xiuxian-border: #ffffff;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 焦点可见性 */
@media (max-width: 767px) {
    .btn:focus,
    .input-field:focus {
        outline: 2px solid var(--xiuxian-primary);
        outline-offset: 2px;
    }
}
