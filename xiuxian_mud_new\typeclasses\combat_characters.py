"""
仙侠战斗角色类

基于Evennia TurnBattle系统的仙侠战斗角色实现。
集成五行相克、修仙境界、技能系统等仙侠特色功能。
"""

from evennia.contrib.game_systems.turnbattle.tb_basic import TBBasicCharacter
from evennia.utils import logger
from ..systems.tag_property_system import XianxiaTagProperty, TagPropertyQueryManager
from ..systems.event_system import CombatStateEvent, SkillCastEvent, publish_event
import time


class XianxiaCombatCharacter(TBBasicCharacter):
    """
    仙侠战斗角色类
    
    继承TBBasicCharacter，添加仙侠特色功能：
    - 修仙境界影响战斗能力
    - 五行属性和相克计算
    - 仙侠技能系统
    - 灵力和法力管理
    - 与事件总线集成
    """
    
    # 仙侠特色属性（用于战斗计算）
    修为境界 = XianxiaTagProperty(
        category="combat_realm",
        default="练气",
        xianxia_type="cultivation",
        valid_values=TagPropertyQueryManager.REALM_LEVELS
    )
    
    五行属性 = XianxiaTagProperty(
        category="combat_element",
        default="土",
        xianxia_type="elemental",
        valid_values=TagPropertyQueryManager.ELEMENTAL_TYPES
    )
    
    def at_object_creation(self):
        """角色创建时的初始化"""
        super().at_object_creation()
        
        # 初始化仙侠技能
        if not hasattr(self.db, 'xiuxian_skills'):
            self.db.xiuxian_skills = {
                "基础剑法": {
                    "damage": 20,
                    "mp_cost": 5,
                    "element": "金",
                    "cooldown": 0,
                    "requirements": {"realm_level": 0},
                    "description": "最基础的剑法技能",
                    "last_used": 0
                },
                "灵力护体": {
                    "defense": 15,
                    "mp_cost": 8,
                    "element": "土",
                    "duration": 3,
                    "cooldown": 5,
                    "requirements": {"realm_level": 1},
                    "description": "用灵力护体，提升防御",
                    "last_used": 0
                }
            }
        
        # 初始化灵力系统
        if not hasattr(self.db, 'spiritual_power'):
            self.db.spiritual_power = 100
            self.db.max_spiritual_power = 100
        
        # 设置默认TagProperty属性
        if not self.tags.get(category="combat_realm"):
            self.修为境界 = "练气"
        
        if not self.tags.get(category="combat_element"):
            self.五行属性 = "土"
        
        logger.log_info(f"XianxiaCombatCharacter created: {self.key} ({self.修为境界}, {self.五行属性})")
    
    def get_realm_level(self) -> int:
        """
        获取境界等级数值
        
        Returns:
            int: 境界等级（0-9）
        """
        realm_levels = {
            "练气": 0, "筑基": 1, "金丹": 2, "元婴": 3, "化神": 4,
            "炼虚": 5, "合体": 6, "大乘": 7, "渡劫": 8, "仙人": 9
        }
        return realm_levels.get(self.修为境界, 0)
    
    def get_realm_combat_bonus(self) -> float:
        """
        获取境界对战斗的加成
        
        Returns:
            float: 战斗力加成倍数
        """
        realm_level = self.get_realm_level()
        # 每个境界提供10%的基础战斗力加成
        return 1.0 + (realm_level * 0.1)
    
    def calculate_wuxing_bonus(self, skill_element: str, target_element: str) -> float:
        """
        计算五行相克加成
        
        Args:
            skill_element: 技能的五行属性
            target_element: 目标的五行属性
            
        Returns:
            float: 伤害加成倍数
        """
        # 五行相克关系：金克木，木克土，土克水，水克火，火克金
        wuxing_matrix = {
            "金": {"克制": "木", "被克": "火"},
            "木": {"克制": "土", "被克": "金"},
            "水": {"克制": "火", "被克": "土"},
            "火": {"克制": "金", "被克": "水"},
            "土": {"克制": "水", "被克": "木"}
        }
        
        if skill_element in wuxing_matrix:
            relations = wuxing_matrix[skill_element]
            if relations["克制"] == target_element:
                return 1.5  # 相克加成50%
            elif relations["被克"] == target_element:
                return 0.7  # 被克减少30%
        
        return 1.0  # 无相克关系
    
    def use_xiuxian_skill(self, skill_name: str, target=None) -> dict:
        """
        使用仙侠技能
        
        Args:
            skill_name: 技能名称
            target: 目标对象
            
        Returns:
            dict: 技能使用结果
        """
        # 检查技能是否存在
        if skill_name not in self.db.xiuxian_skills:
            return {"success": False, "message": f"未知技能: {skill_name}"}
        
        skill_data = self.db.xiuxian_skills[skill_name]
        current_time = time.time()
        
        # 检查冷却时间
        if current_time - skill_data.get("last_used", 0) < skill_data.get("cooldown", 0):
            remaining = skill_data["cooldown"] - (current_time - skill_data["last_used"])
            return {"success": False, "message": f"技能冷却中，还需 {remaining:.1f} 秒"}
        
        # 检查境界要求
        required_level = skill_data.get("requirements", {}).get("realm_level", 0)
        if self.get_realm_level() < required_level:
            return {"success": False, "message": "修为不足，无法使用此技能"}
        
        # 检查灵力消耗
        mp_cost = skill_data.get("mp_cost", 0)
        if self.db.spiritual_power < mp_cost:
            return {"success": False, "message": "灵力不足"}
        
        # 消耗灵力
        self.db.spiritual_power -= mp_cost
        
        # 更新最后使用时间
        skill_data["last_used"] = current_time
        
        # 计算技能效果
        result = self._calculate_skill_effect(skill_data, target)
        
        # 发布技能使用事件
        self._publish_skill_event(skill_name, skill_data, target, result)
        
        return result
    
    def _calculate_skill_effect(self, skill_data: dict, target=None) -> dict:
        """
        计算技能效果
        
        Args:
            skill_data: 技能数据
            target: 目标对象
            
        Returns:
            dict: 技能效果结果
        """
        result = {"success": True, "effects": []}
        
        # 境界加成
        realm_bonus = self.get_realm_combat_bonus()
        
        # 伤害技能
        if "damage" in skill_data and target:
            base_damage = skill_data["damage"]
            
            # 五行相克计算
            skill_element = skill_data.get("element", "无")
            target_element = getattr(target, "五行属性", "土")
            wuxing_bonus = self.calculate_wuxing_bonus(skill_element, target_element)
            
            # 最终伤害
            final_damage = int(base_damage * realm_bonus * wuxing_bonus)
            
            result["effects"].append({
                "type": "damage",
                "value": final_damage,
                "target": target.key if target else None,
                "element": skill_element,
                "wuxing_bonus": wuxing_bonus
            })
        
        # 防御技能
        if "defense" in skill_data:
            defense_bonus = int(skill_data["defense"] * realm_bonus)
            duration = skill_data.get("duration", 1)
            
            result["effects"].append({
                "type": "defense",
                "value": defense_bonus,
                "duration": duration
            })
        
        return result
    
    def _publish_skill_event(self, skill_name: str, skill_data: dict, target, result: dict):
        """
        发布技能使用事件
        
        Args:
            skill_name: 技能名称
            skill_data: 技能数据
            target: 目标对象
            result: 技能效果结果
        """
        event = SkillCastEvent(
            source_id=str(self.id),
            target_id=str(target.id) if target else None,
            data={
                "skill_name": skill_name,
                "skill_element": skill_data.get("element", "无"),
                "caster_realm": self.修为境界,
                "effects": result.get("effects", []),
                "mp_cost": skill_data.get("mp_cost", 0)
            }
        )
        
        publish_event(event)
    
    def check_skill_requirements(self, skill_name: str) -> dict:
        """
        检查技能使用要求
        
        Args:
            skill_name: 技能名称
            
        Returns:
            dict: 检查结果
        """
        if skill_name not in self.db.xiuxian_skills:
            return {"can_use": False, "reason": "技能不存在"}
        
        skill_data = self.db.xiuxian_skills[skill_name]
        current_time = time.time()
        
        # 检查冷却
        if current_time - skill_data.get("last_used", 0) < skill_data.get("cooldown", 0):
            remaining = skill_data["cooldown"] - (current_time - skill_data["last_used"])
            return {"can_use": False, "reason": f"冷却中 ({remaining:.1f}s)"}
        
        # 检查境界
        required_level = skill_data.get("requirements", {}).get("realm_level", 0)
        if self.get_realm_level() < required_level:
            return {"can_use": False, "reason": "修为不足"}
        
        # 检查灵力
        mp_cost = skill_data.get("mp_cost", 0)
        if self.db.spiritual_power < mp_cost:
            return {"can_use": False, "reason": "灵力不足"}
        
        return {"can_use": True, "reason": "可以使用"}
    
    def get_available_skills(self) -> list:
        """
        获取可用技能列表

        Returns:
            list: 可用技能信息
        """
        available_skills = []

        for skill_name, skill_data in self.db.xiuxian_skills.items():
            check_result = self.check_skill_requirements(skill_name)
            
            skill_info = {
                "name": skill_name,
                "description": skill_data.get("description", ""),
                "element": skill_data.get("element", "无"),
                "mp_cost": skill_data.get("mp_cost", 0),
                "cooldown": skill_data.get("cooldown", 0),
                "can_use": check_result["can_use"],
                "reason": check_result["reason"]
            }
            
            available_skills.append(skill_info)
        
        return available_skills
    
    def restore_spiritual_power(self, amount: int = None):
        """
        恢复灵力

        Args:
            amount: 恢复数量，None表示完全恢复
        """
        if amount is None:
            self.db.spiritual_power = self.db.max_spiritual_power
        else:
            self.db.spiritual_power = min(
                self.db.spiritual_power + amount,
                self.db.max_spiritual_power
            )

    def get_combat_tags(self) -> Dict[str, str]:
        """
        获取战斗相关的TagProperty标签

        Returns:
            Dict[str, str]: 战斗标签字典
        """
        return {
            "realm": self.修为境界,
            "element": self.五行属性,
            "profession": getattr(self, '职业类型', '散修'),
            "sect": getattr(self, '门派', '无门派')
        }

    def update_combat_tags(self, **kwargs):
        """
        批量更新战斗相关标签

        Args:
            **kwargs: 要更新的标签键值对
        """
        if "realm" in kwargs:
            self.修为境界 = kwargs["realm"]
        if "element" in kwargs:
            self.五行属性 = kwargs["element"]
        if "profession" in kwargs:
            setattr(self, '职业类型', kwargs["profession"])
        if "sect" in kwargs:
            setattr(self, '门派', kwargs["sect"])

    def get_combat_performance_stats(self) -> Dict:
        """
        获取战斗性能统计

        Returns:
            Dict: 性能统计信息
        """
        return {
            "tag_queries": getattr(self, '_tag_query_count', 0),
            "skill_uses": getattr(self, '_skill_use_count', 0),
            "wuxing_calculations": getattr(self, '_wuxing_calc_count', 0),
            "last_combat_time": getattr(self, '_last_combat_time', 0)
        }
