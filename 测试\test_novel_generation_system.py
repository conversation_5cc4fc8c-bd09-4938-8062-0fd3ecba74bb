"""
小说生成系统测试套件

测试小说生成系统的各个组件和功能。
"""

import unittest
import sys
import os
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 模拟Evennia环境
class MockDefaultScript:
    def __init__(self):
        self.attributes = MockAttributes()
        self.key = ""
        self.desc = ""
        self.interval = 0
        self.persistent = False
        self.start_delay = False
        
class MockAttributes:
    def __init__(self):
        self._data = {}
        
    def add(self, key, value, category=None):
        self._data[key] = value
        
    def get(self, key, default=None):
        return self._data.get(key, default)
        
    def set(self, key, value):
        self._data[key] = value

# 模拟导入
sys.modules['evennia'] = Mock()
sys.modules['evennia.scripts'] = Mock()
sys.modules['evennia.scripts.defaultscript'] = Mock()
sys.modules['evennia.scripts.defaultscript'].DefaultScript = MockDefaultScript
sys.modules['evennia.utils'] = Mock()
sys.modules['evennia.utils.logger'] = Mock()

# 导入测试目标
from xiuxian_mud_new.scripts.novel_generator_script import NovelGeneratorScript, NovelEventHandler
from xiuxian_mud_new.systems.novel_event_collector import NovelEventCollector, EventRecord, EventSignificance
from xiuxian_mud_new.systems.narrative_context_manager import NarrativeContextManager
from xiuxian_mud_new.systems.novel_content_generator import NovelContentGenerator


class TestNovelEventCollector(unittest.TestCase):
    """测试事件收集器"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_script = Mock()
        self.mock_script.attributes = MockAttributes()
        self.collector = NovelEventCollector(self.mock_script)
        
    def test_collect_event_basic(self):
        """测试基本事件收集"""
        event_data = {
            "type": "CultivationBreakthroughEvent",
            "description": "玩家突破到筑基期",
            "participants": ["张三"],
            "location": "修炼室",
            "timestamp": time.time()
        }
        
        result = self.collector.collect_event(event_data)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.event_type, "CultivationBreakthroughEvent")
        self.assertEqual(result.participants, ["张三"])
        self.assertGreater(result.significance, 0.0)
        
    def test_evaluate_significance_high(self):
        """测试高重要性事件评估"""
        event_data = {
            "type": "CultivationBreakthroughEvent",
            "description": "突破到金丹期",
            "participants": ["张三", "李四"],
            "location": "天机峰",
            "impact_scope": "sect"
        }
        
        significance = self.collector.evaluate_significance(event_data)
        
        # 修为突破 + 多人参与 + 重要地点 + 门派影响
        self.assertGreaterEqual(significance, 0.7)
        
    def test_evaluate_significance_low(self):
        """测试低重要性事件评估"""
        event_data = {
            "type": "DailyTaskEvent",
            "description": "完成日常任务",
            "participants": ["张三"],
            "location": "任务大厅"
        }
        
        significance = self.collector.evaluate_significance(event_data)
        
        # 日常任务应该是低重要性
        self.assertLess(significance, 0.5)
        
    def test_generate_narrative_tags(self):
        """测试叙事标签生成"""
        event_data = {
            "type": "CombatVictoryEvent",
            "description": "击败强敌",
            "participants": ["张三"],
            "location": "魔兽森林"
        }
        
        tags = self.collector._generate_narrative_tags(event_data)
        
        self.assertIn("战斗", tags)
        self.assertIn("胜利", tags)
        self.assertTrue(len(tags) > 0)


class TestNarrativeContextManager(unittest.TestCase):
    """测试叙事上下文管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_script = Mock()
        self.mock_script.attributes = MockAttributes()
        
        # 模拟一些事件记录
        self.mock_script.attributes.add("event_records", [
            {
                "event_type": "CultivationBreakthroughEvent",
                "participants": ["张三"],
                "timestamp": time.time() - 3600,
                "significance": 0.8,
                "narrative_tags": ["修炼", "突破"]
            },
            {
                "event_type": "SectJoinEvent", 
                "participants": ["李四"],
                "timestamp": time.time() - 7200,
                "significance": 0.6,
                "narrative_tags": ["门派", "加入"]
            }
        ])
        
        self.manager = NarrativeContextManager(self.mock_script)
        
    def test_build_narrative_context(self):
        """测试叙事上下文构建"""
        context = self.manager.build_narrative_context()
        
        self.assertIn("recent_events", context)
        self.assertIn("world_state", context)
        self.assertIn("character_arcs", context)
        self.assertIn("current_themes", context)
        self.assertIn("plot_trends", context)
        
    def test_analyze_character_arcs(self):
        """测试角色发展分析"""
        character_arcs = self.manager._analyze_character_arcs()
        
        # 应该包含张三的发展轨迹
        self.assertIn("张三", character_arcs)
        
        zhang_arc = character_arcs["张三"]
        self.assertIn("development_stage", zhang_arc)
        self.assertIn("growth_trend", zhang_arc)
        self.assertIn("character_themes", zhang_arc)
        
    def test_identify_current_themes(self):
        """测试主题识别"""
        recent_events = [
            {"narrative_tags": ["修炼", "突破"]},
            {"narrative_tags": ["门派", "加入"]}
        ]
        
        themes = self.manager._identify_current_themes(recent_events)
        
        self.assertIn("修炼成长", themes)
        self.assertTrue(len(themes) > 0)
        
    def test_analyze_plot_trends(self):
        """测试情节趋势分析"""
        recent_events = [
            {"significance": 0.8, "timestamp": time.time() - 3600},
            {"significance": 0.6, "timestamp": time.time() - 7200}
        ]
        
        trends = self.manager._analyze_plot_trends(recent_events)
        
        self.assertIn("trend", trends)
        self.assertIn("intensity", trends)
        self.assertIn("direction", trends)


class TestNovelContentGenerator(unittest.TestCase):
    """测试内容生成器"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_script = Mock()
        self.mock_script.attributes = MockAttributes()
        self.mock_script.attributes.add("generated_chapters", [])
        
        self.generator = NovelContentGenerator(self.mock_script)
        
    def test_generate_chapter_fallback(self):
        """测试备用章节生成"""
        context = {
            "recent_events": [
                {
                    "description": "张三突破到筑基期",
                    "participants": ["张三"]
                }
            ],
            "world_mood": "宁静祥和",
            "current_themes": ["修炼成长"]
        }
        
        chapter = self.generator._generate_chapter_fallback(context)
        
        self.assertIsNotNone(chapter)
        self.assertIn("第1章", chapter)
        self.assertIn("张三", chapter)
        self.assertTrue(len(chapter) > 100)
        
    def test_generate_event_narrative_fallback(self):
        """测试备用事件叙事生成"""
        event_data = {
            "type": "CombatVictoryEvent",
            "description": "击败强敌",
            "participants": ["张三"],
            "location": "魔兽森林"
        }
        
        narrative = self.generator._generate_event_narrative_fallback(event_data)
        
        self.assertIsNotNone(narrative)
        self.assertIn("张三", narrative)
        self.assertIn("魔兽森林", narrative)
        self.assertTrue(len(narrative) > 50)
        
    def test_generate_dialogue_fallback(self):
        """测试备用对话生成"""
        scene_context = {
            "description": "修仙者相遇",
            "mood": "友好"
        }
        
        character_info = {
            "characters": [
                {"name": "张三", "personality": "沉稳"},
                {"name": "李四", "personality": "活泼"}
            ]
        }
        
        dialogue = self.generator._generate_dialogue_fallback(scene_context, character_info)
        
        self.assertIsNotNone(dialogue)
        self.assertIn("张三", dialogue)
        self.assertIn("李四", dialogue)
        self.assertTrue(len(dialogue) > 50)
        
    def test_post_process_content(self):
        """测试内容后处理"""
        raw_content = """
        
        这是一段测试内容。
        
        
        包含多余的空行。
        
        
        """
        
        processed = self.generator._post_process_content(raw_content)
        
        # 应该清理多余空行
        self.assertNotIn("\n\n\n", processed)
        self.assertTrue(processed.strip())


class TestNovelGeneratorScript(unittest.TestCase):
    """测试小说生成脚本"""
    
    def setUp(self):
        """测试前准备"""
        with patch('xiuxian_mud_new.scripts.novel_generator_script.XianxiaEventBus'):
            self.script = NovelGeneratorScript()
            
    def test_script_initialization(self):
        """测试脚本初始化"""
        self.assertEqual(self.script.key, "novel_generator")
        self.assertTrue(self.script.persistent)
        self.assertIsNotNone(self.script.attributes)
        
    def test_should_generate_content(self):
        """测试内容生成条件判断"""
        # 设置配置
        self.script.attributes.set("generator_config", {
            "auto_generation": True,
            "max_chapters": 50
        })
        
        # 设置章节数量
        self.script.attributes.set("generated_chapters", [])
        
        result = self.script.should_generate_content()
        self.assertTrue(result)
        
    def test_manual_generate(self):
        """测试手动生成"""
        with patch.object(self.script, 'generate_novel_chapter') as mock_generate:
            mock_generate.return_value = "测试章节内容"
            
            result = self.script.manual_generate()
            
            self.assertEqual(result, "测试章节内容")
            mock_generate.assert_called_once()
            
    def test_get_status(self):
        """测试状态获取"""
        status = self.script.get_status()
        
        self.assertIn("system_status", status)
        self.assertIn("total_chapters", status)
        self.assertIn("total_events", status)
        self.assertIn("subsystems", status)
        
    def test_update_config(self):
        """测试配置更新"""
        new_config = {"auto_generation": False}
        
        self.script.update_config(new_config)
        
        config = self.script.attributes.get("generator_config", {})
        self.assertFalse(config.get("auto_generation"))


class TestNovelEventHandler(unittest.TestCase):
    """测试小说事件处理器"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_script = Mock()
        self.handler = NovelEventHandler(self.mock_script)
        
    def test_handle_event(self):
        """测试事件处理"""
        mock_event = Mock()
        mock_event.event_type = "CultivationBreakthroughEvent"
        mock_event.timestamp = time.time()
        mock_event.event_id = "test_event_123"
        mock_event.priority = Mock()
        mock_event.priority.value = "HIGH"
        
        # 添加事件属性
        mock_event.description = "测试突破事件"
        mock_event.participants = ["张三"]
        mock_event.location = "修炼室"
        mock_event.significance = 0.8
        
        result = self.handler.handle_event(mock_event)
        
        self.assertTrue(result)
        self.mock_script.on_event_trigger.assert_called_once()
        
    def test_can_handle_important_event(self):
        """测试重要事件处理判断"""
        mock_event = Mock()
        mock_event.event_type = "CultivationBreakthroughEvent"
        mock_event.priority = Mock()
        mock_event.priority.value = "HIGH"
        
        result = self.handler.can_handle(mock_event)
        self.assertTrue(result)
        
    def test_can_handle_unimportant_event(self):
        """测试不重要事件处理判断"""
        mock_event = Mock()
        mock_event.event_type = "DailyTaskEvent"
        mock_event.priority = Mock()
        mock_event.priority.value = "LOW"
        
        result = self.handler.can_handle(mock_event)
        self.assertFalse(result)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
