"""
天道导演 - 世界级事件管理

负责修仙界的宏观事件和世界格局：
- 世界动态事件系统（天灾、仙界异象、跨域冲突）
- 重大历史进程（门派兴衰、修仙界格局变化）
- 跨区域事件协调（门派战争、联盟结盟）
- 世界级资源分配（灵石矿脉、仙草产出）
"""

import time
import random
from typing import Dict, Any, List, Optional

from evennia.utils import logger
from evennia.utils.search import search_object

from .base_director import BaseDirector, DirectorEventTypes
from systems.ai_decision_engine import AIDecisionEngine, DecisionContext, DirectorType
from systems.query_interfaces import AIDirectorQueryInterface
from systems.event_system import EventPriority
from systems.world_events_system import WorldEventsSystem


class TiandaoDirector(BaseDirector):
    """
    天道导演 - 负责世界级事件管理
    
    决策周期：5分钟
    职责范围：世界宏观事件、历史进程、跨区域协调
    """
    
    def at_script_creation(self):
        """天道导演初始化"""
        self.director_type = "tiandao"
        self.decision_interval = 300  # 5分钟
        
        # 初始化世界状态
        self.db.world_state = {
            "global_spiritual_energy": 1.0,
            "sect_power_balance": {
                "青云门": 1.0,
                "天音寺": 0.9,
                "焚香谷": 0.8,
                "合欢派": 0.7,
                "万毒门": 0.6,
                "鬼王宗": 0.5
            },
            "major_events_history": [],
            "cross_regional_conflicts": [],
            "world_timeline": {
                "current_era": "修仙盛世",
                "major_events_count": 0,
                "last_major_event": None
            },
            "global_resources": {
                "spiritual_stones": 1.0,
                "rare_herbs": 1.0,
                "ancient_artifacts": 1.0
            }
        }
        
        # 世界事件配置
        self.db.world_events_config = {
            "celestial_anomaly_chance": 0.1,  # 天象异常概率
            "cross_sect_conflict_chance": 0.05,  # 跨门派冲突概率
            "ancient_relic_discovery_chance": 0.03,  # 上古遗迹发现概率
            "spiritual_tide_major_change_chance": 0.08,  # 重大灵气变化概率
            "immortal_intervention_chance": 0.01  # 仙人干预概率
        }
        
        super().at_script_creation()
        logger.log_info("天道导演已创建 - 掌管修仙界宏观格局")
    
    def collect_context_data(self) -> Dict[str, Any]:
        """收集世界级决策所需的上下文数据"""
        try:
            # 获取世界状态摘要
            world_summary = AIDirectorQueryInterface.get_world_state_summary()
            
            # 获取所有在线玩家信息
            online_players = self._get_online_players_info()
            
            # 获取门派势力分析
            sect_analysis = self._analyze_sect_power()
            
            # 获取最近的重大事件
            recent_major_events = self._get_recent_major_events()
            
            # 分析世界平衡状态
            balance_analysis = self._analyze_world_balance()
            
            context_data = {
                "world_summary": world_summary,
                "online_players": online_players,
                "sect_analysis": sect_analysis,
                "recent_major_events": recent_major_events,
                "balance_analysis": balance_analysis,
                "world_state": self.db.world_state,
                "events_config": self.db.world_events_config,
                "current_time": time.time(),
                "game_time": self._get_game_time()
            }
            
            return context_data
            
        except Exception as e:
            logger.log_err(f"天道导演收集上下文数据失败: {e}")
            return {}
    
    def make_ai_decision(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基于世界状态进行天道级AI决策"""
        try:
            # 创建决策上下文
            decision_context = DecisionContext(
                director_type=DirectorType.TIANDAO,
                timestamp=time.time(),
                world_state=context.get("world_state", {}),
                recent_events=context.get("recent_major_events", []),
                performance_metrics=self.get_performance_stats(),
                custom_data={
                    "sect_analysis": context.get("sect_analysis", {}),
                    "balance_analysis": context.get("balance_analysis", {}),
                    "online_players": len(context.get("online_players", []))
                }
            )
            
            # 获取AI决策引擎
            ai_engine = self.get_ai_decision_engine()
            if not ai_engine:
                return self._fallback_decision(context)
            
            # 进行AI决策
            decision_result = ai_engine.make_decision(decision_context)
            
            if decision_result:
                return {
                    "decision_type": decision_result.decision_type,
                    "priority": decision_result.priority,
                    "actions": decision_result.actions,
                    "effects": decision_result.effects,
                    "duration": decision_result.duration,
                    "conditions": decision_result.conditions,
                    "confidence": decision_result.confidence,
                    "reasoning": decision_result.reasoning
                }
            
            return None
            
        except Exception as e:
            logger.log_err(f"天道导演AI决策失败: {e}")
            return self._fallback_decision(context)
    
    def execute_decision(self, decision: Dict[str, Any]):
        """执行天道级决策"""
        try:
            decision_type = decision.get("decision_type", "unknown")
            actions = decision.get("actions", [])
            effects = decision.get("effects", {})
            
            logger.log_info(f"天道导演执行决策: {decision_type}")
            logger.log_info(f"决策理由: {decision.get('reasoning', '未知')}")
            
            # 执行具体行动
            for action in actions:
                self._execute_action(action)
            
            # 应用世界效果
            self._apply_world_effects(effects)
            
            # 记录重大事件
            self._record_major_event(decision)
            
            # 发布天道事件到事件总线
            self.publish_director_event(
                DirectorEventTypes.WORLD_EVENT_TRIGGERED,
                {
                    "decision": decision,
                    "timestamp": time.time(),
                    "world_state": self.db.world_state
                },
                EventPriority.HIGH
            )
            
        except Exception as e:
            logger.log_err(f"天道导演执行决策失败: {e}")
    
    def _execute_action(self, action: Dict[str, Any]):
        """执行具体的天道行动"""
        action_type = action.get("type", "unknown")
        details = action.get("details", {})
        
        if action_type == "trigger_celestial_event":
            self._trigger_celestial_event(details)
        elif action_type == "initiate_cross_sect_conflict":
            self._initiate_cross_sect_conflict(details)
        elif action_type == "reveal_ancient_relic":
            self._reveal_ancient_relic(details)
        elif action_type == "adjust_global_spiritual_energy":
            self._adjust_global_spiritual_energy(details)
        elif action_type == "immortal_intervention":
            self._immortal_intervention(details)
        else:
            logger.log_warn(f"未知的天道行动类型: {action_type}")
    
    def _trigger_celestial_event(self, details: Dict[str, Any]):
        """触发天象事件"""
        event_name = details.get("event_name", "天象异常")
        duration = details.get("duration", 3600)
        intensity = details.get("intensity", 1.2)

        logger.log_info(f"天道触发天象事件: {event_name}，强度{intensity}，持续{duration}秒")

        # 集成世界事件系统
        world_events_system = self._get_world_events_system()
        if world_events_system:
            # 根据事件类型生成对应的世界事件
            from systems.world_events_system import WorldEventType, WorldEventSeverity

            event_type_mapping = {
                "天象异常": WorldEventType.CELESTIAL_ANOMALY,
                "上古遗迹": WorldEventType.ANCIENT_RELIC_DISCOVERY,
                "仙人干预": WorldEventType.IMMORTAL_INTERVENTION,
                "灵气大潮": WorldEventType.SPIRITUAL_TIDE_MAJOR
            }

            world_event_type = event_type_mapping.get(event_name, WorldEventType.CELESTIAL_ANOMALY)
            severity = WorldEventSeverity.MAJOR if intensity > 1.5 else WorldEventSeverity.MODERATE

            world_events_system.generate_world_event(world_event_type, severity, forced=True)

        # 通知地灵导演处理天象变化
        self.publish_director_event(
            DirectorEventTypes.CELESTIAL_EVENT,
            {
                "event_name": event_name,
                "duration": duration,
                "intensity": intensity,
                "source": "tiandao",
                "details": details
            },
            EventPriority.HIGH
        )
    
    def _initiate_cross_sect_conflict(self, details: Dict[str, Any]):
        """发起跨门派冲突"""
        sect1 = details.get("sect1", "青云门")
        sect2 = details.get("sect2", "鬼王宗")
        conflict_type = details.get("conflict_type", "领土争端")
        
        logger.log_info(f"天道发起跨门派冲突: {sect1} vs {sect2} - {conflict_type}")
        
        # 更新门派势力平衡
        if sect1 in self.db.world_state["sect_power_balance"]:
            self.db.world_state["sect_power_balance"][sect1] *= 0.95
        if sect2 in self.db.world_state["sect_power_balance"]:
            self.db.world_state["sect_power_balance"][sect2] *= 0.95
        
        # 记录冲突
        conflict_record = {
            "timestamp": time.time(),
            "sect1": sect1,
            "sect2": sect2,
            "conflict_type": conflict_type,
            "status": "ongoing"
        }
        self.db.world_state["cross_regional_conflicts"].append(conflict_record)
    
    def _reveal_ancient_relic(self, details: Dict[str, Any]):
        """揭示上古遗迹"""
        relic_name = details.get("relic_name", "上古仙府")
        location = details.get("location", "未知秘境")
        
        logger.log_info(f"天道揭示上古遗迹: {relic_name} 于 {location}")
        
        # 增加世界级资源
        self.db.world_state["global_resources"]["ancient_artifacts"] *= 1.1
    
    def _adjust_global_spiritual_energy(self, details: Dict[str, Any]):
        """调整全球灵气水平"""
        adjustment = details.get("adjustment", 1.0)
        reason = details.get("reason", "天道调节")
        
        old_energy = self.db.world_state["global_spiritual_energy"]
        self.db.world_state["global_spiritual_energy"] *= adjustment
        
        logger.log_info(f"天道调整全球灵气: {old_energy:.2f} -> {self.db.world_state['global_spiritual_energy']:.2f} ({reason})")
    
    def _immortal_intervention(self, details: Dict[str, Any]):
        """仙人干预事件"""
        immortal_name = details.get("immortal_name", "神秘仙人")
        intervention_type = details.get("intervention_type", "指点迷津")
        
        logger.log_info(f"仙人干预: {immortal_name} - {intervention_type}")
        
        # 仙人干预通常会带来重大变化
        self.db.world_state["global_spiritual_energy"] *= 1.05
    
    def _apply_world_effects(self, effects: Dict[str, Any]):
        """应用世界级效果"""
        for effect_type, value in effects.items():
            if effect_type == "global_spiritual_energy":
                self.db.world_state["global_spiritual_energy"] *= value
            elif effect_type.startswith("sect_power_"):
                sect_name = effect_type.replace("sect_power_", "")
                if sect_name in self.db.world_state["sect_power_balance"]:
                    self.db.world_state["sect_power_balance"][sect_name] *= value
    
    def _record_major_event(self, decision: Dict[str, Any]):
        """记录重大事件到历史"""
        event_record = {
            "timestamp": time.time(),
            "decision_type": decision.get("decision_type"),
            "reasoning": decision.get("reasoning"),
            "effects": decision.get("effects"),
            "confidence": decision.get("confidence")
        }
        
        history = self.db.world_state["major_events_history"]
        history.append(event_record)
        
        # 保留最近100个重大事件
        if len(history) > 100:
            self.db.world_state["major_events_history"] = history[-100:]
        
        # 更新时间线
        self.db.world_state["world_timeline"]["major_events_count"] += 1
        self.db.world_state["world_timeline"]["last_major_event"] = time.time()
    
    def _get_online_players_info(self) -> List[Dict[str, Any]]:
        """获取在线玩家信息"""
        try:
            players = search_object(typeclass="typeclasses.characters.Character")
            online_players = []
            
            for player in players:
                if player.has_account and player.sessions.all():
                    player_info = {
                        "name": player.key,
                        "location": player.location.key if player.location else "未知",
                        "realm": getattr(player, "修为境界", "练气"),
                        "sect": getattr(player, "门派归属", "无门派")
                    }
                    online_players.append(player_info)
            
            return online_players
            
        except Exception as e:
            logger.log_err(f"获取在线玩家信息失败: {e}")
            return []
    
    def _analyze_sect_power(self) -> Dict[str, Any]:
        """分析门派势力"""
        sect_balance = self.db.world_state["sect_power_balance"]
        
        # 计算势力分析
        total_power = sum(sect_balance.values())
        strongest_sect = max(sect_balance.items(), key=lambda x: x[1])
        weakest_sect = min(sect_balance.items(), key=lambda x: x[1])
        
        return {
            "total_power": total_power,
            "strongest_sect": strongest_sect[0],
            "strongest_power": strongest_sect[1],
            "weakest_sect": weakest_sect[0],
            "weakest_power": weakest_sect[1],
            "power_balance": sect_balance,
            "balance_ratio": strongest_sect[1] / weakest_sect[1] if weakest_sect[1] > 0 else float('inf')
        }
    
    def _get_recent_major_events(self) -> List[Dict[str, Any]]:
        """获取最近的重大事件"""
        history = self.db.world_state["major_events_history"]
        
        # 返回最近10个事件
        return history[-10:] if len(history) > 10 else history
    
    def _analyze_world_balance(self) -> Dict[str, Any]:
        """分析世界平衡状态"""
        world_state = self.db.world_state
        
        return {
            "spiritual_energy_level": world_state["global_spiritual_energy"],
            "sect_balance_variance": self._calculate_sect_variance(),
            "recent_conflicts": len(world_state["cross_regional_conflicts"]),
            "resource_abundance": sum(world_state["global_resources"].values()) / len(world_state["global_resources"]),
            "stability_score": self._calculate_stability_score()
        }
    
    def _calculate_sect_variance(self) -> float:
        """计算门派势力方差"""
        powers = list(self.db.world_state["sect_power_balance"].values())
        if not powers:
            return 0.0
        
        mean_power = sum(powers) / len(powers)
        variance = sum((p - mean_power) ** 2 for p in powers) / len(powers)
        
        return variance
    
    def _calculate_stability_score(self) -> float:
        """计算世界稳定性评分"""
        # 基于多个因素计算稳定性
        spiritual_stability = min(1.0, self.db.world_state["global_spiritual_energy"])
        sect_stability = 1.0 / (1.0 + self._calculate_sect_variance())
        conflict_stability = max(0.0, 1.0 - len(self.db.world_state["cross_regional_conflicts"]) * 0.1)
        
        return (spiritual_stability + sect_stability + conflict_stability) / 3.0

    def _get_world_events_system(self):
        """获取世界事件系统"""
        try:
            from systems.world_events_system import get_world_events_system
            return get_world_events_system()
        except ImportError:
            logger.log_warn("世界事件系统未找到")
            return None

    def _get_game_time(self) -> str:
        """获取游戏时间"""
        # 简化的游戏时间系统
        real_time = time.time()
        game_days = int(real_time / 86400) % 365  # 一年365天
        
        seasons = ["春", "夏", "秋", "冬"]
        season = seasons[(game_days // 91) % 4]
        
        return f"{season}季第{game_days % 91 + 1}天"
    
    def _fallback_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """AI不可用时的备用决策"""
        # 简单的规则基础决策
        balance_analysis = context.get("balance_analysis", {})
        stability_score = balance_analysis.get("stability_score", 0.5)
        
        if stability_score < 0.3:
            return {
                "decision_type": "stability_intervention",
                "priority": "high",
                "actions": [
                    {
                        "type": "adjust_global_spiritual_energy",
                        "details": {"adjustment": 1.1, "reason": "稳定性干预"}
                    }
                ],
                "effects": {"global_spiritual_energy": 1.1},
                "duration": 1800,
                "conditions": ["世界稳定性过低"],
                "confidence": 0.6,
                "reasoning": "检测到世界稳定性过低，进行天道干预以恢复平衡"
            }
        
        return {
            "decision_type": "maintain_status",
            "priority": "low",
            "actions": [],
            "effects": {},
            "duration": 300,
            "conditions": [],
            "confidence": 0.5,
            "reasoning": "世界状态稳定，维持现状"
        }
