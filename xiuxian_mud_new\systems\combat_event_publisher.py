"""
战斗事件发布器

专门处理战斗相关事件的发布和管理。
与事件总线集成，支持AI导演和小说生成系统。
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from evennia.utils import logger
from .event_system import (
    BaseEvent, CombatStateEvent, SkillCastEvent, WuxingInteractionEvent,
    CombatStartEvent, CombatEndEvent, EventPriority, publish_event
)
import time


@dataclass
class CombatStartEvent(BaseEvent):
    """战斗开始事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class CombatEndEvent(BaseEvent):
    """战斗结束事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class CombatRoundEvent(BaseEvent):
    """战斗回合事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.NORMAL


@dataclass
class CombatActionEvent(BaseEvent):
    """战斗行动事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.NORMAL


@dataclass
class WuxingInteractionEvent(BaseEvent):
    """五行相克事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


@dataclass
class RealmDifferenceEvent(BaseEvent):
    """境界差异事件"""
    
    def __post_init__(self):
        super().__post_init__()
        self.priority = EventPriority.HIGH


class CombatEventPublisher:
    """
    战斗事件发布器
    
    功能：
    - 发布各种战斗事件
    - 事件数据格式化
    - 事件优先级管理
    - 与AI导演系统集成
    - 支持小说生成系统
    """
    
    def __init__(self):
        """初始化战斗事件发布器"""
        self.published_events = []
        self.event_stats = {
            "total_published": 0,
            "by_type": {},
            "by_priority": {}
        }
        logger.log_info("CombatEventPublisher initialized")
    
    def publish_combat_start(self, combatants: List, location=None, combat_type: str = "xiuxian") -> bool:
        """
        发布战斗开始事件
        
        Args:
            combatants: 战斗参与者列表
            location: 战斗地点
            combat_type: 战斗类型
            
        Returns:
            bool: 发布是否成功
        """
        # 格式化参战者数据
        combatants_data = []
        for combatant in combatants:
            combatant_data = {
                "id": str(combatant.id),
                "name": combatant.key,
                "realm": getattr(combatant, '修为境界', '练气'),
                "element": getattr(combatant, '五行属性', '土'),
                "profession": getattr(combatant, '职业类型', '散修'),
                "hp": getattr(combatant, 'hp', 100),
                "max_hp": getattr(combatant, 'max_hp', 100),
                "spiritual_power": getattr(combatant, 'db', {}).get('spiritual_power', 100)
            }
            combatants_data.append(combatant_data)
        
        # 创建事件
        event = CombatStartEvent(
            data={
                "combatants": combatants_data,
                "location": str(location.id) if location else None,
                "location_name": location.key if location else "未知地点",
                "combat_type": combat_type,
                "start_time": time.time(),
                "combatant_count": len(combatants),
                "realm_levels": [c["realm"] for c in combatants_data],
                "elements": [c["element"] for c in combatants_data]
            }
        )
        
        return self._publish_event(event)
    
    def publish_combat_end(self, winner: str, duration: float, stats: Dict, combatants: List) -> bool:
        """
        发布战斗结束事件
        
        Args:
            winner: 胜利者
            duration: 战斗持续时间
            stats: 战斗统计
            combatants: 参战者列表
            
        Returns:
            bool: 发布是否成功
        """
        # 格式化结束数据
        final_states = []
        for combatant in combatants:
            state = {
                "id": str(combatant.id),
                "name": combatant.key,
                "final_hp": getattr(combatant, 'hp', 0),
                "max_hp": getattr(combatant, 'max_hp', 100),
                "is_alive": getattr(combatant, 'hp', 0) > 0,
                "spiritual_power": getattr(combatant, 'db', {}).get('spiritual_power', 0)
            }
            final_states.append(state)
        
        event = CombatEndEvent(
            data={
                "winner": winner,
                "duration": duration,
                "stats": stats,
                "final_states": final_states,
                "end_time": time.time(),
                "outcome_type": self._determine_outcome_type(winner, final_states)
            }
        )
        
        return self._publish_event(event)
    
    def publish_skill_cast(self, caster, skill_name: str, target=None, effects: List = None) -> bool:
        """
        发布技能施放事件
        
        Args:
            caster: 施法者
            skill_name: 技能名称
            target: 目标（可选）
            effects: 技能效果列表
            
        Returns:
            bool: 发布是否成功
        """
        # 获取技能信息
        skill_data = getattr(caster, 'db', {}).get('xiuxian_skills', {}).get(skill_name, {})
        skill_template = skill_data.get('template', {})
        
        event = SkillCastEvent(
            source_id=str(caster.id),
            target_id=str(target.id) if target else None,
            data={
                "caster_name": caster.key,
                "skill_name": skill_name,
                "skill_type": skill_template.get("type", "未知"),
                "skill_element": skill_template.get("element", "无"),
                "skill_grade": skill_template.get("grade", "凡级"),
                "caster_realm": getattr(caster, '修为境界', '练气'),
                "caster_element": getattr(caster, '五行属性', '土'),
                "target_name": target.key if target else None,
                "target_realm": getattr(target, '修为境界', '练气') if target else None,
                "target_element": getattr(target, '五行属性', '土') if target else None,
                "effects": effects or [],
                "mp_cost": skill_template.get("mp_cost", 0),
                "skill_level": skill_data.get("level", 1)
            }
        )
        
        return self._publish_event(event)
    
    def publish_wuxing_interaction(self, attacker, target, attacker_element: str, 
                                 target_element: str, bonus: float, skill_element: str = None) -> bool:
        """
        发布五行相克事件
        
        Args:
            attacker: 攻击者
            target: 目标
            attacker_element: 攻击者五行
            target_element: 目标五行
            bonus: 相克加成
            skill_element: 技能五行（可选）
            
        Returns:
            bool: 发布是否成功
        """
        interaction_type = self._get_interaction_type(bonus)
        
        event = WuxingInteractionEvent(
            source_id=str(attacker.id),
            target_id=str(target.id),
            data={
                "attacker_name": attacker.key,
                "target_name": target.key,
                "attacker_element": attacker_element,
                "target_element": target_element,
                "skill_element": skill_element or attacker_element,
                "bonus": bonus,
                "interaction_type": interaction_type,
                "attacker_realm": getattr(attacker, '修为境界', '练气'),
                "target_realm": getattr(target, '修为境界', '练气'),
                "description": self._get_wuxing_description(attacker_element, target_element, interaction_type)
            }
        )
        
        return self._publish_event(event)
    
    def publish_realm_difference(self, higher_realm_char, lower_realm_char, 
                               realm_diff: int, bonus: float) -> bool:
        """
        发布境界差异事件
        
        Args:
            higher_realm_char: 高境界角色
            lower_realm_char: 低境界角色
            realm_diff: 境界差异
            bonus: 境界加成
            
        Returns:
            bool: 发布是否成功
        """
        event = RealmDifferenceEvent(
            source_id=str(higher_realm_char.id),
            target_id=str(lower_realm_char.id),
            data={
                "higher_realm_name": higher_realm_char.key,
                "lower_realm_name": lower_realm_char.key,
                "higher_realm": getattr(higher_realm_char, '修为境界', '练气'),
                "lower_realm": getattr(lower_realm_char, '修为境界', '练气'),
                "realm_difference": realm_diff,
                "bonus": bonus,
                "description": f"{higher_realm_char.key}的境界压制了{lower_realm_char.key}"
            }
        )
        
        return self._publish_event(event)
    
    def publish_combat_action(self, actor, action_type: str, target=None, 
                            result: Dict = None) -> bool:
        """
        发布战斗行动事件
        
        Args:
            actor: 行动者
            action_type: 行动类型
            target: 目标（可选）
            result: 行动结果
            
        Returns:
            bool: 发布是否成功
        """
        event = CombatActionEvent(
            source_id=str(actor.id),
            target_id=str(target.id) if target else None,
            data={
                "actor_name": actor.key,
                "action_type": action_type,
                "target_name": target.key if target else None,
                "result": result or {},
                "actor_realm": getattr(actor, '修为境界', '练气'),
                "actor_element": getattr(actor, '五行属性', '土'),
                "timestamp": time.time()
            }
        )
        
        return self._publish_event(event)
    
    def publish_combat_round(self, round_number: int, active_combatants: List, 
                           round_summary: Dict) -> bool:
        """
        发布战斗回合事件
        
        Args:
            round_number: 回合数
            active_combatants: 活跃战斗者
            round_summary: 回合总结
            
        Returns:
            bool: 发布是否成功
        """
        combatants_status = []
        for combatant in active_combatants:
            status = {
                "id": str(combatant.id),
                "name": combatant.key,
                "hp": getattr(combatant, 'hp', 100),
                "max_hp": getattr(combatant, 'max_hp', 100),
                "spiritual_power": getattr(combatant, 'db', {}).get('spiritual_power', 100),
                "is_alive": getattr(combatant, 'hp', 0) > 0
            }
            combatants_status.append(status)
        
        event = CombatRoundEvent(
            data={
                "round_number": round_number,
                "combatants_status": combatants_status,
                "round_summary": round_summary,
                "active_count": len([c for c in combatants_status if c["is_alive"]]),
                "timestamp": time.time()
            }
        )
        
        return self._publish_event(event)
    
    def _publish_event(self, event: BaseEvent) -> bool:
        """
        发布事件到事件总线
        
        Args:
            event: 要发布的事件
            
        Returns:
            bool: 发布是否成功
        """
        try:
            success = publish_event(event)
            
            if success:
                # 更新统计
                self.published_events.append(event)
                self.event_stats["total_published"] += 1
                
                event_type = event.event_type
                self.event_stats["by_type"][event_type] = self.event_stats["by_type"].get(event_type, 0) + 1
                
                priority = event.priority.value
                self.event_stats["by_priority"][priority] = self.event_stats["by_priority"].get(priority, 0) + 1
                
                logger.log_info(f"Combat event published: {event_type}")
            
            return success
            
        except Exception as e:
            logger.log_err(f"Failed to publish combat event: {e}")
            return False
    
    def _determine_outcome_type(self, winner: str, final_states: List) -> str:
        """
        确定战斗结果类型
        
        Args:
            winner: 胜利者
            final_states: 最终状态
            
        Returns:
            str: 结果类型
        """
        alive_count = len([s for s in final_states if s["is_alive"]])
        
        if winner == "平局":
            return "平局"
        elif alive_count == 1:
            return "决定性胜利"
        elif alive_count > 1:
            return "部分胜利"
        else:
            return "同归于尽"
    
    def _get_interaction_type(self, bonus: float) -> str:
        """
        根据加成确定相克类型
        
        Args:
            bonus: 加成倍数
            
        Returns:
            str: 相克类型
        """
        if bonus > 1.2:
            return "强相克"
        elif bonus > 1.0:
            return "相克"
        elif bonus < 0.8:
            return "强被克"
        elif bonus < 1.0:
            return "被克"
        else:
            return "平衡"
    
    def _get_wuxing_description(self, attacker_element: str, target_element: str, 
                              interaction_type: str) -> str:
        """
        获取五行相克描述
        
        Args:
            attacker_element: 攻击者五行
            target_element: 目标五行
            interaction_type: 相克类型
            
        Returns:
            str: 相克描述
        """
        descriptions = {
            "强相克": f"{attacker_element}系力量强烈克制{target_element}系",
            "相克": f"{attacker_element}克{target_element}",
            "强被克": f"{attacker_element}系力量被{target_element}系强烈克制",
            "被克": f"{attacker_element}被{target_element}克制",
            "平衡": f"{attacker_element}与{target_element}势均力敌"
        }
        
        return descriptions.get(interaction_type, "五行力量交锋")
    
    def get_event_stats(self) -> Dict:
        """
        获取事件发布统计
        
        Returns:
            Dict: 统计信息
        """
        return self.event_stats.copy()
    
    def get_recent_events(self, count: int = 10) -> List[BaseEvent]:
        """
        获取最近的事件
        
        Args:
            count: 获取数量
            
        Returns:
            List[BaseEvent]: 最近的事件列表
        """
        return self.published_events[-count:] if self.published_events else []
    
    def clear_event_history(self):
        """清空事件历史"""
        self.published_events.clear()
        self.event_stats = {
            "total_published": 0,
            "by_type": {},
            "by_priority": {}
        }
        logger.log_info("Combat event history cleared")


# 全局实例
combat_event_publisher = CombatEventPublisher()


def get_combat_event_publisher() -> CombatEventPublisher:
    """获取战斗事件发布器实例"""
    return combat_event_publisher
