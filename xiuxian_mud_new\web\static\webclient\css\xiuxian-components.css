/*
仙侠MUD UI组件样式库

设计理念：
- 模块化组件设计
- BEM命名约定
- 可复用的UI元素
- 仙侠主题一致性

组件分类：
- 角色状态组件
- 快捷操作组件
- AI导演面板组件
- 聊天频道组件
- 在线玩家组件
- 修仙进度组件
*/

/* ===== 角色状态组件 ===== */

/* 角色状态面板 */
.character-status {
    background: var(--xiuxian-gradient-primary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 12px;
    padding: 16px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-md);
    position: relative;
    overflow: hidden;
}

.character-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--xiuxian-gradient-gold);
}

/* 角色基本信息 */
.character-status__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--xiuxian-divider);
}

.character-status__name {
    font-size: 1.2em;
    font-weight: bold;
    color: var(--xiuxian-secondary);
    text-shadow: 0 0 8px var(--xiuxian-primary);
}

.character-status__level {
    background: var(--xiuxian-gradient-accent);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9em;
    font-weight: bold;
}

/* 角色属性网格 */
.character-status__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
}

.character-status__stat {
    background: rgba(212, 175, 55, 0.1);
    border: 1px solid var(--xiuxian-border-light);
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    transition: var(--xiuxian-transition-fast);
}

.character-status__stat:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-1px);
}

.character-status__stat-label {
    display: block;
    font-size: 0.8em;
    color: var(--xiuxian-text-secondary);
    margin-bottom: 2px;
}

.character-status__stat-value {
    display: block;
    font-size: 1.1em;
    font-weight: bold;
    color: var(--xiuxian-primary);
}

/* 修仙境界显示 */
.character-status__realm {
    background: var(--xiuxian-gradient-gold);
    color: var(--xiuxian-bg-primary);
    padding: 8px 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 8px;
    box-shadow: var(--xiuxian-shadow-glow);
}

/* 门派信息 */
.character-status__sect {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--xiuxian-text-secondary);
    font-size: 0.9em;
}

.character-status__sect-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--xiuxian-primary);
}

/* ===== 快捷操作组件 ===== */

/* 快捷操作面板 */
.quick-actions {
    background: var(--xiuxian-bg-secondary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 10px;
    padding: 12px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-md);
}

.quick-actions__title {
    font-size: 1em;
    font-weight: bold;
    color: var(--xiuxian-primary);
    margin-bottom: 10px;
    text-align: center;
    border-bottom: 1px solid var(--xiuxian-divider);
    padding-bottom: 6px;
}

/* 快捷按钮网格 */
.quick-actions__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
}

.quick-actions__btn {
    background: var(--xiuxian-gradient-primary);
    border: 1px solid var(--xiuxian-border-light);
    border-radius: 8px;
    padding: 10px 8px;
    color: var(--xiuxian-text-primary);
    font-size: 0.85em;
    text-align: center;
    cursor: pointer;
    transition: var(--xiuxian-transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.quick-actions__btn:hover {
    background: var(--xiuxian-gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--xiuxian-shadow-md);
}

.quick-actions__btn-icon {
    font-size: 1.2em;
    margin-bottom: 2px;
}

.quick-actions__btn-text {
    font-size: 0.8em;
    line-height: 1.2;
}

/* ===== AI导演面板组件 ===== */

/* AI导演面板 */
.ai-director-panel {
    background: var(--xiuxian-gradient-accent);
    border: 2px solid var(--xiuxian-primary);
    border-radius: 12px;
    padding: 16px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-lg), var(--xiuxian-shadow-glow);
    position: relative;
    overflow: hidden;
}

.ai-director-panel::before {
    content: '🎭';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 3em;
    opacity: 0.1;
    transform: rotate(15deg);
}

.ai-director-panel__header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: white;
}

.ai-director-panel__title {
    font-size: 1.1em;
    font-weight: bold;
}

.ai-director-panel__status {
    background: var(--xiuxian-success);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: bold;
}

/* AI导演消息区域 */
.ai-director-panel__messages {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    max-height: 150px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.ai-director-panel__message {
    color: white;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    font-size: 0.9em;
    line-height: 1.4;
}

.ai-director-panel__message:last-child {
    margin-bottom: 0;
}

/* AI导演控制按钮 */
.ai-director-panel__controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.ai-director-panel__btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    padding: 6px 12px;
    font-size: 0.8em;
    cursor: pointer;
    transition: var(--xiuxian-transition-fast);
}

.ai-director-panel__btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* ===== 聊天频道组件 ===== */

/* 聊天频道面板 */
.chat-channels {
    background: var(--xiuxian-bg-secondary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 10px;
    padding: 12px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-md);
}

.chat-channels__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--xiuxian-divider);
}

.chat-channels__title {
    font-size: 1em;
    font-weight: bold;
    color: var(--xiuxian-primary);
}

.chat-channels__toggle {
    background: none;
    border: none;
    color: var(--xiuxian-text-secondary);
    cursor: pointer;
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 4px;
    transition: var(--xiuxian-transition-fast);
}

.chat-channels__toggle:hover {
    background: var(--xiuxian-border);
    color: var(--xiuxian-text-primary);
}

/* 频道列表 */
.chat-channels__list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.chat-channels__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--xiuxian-transition-fast);
}

.chat-channels__item:hover {
    background: rgba(212, 175, 55, 0.1);
}

.chat-channels__item.active {
    background: var(--xiuxian-gradient-gold);
    color: var(--xiuxian-bg-primary);
}

.chat-channels__name {
    font-size: 0.9em;
    font-weight: 500;
}

.chat-channels__count {
    background: var(--xiuxian-primary);
    color: var(--xiuxian-bg-primary);
    padding: 1px 5px;
    border-radius: 8px;
    font-size: 0.7em;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
}

/* ===== 在线玩家组件 ===== */

/* 在线玩家面板 */
.online-players {
    background: var(--xiuxian-bg-secondary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 10px;
    padding: 12px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-md);
}

.online-players__header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--xiuxian-divider);
}

.online-players__title {
    font-size: 1em;
    font-weight: bold;
    color: var(--xiuxian-primary);
}

.online-players__count {
    background: var(--xiuxian-success);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: bold;
}

/* 玩家列表 */
.online-players__list {
    max-height: 200px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.online-players__item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    border-radius: 6px;
    margin-bottom: 4px;
    transition: var(--xiuxian-transition-fast);
}

.online-players__item:hover {
    background: rgba(212, 175, 55, 0.1);
}

.online-players__avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--xiuxian-gradient-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    color: var(--xiuxian-bg-primary);
    font-weight: bold;
}

.online-players__info {
    flex: 1;
}

.online-players__name {
    font-size: 0.9em;
    font-weight: 500;
    color: var(--xiuxian-text-primary);
}

.online-players__realm {
    font-size: 0.7em;
    color: var(--xiuxian-text-secondary);
}

.online-players__status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--xiuxian-success);
}

.online-players__status.away {
    background: var(--xiuxian-warning);
}

.online-players__status.busy {
    background: var(--xiuxian-error);
}

/* ===== 修仙进度组件 ===== */

/* 修仙进度面板 */
.cultivation-progress {
    background: var(--xiuxian-bg-secondary);
    border: 1px solid var(--xiuxian-border);
    border-radius: 10px;
    padding: 12px;
    margin: 10px;
    box-shadow: var(--xiuxian-shadow-md);
}

.cultivation-progress__title {
    font-size: 1em;
    font-weight: bold;
    color: var(--xiuxian-primary);
    margin-bottom: 10px;
    text-align: center;
}

/* 进度条 */
.cultivation-progress__bar {
    background: var(--xiuxian-bg-tertiary);
    border-radius: 10px;
    height: 20px;
    margin-bottom: 8px;
    overflow: hidden;
    position: relative;
    border: 1px solid var(--xiuxian-border);
}

.cultivation-progress__fill {
    background: var(--xiuxian-gradient-gold);
    height: 100%;
    border-radius: 10px;
    transition: width var(--xiuxian-transition-slow);
    position: relative;
    overflow: hidden;
}

.cultivation-progress__fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.cultivation-progress__text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8em;
    font-weight: bold;
    color: var(--xiuxian-bg-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 修仙统计 */
.cultivation-progress__stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.8em;
}

.cultivation-progress__stat {
    text-align: center;
    color: var(--xiuxian-text-secondary);
}

.cultivation-progress__stat-value {
    display: block;
    font-weight: bold;
    color: var(--xiuxian-primary);
    margin-bottom: 2px;
}

/* ===== 响应式组件适配 ===== */

@media (max-width: 768px) {
    /* 移动端组件间距调整 */
    .character-status,
    .quick-actions,
    .ai-director-panel,
    .chat-channels,
    .online-players,
    .cultivation-progress {
        margin: 8px 5px;
        padding: 10px;
    }
    
    /* 移动端网格调整 */
    .character-status__stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .quick-actions__grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
    }
    
    /* 移动端字体调整 */
    .character-status__name,
    .ai-director-panel__title,
    .chat-channels__title,
    .online-players__title,
    .cultivation-progress__title {
        font-size: 1em;
    }
}
