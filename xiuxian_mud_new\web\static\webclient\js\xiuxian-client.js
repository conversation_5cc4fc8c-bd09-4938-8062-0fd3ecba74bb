/**
 * 仙侠MUD客户端主JavaScript文件
 * 
 * 功能：
 * - 统一管理所有仙侠主题UI组件
 * - 提供全局事件总线和状态管理
 * - 集成Evennia WebSocket通信
 * - 处理主题切换和用户偏好
 * - 提供通用工具函数和API
 * - 管理组件生命周期
 * 
 * 技术集成：
 * - 与Evennia WebSocket完美集成
 * - 模块化组件管理架构
 * - 事件驱动的通信机制
 * - 本地存储管理
 * - 性能优化和错误处理
 */

// 仙侠客户端主管理器
window.XiuxianClient = {
    // 版本信息
    version: '1.0.0',
    
    // 配置
    config: {
        debug: false,
        autoSave: true,
        saveInterval: 30000, // 30秒自动保存
        reconnectAttempts: 5,
        reconnectDelay: 3000,
        heartbeatInterval: 30000,
        theme: 'xiuxian-default',
        language: 'zh-CN'
    },

    // 全局状态
    state: {
        initialized: false,
        connected: false,
        user: null,
        character: null,
        currentTheme: 'xiuxian-default',
        components: {},
        eventListeners: {},
        settings: {}
    },

    // 组件注册表
    components: {
        characterStatus: null,
        quickActions: null,
        aiDirectorPanel: null,
        chatChannels: null,
        onlinePlayers: null,
        cultivationProgress: null
    },

    // 事件总线
    eventBus: {
        listeners: {},
        
        // 注册事件监听器
        on: function(event, callback, context) {
            if (!this.listeners[event]) {
                this.listeners[event] = [];
            }
            this.listeners[event].push({
                callback: callback,
                context: context || null
            });
        },
        
        // 移除事件监听器
        off: function(event, callback) {
            if (!this.listeners[event]) return;
            
            this.listeners[event] = this.listeners[event].filter(function(listener) {
                return listener.callback !== callback;
            });
        },
        
        // 触发事件
        emit: function(event, data) {
            if (!this.listeners[event]) return;
            
            this.listeners[event].forEach(function(listener) {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data);
                    } else {
                        listener.callback(data);
                    }
                } catch (error) {
                    console.error('Event handler error:', error);
                }
            });
        },
        
        // 清除所有监听器
        clear: function() {
            this.listeners = {};
        }
    },

    // 初始化客户端
    init: function() {
        if (this.state.initialized) {
            console.warn('XiuxianClient already initialized');
            return;
        }

        console.log('Initializing XiuxianClient v' + this.version);
        
        // 加载设置
        this.loadSettings();
        
        // 应用主题
        this.applyTheme(this.state.currentTheme);
        
        // 初始化组件
        this.initializeComponents();
        
        // 设置WebSocket集成
        this.setupWebSocketIntegration();
        
        // 绑定全局事件
        this.bindGlobalEvents();
        
        // 启动自动保存
        this.startAutoSave();
        
        // 启动心跳检测
        this.startHeartbeat();
        
        this.state.initialized = true;
        this.eventBus.emit('client:initialized', {});
        
        console.log('XiuxianClient initialized successfully');
    },

    // 初始化组件
    initializeComponents: function() {
        const self = this;
        
        // 角色状态组件
        if (window.CharacterStatusManager) {
            this.components.characterStatus = window.CharacterStatusManager;
            this.components.characterStatus.init();
        }
        
        // 快捷操作组件
        if (window.QuickActionsManager) {
            this.components.quickActions = window.QuickActionsManager;
            this.components.quickActions.init();
        }
        
        // AI导演面板组件
        if (window.AIDirectorPanelManager) {
            this.components.aiDirectorPanel = window.AIDirectorPanelManager;
            this.components.aiDirectorPanel.init();
        }
        
        // 聊天频道组件
        if (window.ChatChannelsManager) {
            this.components.chatChannels = window.ChatChannelsManager;
            this.components.chatChannels.init();
        }
        
        // 在线玩家组件
        if (window.OnlinePlayersManager) {
            this.components.onlinePlayers = window.OnlinePlayersManager;
            this.components.onlinePlayers.init();
        }
        
        // 修仙进度组件
        if (window.CultivationProgressManager) {
            this.components.cultivationProgress = window.CultivationProgressManager;
            this.components.cultivationProgress.init();
        }

        // 注册组件间通信
        this.setupComponentCommunication();
    },

    // 设置组件间通信
    setupComponentCommunication: function() {
        const self = this;
        
        // 角色状态更新时通知其他组件
        this.eventBus.on('character:status_updated', function(data) {
            if (self.components.cultivationProgress) {
                self.components.cultivationProgress.handleCharacterUpdate(data);
            }
            if (self.components.quickActions) {
                self.components.quickActions.updateCharacterStatus(data);
            }
        });
        
        // 修仙进度更新时通知其他组件
        this.eventBus.on('cultivation:progress_updated', function(data) {
            if (self.components.characterStatus) {
                self.components.characterStatus.updateCultivationInfo(data);
            }
        });
        
        // 聊天消息时更新在线玩家状态
        this.eventBus.on('chat:message_received', function(data) {
            if (self.components.onlinePlayers && data.sender) {
                self.components.onlinePlayers.updatePlayerActivity(data.sender);
            }
        });
        
        // AI导演事件时更新相关组件
        this.eventBus.on('ai_director:event_triggered', function(data) {
            if (self.components.chatChannels) {
                self.components.chatChannels.addSystemMessage(data.message);
            }
        });
    },

    // 设置WebSocket集成
    setupWebSocketIntegration: function() {
        if (!window.Evennia) {
            console.warn('Evennia WebSocket not available');
            return;
        }

        const self = this;
        
        // 保存原始消息处理器
        this.originalEvenniaMsg = window.Evennia.msg;
        
        // 扩展消息处理器
        window.Evennia.msg = function(cmdname, args, kwargs) {
            // 处理仙侠客户端消息
            self.handleEvenniaMessage(cmdname, args, kwargs);
            
            // 调用原始处理器
            if (self.originalEvenniaMsg) {
                self.originalEvenniaMsg(cmdname, args, kwargs);
            }
        };

        // 监听连接状态
        this.eventBus.on('websocket:connected', function() {
            self.state.connected = true;
            self.onWebSocketConnected();
        });

        this.eventBus.on('websocket:disconnected', function() {
            self.state.connected = false;
            self.onWebSocketDisconnected();
        });
    },

    // 处理Evennia消息
    handleEvenniaMessage: function(cmdname, args, kwargs) {
        // 分发消息到相应组件
        this.eventBus.emit('evennia:' + cmdname, {
            command: cmdname,
            args: args,
            kwargs: kwargs
        });

        // 处理特定消息类型
        switch (cmdname) {
            case 'logged_in':
                this.handleLogin(args, kwargs);
                break;
            case 'logged_out':
                this.handleLogout(args, kwargs);
                break;
            case 'character_data':
                this.handleCharacterData(args, kwargs);
                break;
            case 'system_message':
                this.handleSystemMessage(args, kwargs);
                break;
        }
    },

    // 处理登录
    handleLogin: function(args, kwargs) {
        this.state.user = kwargs.user || args[0];
        this.eventBus.emit('user:logged_in', this.state.user);
        this.showNotification('欢迎回到仙侠世界！', 'success');
    },

    // 处理登出
    handleLogout: function(args, kwargs) {
        this.state.user = null;
        this.state.character = null;
        this.eventBus.emit('user:logged_out', {});
        this.showNotification('已安全退出', 'info');
    },

    // 处理角色数据
    handleCharacterData: function(args, kwargs) {
        this.state.character = kwargs.character || args[0];
        this.eventBus.emit('character:data_updated', this.state.character);
    },

    // 处理系统消息
    handleSystemMessage: function(args, kwargs) {
        const message = kwargs.message || args[0];
        const type = kwargs.type || 'info';
        this.showNotification(message, type);
    },

    // WebSocket连接成功
    onWebSocketConnected: function() {
        this.showNotification('连接已建立', 'success');
        this.eventBus.emit('client:connected', {});
        
        // 请求初始数据
        this.requestInitialData();
    },

    // WebSocket连接断开
    onWebSocketDisconnected: function() {
        this.showNotification('连接已断开，正在重连...', 'warning');
        this.eventBus.emit('client:disconnected', {});
    },

    // 请求初始数据
    requestInitialData: function() {
        this.sendCommand('get_character_data', {});
        this.sendCommand('get_initial_ui_data', {});
    },

    // 发送命令到服务器
    sendCommand: function(command, data) {
        if (window.Evennia && window.Evennia.msg) {
            window.Evennia.msg('xiuxian_command', [command], data);
        }
    },

    // 绑定全局事件
    bindGlobalEvents: function() {
        const self = this;

        // 窗口大小改变
        window.addEventListener('resize', function() {
            self.handleWindowResize();
        });

        // 页面可见性改变
        document.addEventListener('visibilitychange', function() {
            self.handleVisibilityChange();
        });

        // 页面卸载前保存数据
        window.addEventListener('beforeunload', function() {
            self.saveSettings();
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            self.handleKeyboardShortcuts(e);
        });
    },

    // 处理窗口大小改变
    handleWindowResize: function() {
        this.eventBus.emit('window:resized', {
            width: window.innerWidth,
            height: window.innerHeight
        });
    },

    // 处理页面可见性改变
    handleVisibilityChange: function() {
        if (document.hidden) {
            this.eventBus.emit('page:hidden', {});
        } else {
            this.eventBus.emit('page:visible', {});
        }
    },

    // 处理键盘快捷键
    handleKeyboardShortcuts: function(e) {
        // Ctrl/Cmd + 数字键切换频道
        if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '9') {
            e.preventDefault();
            const channelIndex = parseInt(e.key) - 1;
            this.eventBus.emit('shortcut:switch_channel', { index: channelIndex });
        }
        
        // ESC键关闭对话框
        if (e.key === 'Escape') {
            this.eventBus.emit('shortcut:escape', {});
        }
        
        // F5刷新数据
        if (e.key === 'F5') {
            e.preventDefault();
            this.refreshAllData();
        }
    },

    // 刷新所有数据
    refreshAllData: function() {
        this.showNotification('正在刷新数据...', 'info');

        // 通知所有组件刷新
        Object.keys(this.components).forEach(function(key) {
            const component = this.components[key];
            if (component && typeof component.refresh === 'function') {
                component.refresh();
            }
        }.bind(this));

        this.eventBus.emit('client:refresh_all', {});
    },

    // 应用主题
    applyTheme: function(themeName) {
        // 移除旧主题类
        document.body.classList.remove('theme-' + this.state.currentTheme);

        // 添加新主题类
        document.body.classList.add('theme-' + themeName);

        this.state.currentTheme = themeName;
        this.eventBus.emit('theme:changed', { theme: themeName });

        // 保存主题设置
        this.state.settings.theme = themeName;
        this.saveSettings();
    },

    // 切换主题
    toggleTheme: function() {
        const themes = ['xiuxian-default', 'xiuxian-dark', 'xiuxian-light'];
        const currentIndex = themes.indexOf(this.state.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;

        this.applyTheme(themes[nextIndex]);
        this.showNotification('已切换到 ' + themes[nextIndex] + ' 主题', 'info');
    },

    // 显示通知
    showNotification: function(message, type, duration) {
        type = type || 'info';
        duration = duration || 3000;

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `xiuxian-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加到通知容器
        let container = document.getElementById('xiuxian-notifications');
        if (!container) {
            container = document.createElement('div');
            container.id = 'xiuxian-notifications';
            container.className = 'xiuxian-notifications-container';
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // 自动移除
        setTimeout(function() {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);

        // 触发通知事件
        this.eventBus.emit('notification:shown', {
            message: message,
            type: type,
            duration: duration
        });
    },

    // 获取通知图标
    getNotificationIcon: function(type) {
        const icons = {
            success: '✓',
            error: '✗',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    },

    // 启动自动保存
    startAutoSave: function() {
        if (!this.config.autoSave) return;

        const self = this;
        setInterval(function() {
            self.saveSettings();
        }, this.config.saveInterval);
    },

    // 启动心跳检测
    startHeartbeat: function() {
        const self = this;
        setInterval(function() {
            if (self.state.connected) {
                self.sendCommand('heartbeat', { timestamp: Date.now() });
            }
        }, this.config.heartbeatInterval);
    },

    // 加载设置
    loadSettings: function() {
        try {
            const saved = localStorage.getItem('xiuxian_client_settings');
            if (saved) {
                this.state.settings = JSON.parse(saved);

                // 应用保存的设置
                if (this.state.settings.theme) {
                    this.state.currentTheme = this.state.settings.theme;
                }
                if (this.state.settings.debug !== undefined) {
                    this.config.debug = this.state.settings.debug;
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    },

    // 保存设置
    saveSettings: function() {
        try {
            localStorage.setItem('xiuxian_client_settings', JSON.stringify(this.state.settings));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    },

    // 获取设置
    getSetting: function(key, defaultValue) {
        return this.state.settings[key] !== undefined ? this.state.settings[key] : defaultValue;
    },

    // 设置设置
    setSetting: function(key, value) {
        this.state.settings[key] = value;
        this.saveSettings();
        this.eventBus.emit('setting:changed', { key: key, value: value });
    },

    // 获取组件
    getComponent: function(name) {
        return this.components[name];
    },

    // 注册组件
    registerComponent: function(name, component) {
        this.components[name] = component;
        this.eventBus.emit('component:registered', { name: name, component: component });
    },

    // 注销组件
    unregisterComponent: function(name) {
        if (this.components[name]) {
            if (typeof this.components[name].destroy === 'function') {
                this.components[name].destroy();
            }
            delete this.components[name];
            this.eventBus.emit('component:unregistered', { name: name });
        }
    },

    // 工具函数：格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    // 工具函数：格式化日期
    formatDate: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    },

    // 工具函数：格式化数字
    formatNumber: function(num) {
        if (num >= 100000000) {
            return (num / 100000000).toFixed(1) + '亿';
        } else if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    },

    // 工具函数：防抖
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction() {
            const later = function() {
                clearTimeout(timeout);
                func.apply(this, arguments);
            }.bind(this);
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 工具函数：节流
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        };
    },

    // 调试日志
    log: function(message, data) {
        if (this.config.debug) {
            console.log('[XiuxianClient]', message, data || '');
        }
    },

    // 错误日志
    error: function(message, error) {
        console.error('[XiuxianClient]', message, error || '');
        this.eventBus.emit('client:error', { message: message, error: error });
    },

    // 销毁客户端
    destroy: function() {
        // 停止定时器
        clearInterval(this.autoSaveTimer);
        clearInterval(this.heartbeatTimer);

        // 销毁所有组件
        Object.keys(this.components).forEach(function(key) {
            this.unregisterComponent(key);
        }.bind(this));

        // 恢复原始WebSocket处理器
        if (this.originalEvenniaMsg) {
            window.Evennia.msg = this.originalEvenniaMsg;
        }

        // 清除事件监听器
        this.eventBus.clear();

        // 保存设置
        this.saveSettings();

        this.state.initialized = false;
        console.log('XiuxianClient destroyed');
    }
};

// 全局工具函数
window.XiuxianUtils = {
    // 获取境界文本
    getRealmText: function(realm) {
        const realmMap = {
            qi: '练气期',
            foundation: '筑基期',
            golden: '金丹期',
            nascent: '元婴期',
            spirit: '化神期',
            void: '炼虚期',
            unity: '合体期',
            tribulation: '大乘期',
            immortal: '仙人境'
        };
        return realmMap[realm] || '未知境界';
    },

    // 获取门派文本
    getSectText: function(sect) {
        const sectMap = {
            wudang: '武当派',
            shaolin: '少林寺',
            emei: '峨眉派',
            huashan: '华山派',
            kunlun: '昆仑派',
            none: '散修'
        };
        return sectMap[sect] || '未知';
    },

    // 获取状态文本
    getStatusText: function(status) {
        const statusMap = {
            online: '在线',
            busy: '忙碌',
            away: '离开',
            cultivation: '修炼中',
            combat: '战斗中'
        };
        return statusMap[status] || '未知';
    },

    // 颜色工具
    colors: {
        primary: '#8B4513',
        secondary: '#DAA520',
        success: '#228B22',
        warning: '#FF8C00',
        error: '#DC143C',
        info: '#4682B4'
    }
};

// 当DOM加载完成时自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化以确保所有组件都已加载
    setTimeout(function() {
        if (window.XiuxianClient) {
            window.XiuxianClient.init();
        }
    }, 100);
});

// 导出到全局
window.xiuxian = window.XiuxianClient;
