#!/usr/bin/env python3
"""
测试脚本：检查AI导演命令是否正确加载
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
django.setup()

from evennia import ObjectDB, AccountDB, DefaultCharacter
from commands.default_cmdsets import CharacterCmdSet

def test_ai_commands():
    print("=== 测试AI导演命令加载 ===")
    
    # 创建测试命令集
    cmdset = CharacterCmdSet()
    cmdset.at_cmdset_creation()
    
    # 获取所有命令
    commands = list(cmdset.commands)
    
    print(f"总命令数: {len(commands)}")
    
    # 查找AI相关命令
    ai_commands = []
    for cmd in commands:
        cmd_key = cmd.key.lower()
        cmd_aliases = [alias.lower() for alias in getattr(cmd, 'aliases', [])]
        
        if any(keyword in cmd_key for keyword in ['ai', '测试', '状态', '性能', '决策', '帮助']) or \
           any(any(keyword in alias for keyword in ['ai', 'test', 'status', 'perf', 'decision', 'help']) for alias in cmd_aliases):
            ai_commands.append(cmd)
    
    print(f"\n找到 {len(ai_commands)} 个AI相关命令:")
    
    for i, cmd in enumerate(ai_commands, 1):
        aliases_str = ', '.join(cmd.aliases) if hasattr(cmd, 'aliases') and cmd.aliases else '无'
        help_cat = getattr(cmd, 'help_category', '未分类')
        locks = getattr(cmd, 'locks', '无')
        
        print(f"{i}. {cmd.key}")
        print(f"   别名: {aliases_str}")
        print(f"   分类: {help_cat}")
        print(f"   权限: {locks}")
        print(f"   描述: {cmd.__doc__.strip().split('\\n')[0] if cmd.__doc__ else '无描述'}")
        print()
    
    # 测试特定命令
    test_commands = ['ai状态', 'ai帮助', '测试ai', 'ai性能', 'ai决策']
    print("=== 测试特定命令存在性 ===")
    
    for test_cmd in test_commands:
        found = False
        for cmd in commands:
            if cmd.key == test_cmd or test_cmd in getattr(cmd, 'aliases', []):
                found = True
                break
        
        status = "✓ 存在" if found else "✗ 不存在"
        print(f"{test_cmd}: {status}")
    
    return ai_commands

if __name__ == "__main__":
    try:
        ai_commands = test_ai_commands()
        print(f"\n总结: 成功加载 {len(ai_commands)} 个AI导演相关命令")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()