"""
三层AI导演系统性能测试

专门测试三层AI导演系统的性能表现：
- 消息传递性能
- 协调请求性能
- 决策响应时间
- 内存使用情况
- 并发处理能力
"""

import time
import threading
import statistics
from evennia.utils.test_resources import EvenniaTest
from evennia.utils import search

from scripts.ai_directors.director_initialization import initialize_director_system, get_director_system_status
from scripts.ai_directors.director_integration import DirectorLayer, get_director_integration


class DirectorPerformanceTest(EvenniaTest):
    """导演系统性能测试"""
    
    def setUp(self):
        """测试设置"""
        super().setUp()
        # 初始化导演系统
        initialize_director_system()
        time.sleep(1)
        
        self.integration = get_director_integration()
        self.assertIsNotNone(self.integration, "集成系统应该可用")
    
    def tearDown(self):
        """测试清理"""
        # 清理创建的脚本
        scripts_to_clean = [
            "director_integration_script",
            "tiandao_director_script", 
            "diling_director_script",
            "qiling_director_script",
            "director_initialization_script"
        ]
        
        for script_key in scripts_to_clean:
            scripts = search.search_script(script_key)
            for script in scripts:
                script.delete()
        
        super().tearDown()
    
    def test_message_sending_performance(self):
        """测试消息发送性能"""
        print("\n=== 测试消息发送性能 ===")
        
        message_counts = [10, 50, 100, 500]
        results = {}
        
        for count in message_counts:
            # 清空消息队列
            self.integration.db.communication_state["message_queue"] = []
            
            # 测量发送时间
            start_time = time.time()
            
            for i in range(count):
                self.integration.send_message(
                    DirectorLayer.TIANDAO,
                    DirectorLayer.DILING,
                    "performance_test",
                    {"index": i, "timestamp": time.time()},
                    "normal"
                )
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[count] = {
                "total_time": duration,
                "avg_time_per_message": duration / count * 1000,  # 毫秒
                "messages_per_second": count / duration
            }
            
            print(f"发送{count}条消息:")
            print(f"  总时间: {duration:.3f}秒")
            print(f"  平均每条: {duration/count*1000:.3f}毫秒")
            print(f"  每秒处理: {count/duration:.1f}条")
        
        # 验证性能要求
        # 每条消息处理时间应该小于10毫秒
        for count, result in results.items():
            self.assertLess(
                result["avg_time_per_message"], 
                10.0, 
                f"发送{count}条消息的平均时间应该小于10毫秒"
            )
        
        return results
    
    def test_coordination_request_performance(self):
        """测试协调请求性能"""
        print("\n=== 测试协调请求性能 ===")
        
        request_counts = [5, 10, 25, 50]
        results = {}
        
        for count in request_counts:
            # 清空协调请求队列
            self.integration.db.communication_state["coordination_requests"] = []
            
            # 测量协调请求时间
            start_time = time.time()
            
            for i in range(count):
                self.integration.request_coordination(
                    DirectorLayer.DILING,
                    "performance_test",
                    {"index": i, "timestamp": time.time()}
                )
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[count] = {
                "total_time": duration,
                "avg_time_per_request": duration / count * 1000,  # 毫秒
                "requests_per_second": count / duration
            }
            
            print(f"处理{count}个协调请求:")
            print(f"  总时间: {duration:.3f}秒")
            print(f"  平均每个: {duration/count*1000:.3f}毫秒")
            print(f"  每秒处理: {count/duration:.1f}个")
        
        # 验证性能要求
        # 每个协调请求处理时间应该小于50毫秒
        for count, result in results.items():
            self.assertLess(
                result["avg_time_per_request"], 
                50.0, 
                f"处理{count}个协调请求的平均时间应该小于50毫秒"
            )
        
        return results
    
    def test_shared_context_performance(self):
        """测试共享上下文性能"""
        print("\n=== 测试共享上下文性能 ===")
        
        update_counts = [10, 50, 100, 200]
        results = {}
        
        for count in update_counts:
            # 测量上下文更新时间
            start_time = time.time()
            
            for i in range(count):
                self.integration.update_shared_context(
                    DirectorLayer.TIANDAO,
                    f"test_key_{i}",
                    {"value": i, "timestamp": time.time()}
                )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 测量上下文读取时间
            read_start = time.time()
            
            for i in range(count):
                context = self.integration.get_shared_context()
                # 验证数据存在
                self.assertIn("tiandao", context)
            
            read_end = time.time()
            read_duration = read_end - read_start
            
            results[count] = {
                "update_time": duration,
                "read_time": read_duration,
                "avg_update_time": duration / count * 1000,  # 毫秒
                "avg_read_time": read_duration / count * 1000,  # 毫秒
            }
            
            print(f"更新{count}个上下文项:")
            print(f"  更新总时间: {duration:.3f}秒")
            print(f"  读取总时间: {read_duration:.3f}秒")
            print(f"  平均更新时间: {duration/count*1000:.3f}毫秒")
            print(f"  平均读取时间: {read_duration/count*1000:.3f}毫秒")
        
        # 验证性能要求
        for count, result in results.items():
            self.assertLess(
                result["avg_update_time"], 
                5.0, 
                f"更新{count}个上下文项的平均时间应该小于5毫秒"
            )
            self.assertLess(
                result["avg_read_time"], 
                2.0, 
                f"读取{count}个上下文项的平均时间应该小于2毫秒"
            )
        
        return results
    
    def test_concurrent_operations(self):
        """测试并发操作性能"""
        print("\n=== 测试并发操作性能 ===")
        
        thread_counts = [2, 5, 10]
        operations_per_thread = 20
        results = {}
        
        for thread_count in thread_counts:
            print(f"\n测试{thread_count}个并发线程，每个线程{operations_per_thread}个操作:")
            
            # 清空队列
            self.integration.db.communication_state["message_queue"] = []
            self.integration.db.communication_state["coordination_requests"] = []
            
            # 并发测试函数
            def worker_thread(thread_id, results_list):
                """工作线程函数"""
                thread_start = time.time()
                
                for i in range(operations_per_thread):
                    # 发送消息
                    self.integration.send_message(
                        DirectorLayer.TIANDAO,
                        DirectorLayer.DILING,
                        f"concurrent_test_{thread_id}",
                        {"thread_id": thread_id, "operation": i},
                        "normal"
                    )
                    
                    # 更新上下文
                    self.integration.update_shared_context(
                        DirectorLayer.DILING,
                        f"thread_{thread_id}_op_{i}",
                        {"value": i, "thread": thread_id}
                    )
                    
                    # 协调请求（较少频率）
                    if i % 5 == 0:
                        self.integration.request_coordination(
                            DirectorLayer.QILING,
                            f"concurrent_coordination_{thread_id}",
                            {"thread_id": thread_id, "batch": i // 5}
                        )
                
                thread_end = time.time()
                results_list.append({
                    "thread_id": thread_id,
                    "duration": thread_end - thread_start
                })
            
            # 启动并发线程
            threads = []
            thread_results = []
            
            start_time = time.time()
            
            for thread_id in range(thread_count):
                thread = threading.Thread(
                    target=worker_thread, 
                    args=(thread_id, thread_results)
                )
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 分析结果
            thread_durations = [r["duration"] for r in thread_results]
            avg_thread_duration = statistics.mean(thread_durations)
            max_thread_duration = max(thread_durations)
            min_thread_duration = min(thread_durations)
            
            total_operations = thread_count * operations_per_thread
            operations_per_second = total_operations / total_duration
            
            results[thread_count] = {
                "total_duration": total_duration,
                "avg_thread_duration": avg_thread_duration,
                "max_thread_duration": max_thread_duration,
                "min_thread_duration": min_thread_duration,
                "operations_per_second": operations_per_second,
                "total_operations": total_operations
            }
            
            print(f"  总耗时: {total_duration:.3f}秒")
            print(f"  平均线程耗时: {avg_thread_duration:.3f}秒")
            print(f"  最长线程耗时: {max_thread_duration:.3f}秒")
            print(f"  最短线程耗时: {min_thread_duration:.3f}秒")
            print(f"  总操作数: {total_operations}")
            print(f"  每秒操作数: {operations_per_second:.1f}")
            
            # 验证消息和请求数量
            message_count = len(self.integration.db.communication_state["message_queue"])
            coordination_count = len(self.integration.db.communication_state["coordination_requests"])
            
            expected_messages = thread_count * operations_per_thread
            expected_coordinations = thread_count * (operations_per_thread // 5)
            
            print(f"  消息数量: {message_count} (期望: {expected_messages})")
            print(f"  协调请求数量: {coordination_count} (期望: {expected_coordinations})")
            
            # 验证数据完整性
            self.assertEqual(message_count, expected_messages, "消息数量应该正确")
            self.assertEqual(coordination_count, expected_coordinations, "协调请求数量应该正确")
        
        return results
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        print("\n=== 测试内存使用情况 ===")
        
        # 这是一个简化的内存测试
        # 在实际环境中可以使用psutil等库进行更详细的内存监控
        
        # 创建大量数据
        large_data_count = 1000
        
        print(f"创建{large_data_count}条大数据...")
        
        for i in range(large_data_count):
            # 创建较大的数据结构
            large_data = {
                "index": i,
                "data": "x" * 1000,  # 1KB字符串
                "nested": {
                    "level1": {"level2": {"level3": list(range(100))}}
                },
                "timestamp": time.time()
            }
            
            self.integration.send_message(
                DirectorLayer.TIANDAO,
                DirectorLayer.DILING,
                "memory_test",
                large_data,
                "normal"
            )
            
            self.integration.update_shared_context(
                DirectorLayer.TIANDAO,
                f"memory_test_{i}",
                large_data
            )
        
        print("大数据创建完成")
        
        # 检查系统是否仍然响应
        start_time = time.time()
        
        # 执行一些基本操作
        for i in range(10):
            self.integration.send_message(
                DirectorLayer.DILING,
                DirectorLayer.QILING,
                "response_test",
                {"test": i},
                "normal"
            )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"大数据环境下响应时间: {response_time:.3f}秒")
        
        # 验证系统仍然响应良好
        self.assertLess(response_time, 1.0, "大数据环境下系统应该仍然响应良好")
        
        return {
            "large_data_count": large_data_count,
            "response_time": response_time
        }


def run_comprehensive_performance_test():
    """运行综合性能测试"""
    print("\n" + "="*50)
    print("开始三层AI导演系统综合性能测试")
    print("="*50)
    
    import unittest
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加性能测试用例
    suite.addTest(DirectorPerformanceTest('test_message_sending_performance'))
    suite.addTest(DirectorPerformanceTest('test_coordination_request_performance'))
    suite.addTest(DirectorPerformanceTest('test_shared_context_performance'))
    suite.addTest(DirectorPerformanceTest('test_concurrent_operations'))
    suite.addTest(DirectorPerformanceTest('test_memory_usage'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "="*50)
    if result.wasSuccessful():
        print("所有性能测试通过！")
    else:
        print(f"性能测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    print("="*50)
    
    return result


if __name__ == "__main__":
    run_comprehensive_performance_test()
