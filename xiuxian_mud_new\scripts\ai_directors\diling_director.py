"""
地灵导演 - 区域环境和门派动态管理

负责修仙界的中观层面管理：
- 天象灵气系统（灵气潮汐、五行平衡、季节变化）
- 门派动态管理（门派任务、弟子管理、内部事务）
- 区域环境控制（秘境开启、资源刷新、生态变化）
- 地域特色事件（修仙大会、拍卖会、集市活动）
"""

import time
import random
from typing import Dict, Any, List, Optional

from evennia.utils import logger
from evennia.utils.search import search_object

from .base_director import BaseDirector, DirectorEventTypes
from systems.ai_decision_engine import AIDecisionEngine, DecisionContext, DirectorType
from systems.query_interfaces import AIDirectorQueryInterface
from systems.event_system import EventPriority
from systems.celestial_system import CelestialSystem


class DilingDirector(BaseDirector):
    """
    地灵导演 - 负责区域环境和门派动态管理
    
    决策周期：1分钟
    职责范围：天象灵气、门派动态、区域环境、地域事件
    """
    
    def at_script_creation(self):
        """地灵导演初始化"""
        self.director_type = "diling"
        self.decision_interval = 60  # 1分钟
        
        # 初始化天象灵气系统状态
        self.db.celestial_state = {
            "spiritual_tides": {
                "current_phase": "平稳期",
                "intensity": 1.0,
                "next_change": time.time() + 3600,
                "cycle_duration": 7200  # 2小时一个周期
            },
            "five_elements_balance": {
                "金": 1.0,
                "木": 1.0,
                "水": 1.0,
                "火": 1.0,
                "土": 1.0
            },
            "seasonal_effects": {
                "current_season": "春",
                "seasonal_bonus": {"木": 1.2, "水": 0.8},
                "weather_pattern": "晴朗"
            },
            "celestial_events": []
        }
        
        # 初始化门派动态状态
        self.db.sect_dynamics = {
            "青云门": {
                "activity_level": 1.0,
                "internal_harmony": 0.8,
                "resource_status": 1.0,
                "recent_events": [],
                "active_missions": []
            },
            "天音寺": {
                "activity_level": 0.9,
                "internal_harmony": 0.9,
                "resource_status": 0.8,
                "recent_events": [],
                "active_missions": []
            },
            "焚香谷": {
                "activity_level": 0.8,
                "internal_harmony": 0.7,
                "resource_status": 0.9,
                "recent_events": [],
                "active_missions": []
            }
        }
        
        # 初始化区域环境状态
        self.db.regional_environment = {
            "青云山": {
                "spiritual_density": 1.2,
                "element_dominance": "木",
                "secret_realms": ["青云秘境"],
                "resource_nodes": ["灵石矿脉", "千年灵芝"],
                "environmental_events": []
            },
            "天音山": {
                "spiritual_density": 1.1,
                "element_dominance": "金",
                "secret_realms": ["天音禅境"],
                "resource_nodes": ["佛光舍利", "菩提叶"],
                "environmental_events": []
            },
            "焚香谷": {
                "spiritual_density": 1.0,
                "element_dominance": "火",
                "secret_realms": ["焚香秘谷"],
                "resource_nodes": ["火灵石", "焚香草"],
                "environmental_events": []
            }
        }
        
        super().at_script_creation()
        logger.log_info("地灵导演已创建 - 掌管天象灵气与门派动态")
    
    def collect_context_data(self) -> Dict[str, Any]:
        """收集区域级决策所需的上下文数据"""
        try:
            # 获取区域活动摘要
            regional_activity = AIDirectorQueryInterface.get_regional_activity()
            
            # 获取门派状态分析
            sect_status = self._analyze_sect_status()
            
            # 获取天象灵气状态
            celestial_analysis = self._analyze_celestial_state()
            
            # 获取区域环境状态
            environment_analysis = self._analyze_regional_environment()
            
            # 获取最近的地域事件
            recent_events = self._get_recent_regional_events()
            
            context_data = {
                "regional_activity": regional_activity,
                "sect_status": sect_status,
                "celestial_analysis": celestial_analysis,
                "environment_analysis": environment_analysis,
                "recent_events": recent_events,
                "celestial_state": self.db.celestial_state,
                "sect_dynamics": self.db.sect_dynamics,
                "regional_environment": self.db.regional_environment,
                "current_time": time.time(),
                "game_season": self._get_current_season()
            }
            
            return context_data
            
        except Exception as e:
            logger.log_err(f"地灵导演收集上下文数据失败: {e}")
            return {}
    
    def make_ai_decision(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基于区域状态进行地灵级AI决策"""
        try:
            # 创建决策上下文
            decision_context = DecisionContext(
                director_type=DirectorType.DILING,
                timestamp=time.time(),
                world_state={
                    "celestial_state": context.get("celestial_state", {}),
                    "sect_dynamics": context.get("sect_dynamics", {}),
                    "regional_environment": context.get("regional_environment", {})
                },
                recent_events=context.get("recent_events", []),
                performance_metrics=self.get_performance_stats(),
                custom_data={
                    "sect_status": context.get("sect_status", {}),
                    "celestial_analysis": context.get("celestial_analysis", {}),
                    "environment_analysis": context.get("environment_analysis", {}),
                    "game_season": context.get("game_season", "春")
                }
            )
            
            # 获取AI决策引擎
            ai_engine = self.get_ai_decision_engine()
            if not ai_engine:
                return self._fallback_decision(context)
            
            # 进行AI决策
            decision_result = ai_engine.make_decision(decision_context)
            
            if decision_result:
                return {
                    "decision_type": decision_result.decision_type,
                    "priority": decision_result.priority,
                    "actions": decision_result.actions,
                    "effects": decision_result.effects,
                    "duration": decision_result.duration,
                    "conditions": decision_result.conditions,
                    "confidence": decision_result.confidence,
                    "reasoning": decision_result.reasoning
                }
            
            return None
            
        except Exception as e:
            logger.log_err(f"地灵导演AI决策失败: {e}")
            return self._fallback_decision(context)
    
    def execute_decision(self, decision: Dict[str, Any]):
        """执行地灵级决策"""
        try:
            decision_type = decision.get("decision_type", "unknown")
            actions = decision.get("actions", [])
            effects = decision.get("effects", {})
            
            logger.log_info(f"地灵导演执行决策: {decision_type}")
            logger.log_info(f"决策理由: {decision.get('reasoning', '未知')}")
            
            # 执行具体行动
            for action in actions:
                self._execute_action(action)
            
            # 应用区域效果
            self._apply_regional_effects(effects)
            
            # 记录区域事件
            self._record_regional_event(decision)
            
            # 发布地灵事件到事件总线
            event_type = self._determine_event_type(decision_type)
            self.publish_director_event(
                event_type,
                {
                    "decision": decision,
                    "timestamp": time.time(),
                    "celestial_state": self.db.celestial_state,
                    "affected_regions": self._get_affected_regions(decision)
                },
                EventPriority.NORMAL
            )
            
        except Exception as e:
            logger.log_err(f"地灵导演执行决策失败: {e}")
    
    def _execute_action(self, action: Dict[str, Any]):
        """执行具体的地灵行动"""
        action_type = action.get("type", "unknown")
        details = action.get("details", {})
        
        if action_type == "update_spiritual_tide":
            self._update_spiritual_tide(details)
        elif action_type == "adjust_five_elements":
            self._adjust_five_elements(details)
        elif action_type == "trigger_celestial_phenomenon":
            self._trigger_celestial_phenomenon(details)
        elif action_type == "initiate_sect_mission":
            self._initiate_sect_mission(details)
        elif action_type == "open_secret_realm":
            self._open_secret_realm(details)
        elif action_type == "refresh_regional_resources":
            self._refresh_regional_resources(details)
        elif action_type == "organize_regional_event":
            self._organize_regional_event(details)
        else:
            logger.log_warn(f"未知的地灵行动类型: {action_type}")
    
    def _update_spiritual_tide(self, details: Dict[str, Any]):
        """更新灵气潮汐"""
        region = details.get("region", "全域")
        element = details.get("element", "通用")
        intensity = details.get("intensity", 1.0)
        duration = details.get("duration", 1800)

        logger.log_info(f"地灵更新灵气潮汐: {region} {element}属性 强度{intensity}")

        # 集成天象灵气系统
        celestial_system = self._get_celestial_system()
        if celestial_system:
            from systems.celestial_system import TidePhase

            # 根据强度确定潮汐阶段
            if intensity < 0.8:
                phase = TidePhase.LOW
            elif intensity < 1.0:
                phase = TidePhase.FALLING
            elif intensity < 1.2:
                phase = TidePhase.RISING
            else:
                phase = TidePhase.HIGH

            celestial_system.trigger_spiritual_tide(phase, intensity, duration)

        # 更新灵气潮汐状态
        tides = self.db.celestial_state["spiritual_tides"]
        tides["intensity"] = intensity
        tides["current_phase"] = "变化期" if intensity != 1.0 else "平稳期"
        tides["next_change"] = time.time() + duration

        # 如果指定了元素，更新五行平衡
        if element in self.db.celestial_state["five_elements_balance"]:
            self.db.celestial_state["five_elements_balance"][element] = intensity
    
    def _adjust_five_elements(self, details: Dict[str, Any]):
        """调整五行平衡"""
        element = details.get("element", "木")
        adjustment = details.get("adjustment", 1.0)
        reason = details.get("reason", "自然调节")
        
        if element in self.db.celestial_state["five_elements_balance"]:
            old_value = self.db.celestial_state["five_elements_balance"][element]
            self.db.celestial_state["five_elements_balance"][element] *= adjustment
            
            logger.log_info(f"地灵调整五行平衡: {element} {old_value:.2f} -> {self.db.celestial_state['five_elements_balance'][element]:.2f} ({reason})")

            # 集成天象灵气系统
            celestial_system = self._get_celestial_system()
            if celestial_system:
                from systems.celestial_system import Element

                # 元素名称映射
                element_mapping = {
                    "金": Element.METAL,
                    "木": Element.WOOD,
                    "水": Element.WATER,
                    "火": Element.FIRE,
                    "土": Element.EARTH
                }

                if element in element_mapping:
                    celestial_system.adjust_element_balance(
                        element_mapping[element],
                        adjustment,
                        duration=3600,
                        reason=reason
                    )

    def _get_celestial_system(self):
        """获取天象灵气系统"""
        try:
            from systems.celestial_system import get_celestial_system
            return get_celestial_system()
        except ImportError:
            logger.log_warn("天象灵气系统未找到")
            return None

    def _trigger_celestial_phenomenon(self, details: Dict[str, Any]):
        """触发天象现象"""
        phenomenon_type = details.get("phenomenon_type", "灵气异象")
        location = details.get("location", "天空")
        duration = details.get("duration", 3600)
        
        logger.log_info(f"地灵触发天象现象: {phenomenon_type} 于 {location}，持续{duration}秒")
        
        # 记录天象事件
        celestial_event = {
            "timestamp": time.time(),
            "type": phenomenon_type,
            "location": location,
            "duration": duration,
            "status": "active"
        }
        self.db.celestial_state["celestial_events"].append(celestial_event)
    
    def _initiate_sect_mission(self, details: Dict[str, Any]):
        """发起门派任务"""
        sect_name = details.get("sect_name", "青云门")
        mission_type = details.get("mission_type", "日常任务")
        difficulty = details.get("difficulty", "普通")
        
        logger.log_info(f"地灵为{sect_name}发起任务: {mission_type} (难度: {difficulty})")
        
        if sect_name in self.db.sect_dynamics:
            mission = {
                "timestamp": time.time(),
                "type": mission_type,
                "difficulty": difficulty,
                "status": "available"
            }
            self.db.sect_dynamics[sect_name]["active_missions"].append(mission)
            self.db.sect_dynamics[sect_name]["activity_level"] *= 1.05
    
    def _open_secret_realm(self, details: Dict[str, Any]):
        """开启秘境"""
        realm_name = details.get("realm_name", "神秘秘境")
        region = details.get("region", "青云山")
        duration = details.get("duration", 7200)
        
        logger.log_info(f"地灵开启秘境: {realm_name} 于 {region}，开放{duration}秒")
        
        if region in self.db.regional_environment:
            if realm_name not in self.db.regional_environment[region]["secret_realms"]:
                self.db.regional_environment[region]["secret_realms"].append(realm_name)
            
            # 记录环境事件
            env_event = {
                "timestamp": time.time(),
                "type": "secret_realm_opened",
                "realm_name": realm_name,
                "duration": duration
            }
            self.db.regional_environment[region]["environmental_events"].append(env_event)
    
    def _refresh_regional_resources(self, details: Dict[str, Any]):
        """刷新区域资源"""
        region = details.get("region", "青云山")
        resource_type = details.get("resource_type", "灵草")
        quantity = details.get("quantity", 1.0)
        
        logger.log_info(f"地灵刷新区域资源: {region} {resource_type} 数量倍率{quantity}")
        
        if region in self.db.regional_environment:
            self.db.regional_environment[region]["spiritual_density"] *= quantity
    
    def _organize_regional_event(self, details: Dict[str, Any]):
        """组织地域事件"""
        event_type = details.get("event_type", "修仙集市")
        location = details.get("location", "青云山")
        duration = details.get("duration", 3600)
        
        logger.log_info(f"地灵组织地域事件: {event_type} 于 {location}，持续{duration}秒")
    
    def _apply_regional_effects(self, effects: Dict[str, Any]):
        """应用区域效果"""
        for effect_type, value in effects.items():
            if effect_type.startswith("regional_"):
                region_effect = effect_type.replace("regional_", "")
                # 应用到所有区域
                for region in self.db.regional_environment:
                    if region_effect == "spiritual_density":
                        self.db.regional_environment[region]["spiritual_density"] *= value
            elif effect_type.endswith("_energy"):
                element = effect_type.replace("_energy", "")
                if element in self.db.celestial_state["five_elements_balance"]:
                    self.db.celestial_state["five_elements_balance"][element] *= value
    
    def _record_regional_event(self, decision: Dict[str, Any]):
        """记录区域事件"""
        event_record = {
            "timestamp": time.time(),
            "decision_type": decision.get("decision_type"),
            "reasoning": decision.get("reasoning"),
            "effects": decision.get("effects"),
            "confidence": decision.get("confidence")
        }
        
        # 记录到相关区域
        affected_regions = self._get_affected_regions(decision)
        for region in affected_regions:
            if region in self.db.regional_environment:
                events = self.db.regional_environment[region]["environmental_events"]
                events.append(event_record)
                
                # 保留最近50个事件
                if len(events) > 50:
                    self.db.regional_environment[region]["environmental_events"] = events[-50:]
    
    def _analyze_sect_status(self) -> Dict[str, Any]:
        """分析门派状态"""
        sect_analysis = {}
        
        for sect_name, sect_data in self.db.sect_dynamics.items():
            sect_analysis[sect_name] = {
                "activity_level": sect_data["activity_level"],
                "internal_harmony": sect_data["internal_harmony"],
                "resource_status": sect_data["resource_status"],
                "active_missions_count": len(sect_data["active_missions"]),
                "recent_events_count": len(sect_data["recent_events"]),
                "overall_health": (
                    sect_data["activity_level"] + 
                    sect_data["internal_harmony"] + 
                    sect_data["resource_status"]
                ) / 3.0
            }
        
        return sect_analysis
    
    def _analyze_celestial_state(self) -> Dict[str, Any]:
        """分析天象灵气状态"""
        celestial_state = self.db.celestial_state
        
        # 计算五行平衡度
        elements = celestial_state["five_elements_balance"]
        element_values = list(elements.values())
        balance_variance = sum((v - 1.0) ** 2 for v in element_values) / len(element_values)
        
        return {
            "spiritual_tide_intensity": celestial_state["spiritual_tides"]["intensity"],
            "tide_phase": celestial_state["spiritual_tides"]["current_phase"],
            "five_elements_balance": elements,
            "balance_variance": balance_variance,
            "active_celestial_events": len(celestial_state["celestial_events"]),
            "seasonal_effects": celestial_state["seasonal_effects"],
            "overall_stability": 1.0 / (1.0 + balance_variance)
        }
    
    def _analyze_regional_environment(self) -> Dict[str, Any]:
        """分析区域环境状态"""
        environment_analysis = {}
        
        for region_name, region_data in self.db.regional_environment.items():
            environment_analysis[region_name] = {
                "spiritual_density": region_data["spiritual_density"],
                "element_dominance": region_data["element_dominance"],
                "secret_realms_count": len(region_data["secret_realms"]),
                "resource_nodes_count": len(region_data["resource_nodes"]),
                "recent_events_count": len(region_data["environmental_events"]),
                "environmental_health": min(2.0, region_data["spiritual_density"])
            }
        
        return environment_analysis
    
    def _get_recent_regional_events(self) -> List[Dict[str, Any]]:
        """获取最近的区域事件"""
        all_events = []
        
        for region_data in self.db.regional_environment.values():
            all_events.extend(region_data["environmental_events"])
        
        # 按时间排序，返回最近20个事件
        all_events.sort(key=lambda x: x.get("timestamp", 0), reverse=True)
        return all_events[:20]
    
    def _get_current_season(self) -> str:
        """获取当前季节"""
        # 简化的季节系统
        real_time = time.time()
        day_of_year = (int(real_time / 86400) % 365)
        
        if day_of_year < 91:
            return "春"
        elif day_of_year < 182:
            return "夏"
        elif day_of_year < 273:
            return "秋"
        else:
            return "冬"
    
    def _determine_event_type(self, decision_type: str) -> str:
        """根据决策类型确定事件类型"""
        if "celestial" in decision_type or "spiritual" in decision_type:
            return DirectorEventTypes.CELESTIAL_EVENT
        elif "tide" in decision_type:
            return DirectorEventTypes.SPIRITUAL_TIDE_CHANGE
        elif "sect" in decision_type:
            return DirectorEventTypes.SECT_DYNAMICS_UPDATE
        else:
            return DirectorEventTypes.REGIONAL_ENVIRONMENT_CHANGE
    
    def _get_affected_regions(self, decision: Dict[str, Any]) -> List[str]:
        """获取决策影响的区域"""
        # 简化实现，返回所有区域
        return list(self.db.regional_environment.keys())
    
    def _fallback_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """AI不可用时的备用决策"""
        # 基于规则的简单决策
        celestial_analysis = context.get("celestial_analysis", {})
        balance_variance = celestial_analysis.get("balance_variance", 0.0)
        
        if balance_variance > 0.5:
            # 五行失衡，需要调节
            elements = celestial_analysis.get("five_elements_balance", {})
            if elements:
                # 找到最失衡的元素
                most_unbalanced = max(elements.items(), key=lambda x: abs(x[1] - 1.0))
                adjustment = 0.9 if most_unbalanced[1] > 1.0 else 1.1
                
                return {
                    "decision_type": "balance_adjustment",
                    "priority": "normal",
                    "actions": [
                        {
                            "type": "adjust_five_elements",
                            "details": {
                                "element": most_unbalanced[0],
                                "adjustment": adjustment,
                                "reason": "自动平衡调节"
                            }
                        }
                    ],
                    "effects": {f"{most_unbalanced[0]}_energy": adjustment},
                    "duration": 1800,
                    "conditions": ["五行失衡"],
                    "confidence": 0.7,
                    "reasoning": f"检测到{most_unbalanced[0]}元素失衡，进行自动调节"
                }
        
        # 默认维持现状
        return {
            "decision_type": "maintain_balance",
            "priority": "low",
            "actions": [],
            "effects": {},
            "duration": 60,
            "conditions": [],
            "confidence": 0.5,
            "reasoning": "天象灵气平衡，维持现状"
        }
