"""
智能测试数据生成器

基于仙侠世界语义的AI驱动测试数据生成：
- 角色数据生成（姓名、境界、门派、属性）
- 地点数据生成（名称、环境、灵气等级）
- 事件场景生成（修炼、战斗、社交、探索）
- NPC数据生成（身份、性格、关系）
- 物品数据生成（法宝、丹药、功法）
"""

import random
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from evennia.utils.create import create_object, create_script
from evennia.utils.search import search_object, search_script
from evennia.utils.logger import log_info, log_err

from .integration_test_framework import create_test_character, create_test_room, create_test_npc
from ..systems.event_system import BaseEvent


@dataclass
class GeneratedCharacterData:
    """生成的角色数据"""
    name: str
    realm: str
    sect: str
    element: str
    personality: str
    background: str
    cultivation_progress: int
    attributes: Dict[str, Any]


@dataclass
class GeneratedLocationData:
    """生成的地点数据"""
    name: str
    description: str
    location_type: str
    spiritual_level: str
    environment: Dict[str, str]
    special_features: List[str]


@dataclass
class GeneratedEventData:
    """生成的事件数据"""
    event_type: str
    description: str
    participants: List[str]
    location: str
    context: Dict[str, Any]
    expected_outcomes: List[str]


class XianxiaTestDataGenerator:
    """仙侠测试数据生成器"""
    
    def __init__(self):
        # 仙侠世界基础数据
        self.character_surnames = [
            "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
            "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
            "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧"
        ]
        
        self.character_given_names = [
            "云天", "星河", "明月", "清风", "雪莲", "紫霞", "青山", "碧海",
            "金龙", "银凤", "玉麒", "火凰", "冰心", "雷霆", "风暴", "山岳",
            "流水", "烈焰", "寒冰", "神雷", "天剑", "地刀", "人棍", "鬼爪",
            "仙掌", "魔拳", "圣光", "暗影", "虚空", "混沌", "太极", "无极"
        ]
        
        self.cultivation_realms = [
            "练气一层", "练气二层", "练气三层", "练气四层", "练气五层",
            "练气六层", "练气七层", "练气八层", "练气九层", "练气大圆满",
            "筑基初期", "筑基中期", "筑基后期", "筑基大圆满",
            "金丹初期", "金丹中期", "金丹后期", "金丹大圆满",
            "元婴初期", "元婴中期", "元婴后期", "元婴大圆满",
            "化神初期", "化神中期", "化神后期", "化神大圆满"
        ]
        
        self.sects = [
            "青云宗", "天剑门", "万花谷", "血魔教", "太玄门", "五行宗",
            "星辰阁", "雷音寺", "冰雪宫", "烈火门", "风雷堂", "山海派",
            "天机门", "地煞宗", "人皇殿", "鬼王府", "仙灵观", "魔神殿",
            "圣光教", "暗影盟", "虚空门", "混沌宗", "太极院", "无极宫"
        ]
        
        self.elements = [
            "金", "木", "水", "火", "土", "风", "雷", "冰", "光", "暗",
            "空间", "时间", "生命", "死亡", "混沌", "秩序"
        ]
        
        self.personalities = [
            "冷静沉着", "热情开朗", "孤傲不群", "温和善良", "狡猾多变",
            "正直刚毅", "阴险狠毒", "天真烂漫", "深沉内敛", "豪爽大气",
            "谨慎小心", "冲动鲁莽", "智慧过人", "愚钝憨厚", "高傲自大",
            "谦逊有礼", "残忍嗜血", "慈悲为怀", "贪婪无度", "淡泊名利"
        ]
        
        self.location_types = [
            "宗门", "城市", "村庄", "山峰", "峡谷", "森林", "沙漠", "海岛",
            "洞府", "秘境", "遗迹", "禁地", "战场", "市集", "客栈", "药铺",
            "炼器坊", "藏书阁", "练功房", "丹房", "阵法台", "传送阵"
        ]
        
        self.spiritual_levels = [
            "极低", "低", "中等", "高", "极高", "传说"
        ]
        
        self.event_types = [
            "修炼突破", "门派任务", "历练冒险", "宝物争夺", "仇敌对决",
            "师门传承", "奇遇机缘", "天劫渡劫", "秘境探索", "丹药炼制",
            "法宝炼制", "阵法布置", "妖兽猎杀", "魔族入侵", "仙人降临",
            "古迹发现", "功法传授", "比武大会", "联姻结盟", "背叛复仇"
        ]
    
    def generate_character_name(self) -> str:
        """生成角色姓名"""
        surname = random.choice(self.character_surnames)
        given_name = random.choice(self.character_given_names)
        return f"{surname}{given_name}"
    
    def generate_character_data(self, count: int = 1) -> List[GeneratedCharacterData]:
        """生成角色数据"""
        characters = []
        
        for i in range(count):
            name = self.generate_character_name()
            realm = random.choice(self.cultivation_realms)
            sect = random.choice(self.sects)
            element = random.choice(self.elements)
            personality = random.choice(self.personalities)
            
            # 生成背景故事
            background = self._generate_character_background(name, sect, realm)
            
            # 生成修炼进度
            realm_index = self.cultivation_realms.index(realm)
            base_progress = realm_index * 100
            cultivation_progress = base_progress + random.randint(0, 99)
            
            # 生成属性
            attributes = self._generate_character_attributes(realm, element)
            
            character = GeneratedCharacterData(
                name=name,
                realm=realm,
                sect=sect,
                element=element,
                personality=personality,
                background=background,
                cultivation_progress=cultivation_progress,
                attributes=attributes
            )
            
            characters.append(character)
        
        return characters
    
    def _generate_character_background(self, name: str, sect: str, realm: str) -> str:
        """生成角色背景故事"""
        backgrounds = [
            f"{name}出身{sect}，自幼天赋异禀，修炼至{realm}境界。",
            f"{name}原为散修，后加入{sect}，凭借不懈努力达到{realm}。",
            f"{name}是{sect}长老之子，继承家族血脉，现已修炼到{realm}。",
            f"{name}曾是{sect}弟子，因故离宗，独自修炼至{realm}境界。",
            f"{name}天生异象，被{sect}收为亲传弟子，现为{realm}修士。"
        ]
        return random.choice(backgrounds)
    
    def _generate_character_attributes(self, realm: str, element: str) -> Dict[str, Any]:
        """生成角色属性"""
        realm_index = self.cultivation_realms.index(realm)
        base_value = 50 + realm_index * 10
        
        attributes = {
            "strength": base_value + random.randint(-10, 10),
            "agility": base_value + random.randint(-10, 10),
            "intelligence": base_value + random.randint(-10, 10),
            "constitution": base_value + random.randint(-10, 10),
            "spiritual_power": base_value + random.randint(0, 20),
            "element_affinity": element,
            "combat_experience": random.randint(0, 100),
            "reputation": random.randint(-50, 100)
        }
        
        return attributes
    
    def generate_location_data(self, count: int = 1) -> List[GeneratedLocationData]:
        """生成地点数据"""
        locations = []
        
        for i in range(count):
            location_type = random.choice(self.location_types)
            name = self._generate_location_name(location_type)
            description = self._generate_location_description(name, location_type)
            spiritual_level = random.choice(self.spiritual_levels)
            
            # 生成环境属性
            environment = self._generate_location_environment(location_type, spiritual_level)
            
            # 生成特殊特征
            special_features = self._generate_location_features(location_type)
            
            location = GeneratedLocationData(
                name=name,
                description=description,
                location_type=location_type,
                spiritual_level=spiritual_level,
                environment=environment,
                special_features=special_features
            )
            
            locations.append(location)
        
        return locations
    
    def _generate_location_name(self, location_type: str) -> str:
        """生成地点名称"""
        prefixes = {
            "宗门": ["青云", "天剑", "万花", "血魔", "太玄"],
            "城市": ["天元", "星辰", "雷音", "冰雪", "烈火"],
            "山峰": ["青龙", "白虎", "朱雀", "玄武", "麒麟"],
            "峡谷": ["幽冥", "天堑", "龙吟", "凤鸣", "雷鸣"],
            "森林": ["迷雾", "暗影", "生命", "死亡", "混沌"]
        }
        
        suffixes = {
            "宗门": ["宗", "门", "派", "教", "殿"],
            "城市": ["城", "镇", "坊", "市", "都"],
            "山峰": ["山", "峰", "岭", "崖", "台"],
            "峡谷": ["谷", "涧", "渊", "壑", "沟"],
            "森林": ["林", "森", "木", "丛", "园"]
        }
        
        prefix_list = prefixes.get(location_type, ["神秘", "古老", "传说"])
        suffix_list = suffixes.get(location_type, ["地", "所", "处", "境", "域"])
        
        prefix = random.choice(prefix_list)
        suffix = random.choice(suffix_list)
        
        return f"{prefix}{suffix}"
    
    def _generate_location_description(self, name: str, location_type: str) -> str:
        """生成地点描述"""
        descriptions = {
            "宗门": f"{name}坐落于灵山之上，云雾缭绕，仙气飘渺。",
            "城市": f"{name}繁华热闹，商贾云集，是修士们的重要聚集地。",
            "山峰": f"{name}高耸入云，山势险峻，蕴含着强大的天地灵气。",
            "峡谷": f"{name}深不见底，两壁陡峭，常有奇异现象发生。",
            "森林": f"{name}古木参天，生机盎然，隐藏着无数秘密。"
        }
        
        return descriptions.get(location_type, f"{name}是一处神秘的所在。")
    
    def _generate_location_environment(self, location_type: str, spiritual_level: str) -> Dict[str, str]:
        """生成地点环境属性"""
        base_environments = {
            "宗门": {"climate": "温和", "terrain": "山地", "vegetation": "丰富"},
            "城市": {"climate": "适宜", "terrain": "平原", "vegetation": "人工"},
            "山峰": {"climate": "寒冷", "terrain": "高山", "vegetation": "稀少"},
            "峡谷": {"climate": "阴凉", "terrain": "峡谷", "vegetation": "特殊"},
            "森林": {"climate": "湿润", "terrain": "丘陵", "vegetation": "茂密"}
        }
        
        environment = base_environments.get(location_type, {
            "climate": "未知", "terrain": "特殊", "vegetation": "神秘"
        }).copy()
        
        # 根据灵气等级调整环境
        spiritual_modifiers = {
            "极低": {"spiritual_pollution": "严重"},
            "低": {"spiritual_flow": "缓慢"},
            "中等": {"spiritual_balance": "稳定"},
            "高": {"spiritual_enhancement": "明显"},
            "极高": {"spiritual_resonance": "强烈"},
            "传说": {"spiritual_miracle": "常现"}
        }
        
        environment.update(spiritual_modifiers.get(spiritual_level, {}))
        
        return environment
    
    def _generate_location_features(self, location_type: str) -> List[str]:
        """生成地点特殊特征"""
        feature_pools = {
            "宗门": ["护山大阵", "灵脉汇聚", "传承石碑", "试炼之地"],
            "城市": ["传送阵", "拍卖行", "任务大厅", "修士客栈"],
            "山峰": ["天然洞府", "灵药生长", "妖兽栖息", "天劫频发"],
            "峡谷": ["空间裂缝", "回音壁", "迷幻阵法", "古代遗迹"],
            "森林": ["迷雾弥漫", "生命之泉", "精灵居所", "时空扭曲"]
        }
        
        features = feature_pools.get(location_type, ["神秘力量", "未知现象"])
        return random.sample(features, min(len(features), random.randint(1, 3)))
    
    def generate_event_scenarios(self, count: int = 1) -> List[GeneratedEventData]:
        """生成事件场景"""
        events = []
        
        for i in range(count):
            event_type = random.choice(self.event_types)
            description = self._generate_event_description(event_type)
            participants = self._generate_event_participants(event_type)
            location = self._generate_event_location(event_type)
            context = self._generate_event_context(event_type)
            expected_outcomes = self._generate_event_outcomes(event_type)
            
            event = GeneratedEventData(
                event_type=event_type,
                description=description,
                participants=participants,
                location=location,
                context=context,
                expected_outcomes=expected_outcomes
            )
            
            events.append(event)
        
        return events
    
    def _generate_event_description(self, event_type: str) -> str:
        """生成事件描述"""
        descriptions = {
            "修炼突破": "修士在关键时刻突破境界瓶颈，引发天地异象。",
            "门派任务": "宗门长老发布重要任务，需要弟子前往完成。",
            "历练冒险": "年轻修士踏出宗门，开始人生第一次历练之旅。",
            "宝物争夺": "传说中的神器现世，引发各方势力争夺。",
            "仇敌对决": "宿敌相遇，新仇旧恨一并爆发，决一死战。"
        }
        
        return descriptions.get(event_type, f"发生了{event_type}相关的重要事件。")
    
    def _generate_event_participants(self, event_type: str) -> List[str]:
        """生成事件参与者"""
        participant_counts = {
            "修炼突破": 1,
            "门派任务": random.randint(2, 5),
            "历练冒险": random.randint(1, 3),
            "宝物争夺": random.randint(3, 8),
            "仇敌对决": 2
        }
        
        count = participant_counts.get(event_type, random.randint(1, 4))
        participants = []
        
        for i in range(count):
            participants.append(f"participant_{i}")
        
        return participants
    
    def _generate_event_location(self, event_type: str) -> str:
        """生成事件地点"""
        location_preferences = {
            "修炼突破": ["洞府", "秘境", "灵脉"],
            "门派任务": ["宗门", "城市", "村庄"],
            "历练冒险": ["森林", "山峰", "峡谷"],
            "宝物争夺": ["遗迹", "禁地", "秘境"],
            "仇敌对决": ["战场", "荒野", "峡谷"]
        }
        
        preferred_locations = location_preferences.get(event_type, self.location_types)
        return random.choice(preferred_locations)
    
    def _generate_event_context(self, event_type: str) -> Dict[str, Any]:
        """生成事件上下文"""
        contexts = {
            "修炼突破": {
                "trigger": "长期修炼积累",
                "difficulty": random.choice(["简单", "中等", "困难", "极难"]),
                "time_pressure": random.choice(["无", "轻微", "中等", "紧急"]),
                "external_factors": random.choice(["无", "天象变化", "他人干扰", "环境影响"])
            },
            "门派任务": {
                "urgency": random.choice(["低", "中", "高", "紧急"]),
                "reward": random.choice(["功法", "丹药", "法宝", "灵石"]),
                "risk_level": random.choice(["安全", "一般", "危险", "极危"]),
                "completion_time": random.choice(["一天", "三天", "一周", "一月"])
            }
        }
        
        return contexts.get(event_type, {"type": event_type, "complexity": "中等"})
    
    def _generate_event_outcomes(self, event_type: str) -> List[str]:
        """生成事件预期结果"""
        outcomes = {
            "修炼突破": ["境界提升", "实力增强", "感悟加深", "声望提高"],
            "门派任务": ["任务完成", "获得奖励", "经验增长", "关系改善"],
            "历练冒险": ["见识增长", "实战经验", "意外收获", "性格磨练"],
            "宝物争夺": ["获得宝物", "结仇立敌", "联盟形成", "实力展现"],
            "仇敌对决": ["恩怨了结", "胜负分明", "生死决断", "因果循环"]
        }
        
        possible_outcomes = outcomes.get(event_type, ["未知结果"])
        return random.sample(possible_outcomes, min(len(possible_outcomes), random.randint(1, 3)))
    
    def create_test_objects_from_data(self, 
                                    character_data: List[GeneratedCharacterData] = None,
                                    location_data: List[GeneratedLocationData] = None) -> Dict[str, List]:
        """根据生成的数据创建测试对象"""
        created_objects = {
            "characters": [],
            "locations": [],
            "npcs": []
        }
        
        # 创建地点对象
        if location_data:
            for loc_data in location_data:
                room = create_test_room(loc_data.name.replace(" ", "_"))
                room.db.desc = loc_data.description
                
                # 设置标签
                room.tags.add(loc_data.location_type, category="location_type")
                room.tags.add(loc_data.spiritual_level, category="spiritual_level")
                
                for env_key, env_value in loc_data.environment.items():
                    room.tags.add(env_value, category=env_key)
                
                for feature in loc_data.special_features:
                    room.tags.add(feature, category="special_feature")
                
                created_objects["locations"].append(room)
        
        # 创建角色对象
        if character_data:
            default_room = created_objects["locations"][0] if created_objects["locations"] else None
            
            for char_data in character_data:
                char = create_test_character(char_data.name.replace(" ", "_"), default_room)
                char.db.desc = char_data.background
                
                # 设置标签
                char.tags.add(char_data.realm, category="realm")
                char.tags.add(char_data.sect, category="sect")
                char.tags.add(char_data.element, category="element")
                char.tags.add(char_data.personality, category="personality")
                char.tags.add(str(char_data.cultivation_progress), category="cultivation_progress")
                
                # 设置属性
                for attr_key, attr_value in char_data.attributes.items():
                    char.tags.add(str(attr_value), category=attr_key)
                
                created_objects["characters"].append(char)
        
        return created_objects
    
    def generate_test_scenario(self, scenario_type: str = "comprehensive") -> Dict[str, Any]:
        """生成完整测试场景"""
        scenarios = {
            "comprehensive": self._generate_comprehensive_scenario,
            "combat": self._generate_combat_scenario,
            "social": self._generate_social_scenario,
            "cultivation": self._generate_cultivation_scenario,
            "exploration": self._generate_exploration_scenario
        }
        
        generator = scenarios.get(scenario_type, self._generate_comprehensive_scenario)
        return generator()
    
    def _generate_comprehensive_scenario(self) -> Dict[str, Any]:
        """生成综合测试场景"""
        characters = self.generate_character_data(5)
        locations = self.generate_location_data(3)
        events = self.generate_event_scenarios(10)
        
        return {
            "scenario_type": "comprehensive",
            "description": "包含多种元素的综合测试场景",
            "characters": characters,
            "locations": locations,
            "events": events,
            "duration": "30分钟",
            "complexity": "高"
        }
    
    def _generate_combat_scenario(self) -> Dict[str, Any]:
        """生成战斗测试场景"""
        # 生成战斗相关的角色和地点
        fighters = self.generate_character_data(4)
        arena = self.generate_location_data(1)
        combat_events = [e for e in self.generate_event_scenarios(5) 
                        if "对决" in e.event_type or "争夺" in e.event_type]
        
        return {
            "scenario_type": "combat",
            "description": "专注于战斗系统的测试场景",
            "characters": fighters,
            "locations": arena,
            "events": combat_events,
            "duration": "15分钟",
            "complexity": "中等"
        }
    
    def _generate_social_scenario(self) -> Dict[str, Any]:
        """生成社交测试场景"""
        # 生成社交相关的角色和地点
        social_chars = self.generate_character_data(6)
        social_locations = self.generate_location_data(2)
        social_events = [e for e in self.generate_event_scenarios(8)
                        if "任务" in e.event_type or "冒险" in e.event_type]
        
        return {
            "scenario_type": "social",
            "description": "专注于社交系统的测试场景",
            "characters": social_chars,
            "locations": social_locations,
            "events": social_events,
            "duration": "20分钟",
            "complexity": "中等"
        }
    
    def _generate_cultivation_scenario(self) -> Dict[str, Any]:
        """生成修炼测试场景"""
        # 生成修炼相关的角色和地点
        cultivators = self.generate_character_data(3)
        cultivation_sites = self.generate_location_data(2)
        cultivation_events = [e for e in self.generate_event_scenarios(6)
                             if "修炼" in e.event_type or "突破" in e.event_type]
        
        return {
            "scenario_type": "cultivation",
            "description": "专注于修炼系统的测试场景",
            "characters": cultivators,
            "locations": cultivation_sites,
            "events": cultivation_events,
            "duration": "25分钟",
            "complexity": "高"
        }
    
    def _generate_exploration_scenario(self) -> Dict[str, Any]:
        """生成探索测试场景"""
        # 生成探索相关的角色和地点
        explorers = self.generate_character_data(3)
        mysterious_locations = self.generate_location_data(4)
        exploration_events = [e for e in self.generate_event_scenarios(7)
                             if "冒险" in e.event_type or "探索" in e.event_type]
        
        return {
            "scenario_type": "exploration",
            "description": "专注于探索系统的测试场景",
            "characters": explorers,
            "locations": mysterious_locations,
            "events": exploration_events,
            "duration": "20分钟",
            "complexity": "中等"
        }


# 全局测试数据生成器实例
test_data_generator = XianxiaTestDataGenerator()


def generate_quick_test_data(char_count: int = 3, loc_count: int = 2, event_count: int = 5) -> Dict[str, Any]:
    """快速生成测试数据"""
    return {
        "characters": test_data_generator.generate_character_data(char_count),
        "locations": test_data_generator.generate_location_data(loc_count),
        "events": test_data_generator.generate_event_scenarios(event_count)
    }


def create_test_environment_from_scenario(scenario: Dict[str, Any]) -> Dict[str, List]:
    """根据场景创建测试环境"""
    return test_data_generator.create_test_objects_from_data(
        character_data=scenario.get("characters"),
        location_data=scenario.get("locations")
    )


if __name__ == "__main__":
    # 测试数据生成器
    generator = XianxiaTestDataGenerator()
    
    # 生成测试数据
    chars = generator.generate_character_data(3)
    locs = generator.generate_location_data(2)
    events = generator.generate_event_scenarios(5)
    
    print("生成的角色数据:")
    for char in chars:
        print(f"  {char.name} - {char.realm} - {char.sect}")
    
    print("\n生成的地点数据:")
    for loc in locs:
        print(f"  {loc.name} - {loc.location_type} - {loc.spiritual_level}")
    
    print("\n生成的事件数据:")
    for event in events:
        print(f"  {event.event_type} - {event.description}")
