"""
基于Evennia的仙侠MUD性能监控系统

基于DefaultScript实现的统一性能监控系统，提供：
- 实时性能指标收集
- 多维度性能分析
- 自动性能优化建议
- 性能趋势预测

核心功能：
- 利用Evennia内置统计功能
- TagProperty查询性能监控
- 事件系统吞吐量监控
- Handler内存使用监控
- AI导演决策性能监控
"""

import time
import psutil
import statistics
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

from evennia import DefaultScript
from evennia.utils.logger import log_info, log_err
from evennia.server.sessionhandler import SESSIONS
from evennia import search_object_by_tag

from .tag_property_system import TagPropertyQueryManager
from .event_system import XianxiaEventBus
from .handler_system import HandlerMemoryManager
from ..scripts.ai_directors.base_director import BaseDirector


class PerformanceMetrics:
    """性能指标数据结构"""
    
    def __init__(self):
        self.timestamp = time.time()
        self.system_metrics = {}
        self.tagproperty_metrics = {}
        self.event_metrics = {}
        self.handler_metrics = {}
        self.ai_director_metrics = {}
        self.custom_metrics = {}


class PerformanceAnalyzer:
    """性能分析引擎"""
    
    def __init__(self):
        self.metrics_history = deque(maxlen=1000)  # 保留最近1000个数据点
        self.performance_baselines = {}
        self.trend_analysis = {}
        
    def add_metrics(self, metrics: PerformanceMetrics):
        """添加性能指标数据"""
        self.metrics_history.append(metrics)
        self._update_trend_analysis(metrics)
        
    def _update_trend_analysis(self, metrics: PerformanceMetrics):
        """更新趋势分析"""
        if len(self.metrics_history) < 10:
            return
            
        # 分析最近10个数据点的趋势
        recent_metrics = list(self.metrics_history)[-10:]
        
        # CPU使用率趋势
        cpu_values = [m.system_metrics.get('cpu_percent', 0) for m in recent_metrics]
        self.trend_analysis['cpu_trend'] = self._calculate_trend(cpu_values)
        
        # 内存使用趋势
        memory_values = [m.system_metrics.get('memory_percent', 0) for m in recent_metrics]
        self.trend_analysis['memory_trend'] = self._calculate_trend(memory_values)
        
        # TagProperty查询性能趋势
        tag_query_times = [m.tagproperty_metrics.get('avg_query_time', 0) for m in recent_metrics]
        self.trend_analysis['tagproperty_trend'] = self._calculate_trend(tag_query_times)
        
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return "stable"
            
        # 简单线性回归斜率
        n = len(values)
        x_mean = (n - 1) / 2
        y_mean = sum(values) / n
        
        numerator = sum((i - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((i - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return "stable"
            
        slope = numerator / denominator
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
            
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能分析报告"""
        if not self.metrics_history:
            return {"error": "No metrics data available"}
            
        latest_metrics = self.metrics_history[-1]
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_status": self._analyze_system_status(latest_metrics),
            "performance_trends": self.trend_analysis.copy(),
            "optimization_recommendations": self._generate_recommendations(),
            "performance_summary": self._generate_summary()
        }
        
        return report
        
    def _analyze_system_status(self, metrics: PerformanceMetrics) -> Dict[str, str]:
        """分析系统状态"""
        status = {}
        
        # CPU状态
        cpu_percent = metrics.system_metrics.get('cpu_percent', 0)
        if cpu_percent > 80:
            status['cpu'] = "high_load"
        elif cpu_percent > 50:
            status['cpu'] = "moderate_load"
        else:
            status['cpu'] = "normal"
            
        # 内存状态
        memory_percent = metrics.system_metrics.get('memory_percent', 0)
        if memory_percent > 85:
            status['memory'] = "high_usage"
        elif memory_percent > 60:
            status['memory'] = "moderate_usage"
        else:
            status['memory'] = "normal"
            
        # TagProperty查询状态
        avg_query_time = metrics.tagproperty_metrics.get('avg_query_time', 0)
        if avg_query_time > 0.1:
            status['tagproperty'] = "slow_queries"
        elif avg_query_time > 0.05:
            status['tagproperty'] = "moderate_performance"
        else:
            status['tagproperty'] = "optimal"
            
        return status
        
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if not self.metrics_history:
            return recommendations
            
        latest_metrics = self.metrics_history[-1]
        
        # CPU优化建议
        if latest_metrics.system_metrics.get('cpu_percent', 0) > 70:
            recommendations.append("考虑优化CPU密集型操作，如AI导演决策算法")
            
        # 内存优化建议
        if latest_metrics.system_metrics.get('memory_percent', 0) > 75:
            recommendations.append("建议启用Handler内存智能管理优化")
            
        # TagProperty优化建议
        if latest_metrics.tagproperty_metrics.get('avg_query_time', 0) > 0.05:
            recommendations.append("启用TagProperty智能缓存以提升查询性能")
            
        # 事件系统优化建议
        event_queue_size = latest_metrics.event_metrics.get('total_queue_size', 0)
        if event_queue_size > 100:
            recommendations.append("考虑优化事件总线批量处理策略")
            
        return recommendations
        
    def _generate_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        if len(self.metrics_history) < 2:
            return {}
            
        recent_metrics = list(self.metrics_history)[-10:]
        
        summary = {
            "avg_cpu_usage": statistics.mean(m.system_metrics.get('cpu_percent', 0) for m in recent_metrics),
            "avg_memory_usage": statistics.mean(m.system_metrics.get('memory_percent', 0) for m in recent_metrics),
            "avg_tagproperty_query_time": statistics.mean(m.tagproperty_metrics.get('avg_query_time', 0) for m in recent_metrics),
            "total_events_processed": sum(m.event_metrics.get('events_processed', 0) for m in recent_metrics),
            "active_sessions": recent_metrics[-1].system_metrics.get('active_sessions', 0)
        }
        
        return summary


class XianxiaPerformanceMonitor(DefaultScript):
    """
    仙侠MUD性能监控系统
    
    基于Evennia DefaultScript实现的统一性能监控，提供：
    - 实时系统性能监控
    - 各子系统性能分析
    - 自动优化建议生成
    - 性能趋势预测
    """
    
    def at_script_creation(self):
        """脚本创建时初始化"""
        self.key = "xiuxian_performance_monitor"
        self.desc = "仙侠MUD统一性能监控系统"
        self.interval = 30  # 30秒监控周期
        self.persistent = True
        self.start_delay = True
        
        # 初始化性能分析器
        self.analyzer = PerformanceAnalyzer()
        
        # 初始化监控配置
        self.db.monitoring_config = {
            "enabled": True,
            "detailed_logging": False,
            "alert_thresholds": {
                "cpu_percent": 80,
                "memory_percent": 85,
                "tagproperty_query_time": 0.1,
                "event_queue_size": 200
            }
        }
        
        # 初始化统计数据
        self.db.performance_stats = {
            "monitoring_start_time": time.time(),
            "total_monitoring_cycles": 0,
            "alerts_generated": 0,
            "last_optimization_check": time.time()
        }
        
        log_info("仙侠MUD性能监控系统已启动")
        
    def at_repeat(self):
        """定期执行性能监控"""
        if not self.db.monitoring_config.get("enabled", True):
            return
            
        try:
            # 收集性能指标
            metrics = self.collect_performance_metrics()
            
            # 添加到分析器
            self.analyzer.add_metrics(metrics)
            
            # 检查性能警报
            self.check_performance_alerts(metrics)
            
            # 更新统计数据
            self.db.performance_stats["total_monitoring_cycles"] += 1
            
            # 定期生成性能报告
            if time.time() - self.db.performance_stats["last_optimization_check"] > 300:  # 5分钟
                self.generate_and_log_performance_report()
                self.db.performance_stats["last_optimization_check"] = time.time()
                
        except Exception as e:
            log_err(f"性能监控执行出错: {e}")
            
    def collect_performance_metrics(self) -> PerformanceMetrics:
        """收集系统性能指标"""
        metrics = PerformanceMetrics()
        
        # 系统基础指标
        metrics.system_metrics = self.collect_system_metrics()
        
        # TagProperty查询性能
        metrics.tagproperty_metrics = self.collect_tagproperty_metrics()
        
        # 事件系统性能
        metrics.event_metrics = self.collect_event_metrics()
        
        # Handler内存使用
        metrics.handler_metrics = self.collect_handler_metrics()
        
        # AI导演性能
        metrics.ai_director_metrics = self.collect_ai_director_metrics()
        
        return metrics
        
    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统基础指标"""
        try:
            # 利用Evennia内置统计功能
            active_sessions = len(SESSIONS.get_sessions())
            
            # 系统资源使用
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            return {
                "active_sessions": active_sessions,
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_mb": memory.used / (1024 * 1024),
                "memory_available_mb": memory.available / (1024 * 1024)
            }
        except Exception as e:
            log_err(f"收集系统指标失败: {e}")
            return {}
            
    def collect_tagproperty_metrics(self) -> Dict[str, Any]:
        """收集TagProperty查询性能指标"""
        try:
            # 执行测试查询并测量性能
            start_time = time.perf_counter()
            
            # 执行几个典型查询
            test_queries = [
                lambda: search_object_by_tag(key="练气", category="境界等级"),
                lambda: search_object_by_tag(key="青云门", category="sect_territory"),
                lambda: search_object_by_tag(key="火", category="elemental_type")
            ]
            
            query_times = []
            for query_func in test_queries:
                query_start = time.perf_counter()
                try:
                    query_func()
                    query_time = time.perf_counter() - query_start
                    query_times.append(query_time)
                except Exception:
                    pass
                    
            avg_query_time = statistics.mean(query_times) if query_times else 0
            
            return {
                "avg_query_time": avg_query_time,
                "total_test_queries": len(query_times),
                "query_success_rate": len(query_times) / len(test_queries)
            }
        except Exception as e:
            log_err(f"收集TagProperty指标失败: {e}")
            return {}
            
    def collect_event_metrics(self) -> Dict[str, Any]:
        """收集事件系统性能指标"""
        try:
            # 获取事件总线实例
            event_bus = XianxiaEventBus.get_instance()
            if not event_bus:
                return {}
                
            # 收集事件队列统计
            total_queue_size = sum(len(queue) for queue in event_bus.event_queues.values())
            
            stats = event_bus.stats
            
            return {
                "total_queue_size": total_queue_size,
                "events_processed": stats.get("events_processed", 0),
                "events_failed": stats.get("events_failed", 0),
                "avg_processing_time": stats.get("avg_processing_time", 0),
                "last_processing_time": stats.get("last_processing_time", 0)
            }
        except Exception as e:
            log_err(f"收集事件系统指标失败: {e}")
            return {}
            
    def collect_handler_metrics(self) -> Dict[str, Any]:
        """收集Handler内存使用指标"""
        try:
            # 获取Handler内存管理器统计
            stats = HandlerMemoryManager._optimization_stats
            
            return {
                "total_handlers_created": stats.get("total_created", 0),
                "total_handlers_cleaned": stats.get("total_cleaned", 0),
                "memory_saved_mb": stats.get("memory_saved", 0) / (1024 * 1024),
                "cache_hit_rate": stats.get("cache_hits", 0) / max(stats.get("cache_hits", 0) + stats.get("cache_misses", 0), 1)
            }
        except Exception as e:
            log_err(f"收集Handler指标失败: {e}")
            return {}
            
    def collect_ai_director_metrics(self) -> Dict[str, Any]:
        """收集AI导演性能指标"""
        try:
            # 查找AI导演脚本
            from evennia import search_script
            
            directors = search_script("tiandao_director") + search_script("diling_director") + search_script("qiling_director")
            
            if not directors:
                return {}
                
            total_decisions = 0
            total_decision_time = 0
            active_directors = 0
            
            for director in directors:
                if hasattr(director, 'db') and director.db.performance_stats:
                    stats = director.db.performance_stats
                    total_decisions += stats.get("total_decisions", 0)
                    total_decision_time += stats.get("total_decision_time", 0)
                    if stats.get("is_active", False):
                        active_directors += 1
                        
            avg_decision_time = total_decision_time / max(total_decisions, 1)
            
            return {
                "active_directors": active_directors,
                "total_decisions": total_decisions,
                "avg_decision_time": avg_decision_time,
                "decisions_per_minute": total_decisions / max((time.time() - self.db.performance_stats["monitoring_start_time"]) / 60, 1)
            }
        except Exception as e:
            log_err(f"收集AI导演指标失败: {e}")
            return {}
            
    def check_performance_alerts(self, metrics: PerformanceMetrics):
        """检查性能警报"""
        thresholds = self.db.monitoring_config["alert_thresholds"]
        alerts = []
        
        # CPU使用率警报
        cpu_percent = metrics.system_metrics.get('cpu_percent', 0)
        if cpu_percent > thresholds["cpu_percent"]:
            alerts.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            
        # 内存使用警报
        memory_percent = metrics.system_metrics.get('memory_percent', 0)
        if memory_percent > thresholds["memory_percent"]:
            alerts.append(f"内存使用率过高: {memory_percent:.1f}%")
            
        # TagProperty查询性能警报
        avg_query_time = metrics.tagproperty_metrics.get('avg_query_time', 0)
        if avg_query_time > thresholds["tagproperty_query_time"]:
            alerts.append(f"TagProperty查询响应慢: {avg_query_time:.3f}秒")
            
        # 事件队列警报
        total_queue_size = metrics.event_metrics.get('total_queue_size', 0)
        if total_queue_size > thresholds["event_queue_size"]:
            alerts.append(f"事件队列积压: {total_queue_size}个事件")
            
        # 记录警报
        if alerts:
            self.db.performance_stats["alerts_generated"] += len(alerts)
            for alert in alerts:
                log_info(f"[性能警报] {alert}")
                
    def generate_and_log_performance_report(self):
        """生成并记录性能报告"""
        try:
            report = self.analyzer.generate_performance_report()
            
            if self.db.monitoring_config.get("detailed_logging", False):
                log_info(f"性能监控报告: {report}")
            else:
                # 简化日志
                summary = report.get("performance_summary", {})
                log_info(f"性能摘要 - CPU: {summary.get('avg_cpu_usage', 0):.1f}%, "
                        f"内存: {summary.get('avg_memory_usage', 0):.1f}%, "
                        f"TagProperty查询: {summary.get('avg_tagproperty_query_time', 0):.3f}秒")
                        
        except Exception as e:
            log_err(f"生成性能报告失败: {e}")
            
    def get_performance_report(self) -> Dict[str, Any]:
        """获取当前性能报告"""
        return self.analyzer.generate_performance_report()
        
    def enable_detailed_logging(self):
        """启用详细日志记录"""
        self.db.monitoring_config["detailed_logging"] = True
        log_info("已启用详细性能日志记录")
        
    def disable_detailed_logging(self):
        """禁用详细日志记录"""
        self.db.monitoring_config["detailed_logging"] = False
        log_info("已禁用详细性能日志记录")
        
    def update_alert_thresholds(self, **thresholds):
        """更新警报阈值"""
        self.db.monitoring_config["alert_thresholds"].update(thresholds)
        log_info(f"已更新性能警报阈值: {thresholds}")
