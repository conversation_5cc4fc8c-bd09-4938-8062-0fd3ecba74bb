"""
仙侠师徒关系管理系统

基于Evennia Tags和Attributes实现的师徒关系管理，包括：
- 师徒关系建立和解除
- 技能传承和修炼指导
- 师徒权限和互动机制
- 师徒事件自动发布

完全基于Evennia原生组件，与TagProperty系统和事件总线完美集成。
"""

import time
from typing import Dict, Any, List, Optional, Tuple
from evennia.utils import logger
from evennia import search


class MentorshipManager:
    """
    师徒关系管理器
    
    提供师徒关系的完整管理功能：
    - 基于Tags的关系标记（高性能查询）
    - 基于Attributes的详细数据存储
    - 师徒权限和互动机制
    - 与事件系统的完整集成
    """
    
    @staticmethod
    def establish_mentorship(master, disciple, **kwargs) -> bool:
        """
        建立师徒关系
        
        Args:
            master: 师父角色
            disciple: 弟子角色
            **kwargs: 额外的师徒关系数据
            
        Returns:
            bool: 建立是否成功
        """
        try:
            # 验证师徒关系的有效性
            if not MentorshipManager._validate_mentorship(master, disciple):
                return False
            
            # 使用Tags标记师徒关系（用于高性能查询）
            master.tags.add(f"master_of_{disciple.id}", category="mentorship")
            disciple.tags.add(f"disciple_of_{master.id}", category="mentorship")
            
            # 添加通用师徒身份标签
            master.tags.add("has_disciples", category="social_status")
            disciple.tags.add("has_master", category="social_status")
            
            # 使用Attributes存储详细师徒数据
            MentorshipManager._store_master_data(master, disciple, **kwargs)
            MentorshipManager._store_disciple_data(disciple, master, **kwargs)
            
            # 发布师徒关系建立事件
            from systems.social_events import publish_mentorship_event, SocialEventType
            publish_mentorship_event(
                master,
                disciple,
                SocialEventType.MENTORSHIP_ESTABLISHED.value,
                establishment_time=time.time(),
                **kwargs
            )
            
            logger.log_info(f"师徒关系建立: {master.key} -> {disciple.key}")
            return True
            
        except Exception as e:
            logger.log_err(f"建立师徒关系失败: {e}")
            return False
    
    @staticmethod
    def _validate_mentorship(master, disciple) -> bool:
        """验证师徒关系的有效性"""
        # 检查是否为同一角色
        if master.id == disciple.id:
            logger.log_warn("不能与自己建立师徒关系")
            return False
        
        # 检查弟子是否已有师父
        if MentorshipManager.get_master(disciple):
            logger.log_warn(f"{disciple.key} 已有师父")
            return False
        
        # 检查是否已存在师徒关系
        if MentorshipManager.is_master_of(master, disciple):
            logger.log_warn(f"{master.key} 已是 {disciple.key} 的师父")
            return False
        
        # 检查修为境界（师父应该比弟子高）
        master_realm = getattr(master, '修为境界', '练气')
        disciple_realm = getattr(disciple, '修为境界', '练气')
        
        # 简单的境界比较（可以根据需要扩展）
        realm_levels = ["练气", "筑基", "金丹", "元婴", "化神", "炼虚", "合体", "大乘", "渡劫", "仙人"]
        master_level = realm_levels.index(master_realm) if master_realm in realm_levels else 0
        disciple_level = realm_levels.index(disciple_realm) if disciple_realm in realm_levels else 0
        
        if master_level <= disciple_level:
            logger.log_warn(f"师父修为不能低于弟子 ({master_realm} vs {disciple_realm})")
            return False
        
        return True
    
    @staticmethod
    def _store_master_data(master, disciple, **kwargs):
        """存储师父的弟子数据"""
        # 获取现有弟子列表
        disciples = master.attributes.get("disciples", [], category="social")
        
        # 添加新弟子信息
        disciple_info = {
            "id": disciple.id,
            "name": disciple.key,
            "start_time": time.time(),
            "relationship_level": kwargs.get("relationship_level", "新收弟子"),
            "skills_taught": [],
            "guidance_sessions": 0,
            "last_interaction": time.time(),
            "notes": kwargs.get("notes", "")
        }
        
        disciples.append(disciple_info)
        master.attributes.add("disciples", disciples, category="social")
        
        # 更新师父统计
        master.attributes.add("total_disciples", len(disciples), category="social")
    
    @staticmethod
    def _store_disciple_data(disciple, master, **kwargs):
        """存储弟子的师父数据"""
        master_info = {
            "id": master.id,
            "name": master.key,
            "start_time": time.time(),
            "relationship_level": kwargs.get("relationship_level", "新收弟子"),
            "skills_learned": [],
            "guidance_received": 0,
            "last_interaction": time.time(),
            "notes": kwargs.get("notes", "")
        }
        
        disciple.attributes.add("master", master_info, category="social")
        disciple.attributes.add("mentorship_start_time", time.time(), category="social")
    
    @staticmethod
    def terminate_mentorship(master, disciple, reason="师徒决裂") -> bool:
        """
        终止师徒关系
        
        Args:
            master: 师父角色
            disciple: 弟子角色
            reason: 终止原因
            
        Returns:
            bool: 终止是否成功
        """
        try:
            # 移除Tags标记
            master.tags.remove(f"master_of_{disciple.id}", category="mentorship")
            disciple.tags.remove(f"disciple_of_{master.id}", category="mentorship")
            
            # 检查是否还有其他弟子/师父
            if not MentorshipManager.get_disciples(master):
                master.tags.remove("has_disciples", category="social_status")
            
            disciple.tags.remove("has_master", category="social_status")
            
            # 更新Attributes数据
            MentorshipManager._remove_master_data(master, disciple)
            MentorshipManager._remove_disciple_data(disciple)
            
            # 发布师徒关系终止事件
            from systems.social_events import publish_mentorship_event, SocialEventType
            publish_mentorship_event(
                master,
                disciple,
                SocialEventType.MENTORSHIP_TERMINATED.value,
                termination_time=time.time(),
                reason=reason
            )
            
            logger.log_info(f"师徒关系终止: {master.key} -> {disciple.key} (原因: {reason})")
            return True
            
        except Exception as e:
            logger.log_err(f"终止师徒关系失败: {e}")
            return False
    
    @staticmethod
    def _remove_master_data(master, disciple):
        """移除师父的弟子数据"""
        disciples = master.attributes.get("disciples", [], category="social")
        disciples = [d for d in disciples if d.get("id") != disciple.id]
        master.attributes.add("disciples", disciples, category="social")
        master.attributes.add("total_disciples", len(disciples), category="social")
    
    @staticmethod
    def _remove_disciple_data(disciple):
        """移除弟子的师父数据"""
        disciple.attributes.remove("master", category="social")
        disciple.attributes.remove("mentorship_start_time", category="social")
    
    @staticmethod
    def is_master_of(master, disciple) -> bool:
        """检查是否为师徒关系"""
        return master.tags.has(f"master_of_{disciple.id}", category="mentorship")
    
    @staticmethod
    def get_master(disciple) -> Optional[object]:
        """获取弟子的师父"""
        master_tags = disciple.tags.get(category="mentorship")
        for tag in master_tags:
            if tag.startswith("disciple_of_"):
                master_id = int(tag.split("_")[-1])
                masters = search.search_object(f"#{master_id}")
                if masters:
                    return masters[0]
        return None
    
    @staticmethod
    def get_disciples(master) -> List[object]:
        """获取师父的所有弟子"""
        disciples = []
        disciple_tags = master.tags.get(category="mentorship")
        
        for tag in disciple_tags:
            if tag.startswith("master_of_"):
                disciple_id = int(tag.split("_")[-1])
                disciple_objs = search.search_object(f"#{disciple_id}")
                if disciple_objs:
                    disciples.append(disciple_objs[0])
        
        return disciples
    
    @staticmethod
    def get_mentorship_info(master, disciple) -> Optional[Dict[str, Any]]:
        """获取师徒关系详细信息"""
        if not MentorshipManager.is_master_of(master, disciple):
            return None
        
        # 从师父的数据中获取信息
        disciples_data = master.attributes.get("disciples", [], category="social")
        disciple_data = None
        
        for d in disciples_data:
            if d.get("id") == disciple.id:
                disciple_data = d
                break
        
        if not disciple_data:
            return None
        
        return {
            "master": {
                "id": master.id,
                "name": master.key,
                "realm": getattr(master, '修为境界', '未知'),
                "sect": getattr(master, '门派归属', '无门派')
            },
            "disciple": {
                "id": disciple.id,
                "name": disciple.key,
                "realm": getattr(disciple, '修为境界', '未知'),
                "sect": getattr(disciple, '门派归属', '无门派')
            },
            "relationship": {
                "start_time": disciple_data.get("start_time", 0),
                "duration": time.time() - disciple_data.get("start_time", 0),
                "level": disciple_data.get("relationship_level", "新收弟子"),
                "skills_taught": disciple_data.get("skills_taught", []),
                "guidance_sessions": disciple_data.get("guidance_sessions", 0),
                "last_interaction": disciple_data.get("last_interaction", 0),
                "notes": disciple_data.get("notes", "")
            }
        }
    
    @staticmethod
    def teach_skill(master, disciple, skill_name: str, **kwargs) -> bool:
        """
        师父传授技能给弟子
        
        Args:
            master: 师父角色
            disciple: 弟子角色
            skill_name: 技能名称
            **kwargs: 额外的传授数据
            
        Returns:
            bool: 传授是否成功
        """
        try:
            if not MentorshipManager.is_master_of(master, disciple):
                logger.log_warn(f"{master.key} 不是 {disciple.key} 的师父")
                return False
            
            # 更新师父的弟子数据
            disciples_data = master.attributes.get("disciples", [], category="social")
            for d in disciples_data:
                if d.get("id") == disciple.id:
                    if skill_name not in d.get("skills_taught", []):
                        d["skills_taught"].append(skill_name)
                    d["last_interaction"] = time.time()
                    break
            
            master.attributes.add("disciples", disciples_data, category="social")
            
            # 更新弟子的师父数据
            master_data = disciple.attributes.get("master", {}, category="social")
            if "skills_learned" not in master_data:
                master_data["skills_learned"] = []
            
            if skill_name not in master_data["skills_learned"]:
                master_data["skills_learned"].append(skill_name)
            master_data["last_interaction"] = time.time()
            
            disciple.attributes.add("master", master_data, category="social")
            
            # 发布技能传授事件
            from systems.social_events import publish_mentorship_event, SocialEventType
            publish_mentorship_event(
                master,
                disciple,
                SocialEventType.SKILL_TEACHING.value,
                skill_name=skill_name,
                teaching_time=time.time(),
                **kwargs
            )
            
            logger.log_info(f"技能传授: {master.key} -> {disciple.key} ({skill_name})")
            return True
            
        except Exception as e:
            logger.log_err(f"技能传授失败: {e}")
            return False
    
    @staticmethod
    def provide_guidance(master, disciple, guidance_type: str, content: str, **kwargs) -> bool:
        """
        师父为弟子提供修炼指导
        
        Args:
            master: 师父角色
            disciple: 弟子角色
            guidance_type: 指导类型（修炼、战斗、心法等）
            content: 指导内容
            **kwargs: 额外的指导数据
            
        Returns:
            bool: 指导是否成功
        """
        try:
            if not MentorshipManager.is_master_of(master, disciple):
                logger.log_warn(f"{master.key} 不是 {disciple.key} 的师父")
                return False
            
            # 更新指导次数
            disciples_data = master.attributes.get("disciples", [], category="social")
            for d in disciples_data:
                if d.get("id") == disciple.id:
                    d["guidance_sessions"] = d.get("guidance_sessions", 0) + 1
                    d["last_interaction"] = time.time()
                    break
            
            master.attributes.add("disciples", disciples_data, category="social")
            
            # 更新弟子数据
            master_data = disciple.attributes.get("master", {}, category="social")
            master_data["guidance_received"] = master_data.get("guidance_received", 0) + 1
            master_data["last_interaction"] = time.time()
            disciple.attributes.add("master", master_data, category="social")
            
            # 发布修炼指导事件
            from systems.social_events import publish_mentorship_event, SocialEventType
            publish_mentorship_event(
                master,
                disciple,
                SocialEventType.CULTIVATION_GUIDANCE.value,
                guidance_type=guidance_type,
                content=content,
                guidance_time=time.time(),
                **kwargs
            )
            
            logger.log_info(f"修炼指导: {master.key} -> {disciple.key} ({guidance_type})")
            return True
            
        except Exception as e:
            logger.log_err(f"修炼指导失败: {e}")
            return False
    
    @staticmethod
    def get_all_mentorships() -> List[Dict[str, Any]]:
        """获取所有师徒关系"""
        mentorships = []
        
        try:
            # 查找所有有弟子的角色
            masters = search.search_object_tag("has_disciples", category="social_status")
            
            for master in masters:
                disciples = MentorshipManager.get_disciples(master)
                for disciple in disciples:
                    mentorship_info = MentorshipManager.get_mentorship_info(master, disciple)
                    if mentorship_info:
                        mentorships.append(mentorship_info)
        
        except Exception as e:
            logger.log_err(f"获取师徒关系列表失败: {e}")
        
        return mentorships
    
    @staticmethod
    def find_mentorships_by_sect(sect_name: str) -> List[Dict[str, Any]]:
        """查找指定门派的师徒关系"""
        mentorships = []
        
        try:
            from systems.tag_property_system import TagPropertyQueryManager
            sect_members = TagPropertyQueryManager.find_characters_by_sect(sect_name)
            
            for member in sect_members:
                # 如果是师父，获取其弟子
                disciples = MentorshipManager.get_disciples(member)
                for disciple in disciples:
                    mentorship_info = MentorshipManager.get_mentorship_info(member, disciple)
                    if mentorship_info:
                        mentorships.append(mentorship_info)
        
        except Exception as e:
            logger.log_err(f"查找门派师徒关系失败: {e}")
        
        return mentorships
