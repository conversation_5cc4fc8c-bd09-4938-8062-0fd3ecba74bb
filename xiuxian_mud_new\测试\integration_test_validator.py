"""
集成测试验证脚本

自动化结果验证和报告生成：
- 测试结果自动验证
- 性能基准对比
- 系统健康状态检查
- 详细测试报告生成
- 问题诊断和建议
"""

import os
import json
import time
import unittest
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from evennia.utils.logger import log_info, log_err
from evennia.utils.search import search_script

from .integration_test_framework import XianxiaIntegrationTest
from .integration_test_console import integration_console
from .fault_injection_test import fault_injector, resilience_monitor


@dataclass
class TestValidationResult:
    """测试验证结果"""
    test_name: str
    passed: bool
    score: float
    issues: List[str]
    recommendations: List[str]
    performance_metrics: Dict[str, Any]


@dataclass
class SystemHealthStatus:
    """系统健康状态"""
    overall_health: float
    component_health: Dict[str, float]
    critical_issues: List[str]
    warnings: List[str]
    performance_summary: Dict[str, Any]


class TestResultValidator:
    """测试结果验证器"""
    
    def __init__(self):
        self.validation_rules = {
            "performance_thresholds": {
                "response_time_max": 5000,  # 5秒
                "memory_usage_max": 500 * 1024 * 1024,  # 500MB
                "cpu_usage_max": 80,  # 80%
                "success_rate_min": 90,  # 90%
                "error_rate_max": 5  # 5%
            },
            "integration_requirements": {
                "event_chain_completion_rate": 95,  # 95%
                "cross_system_communication_success": 90,  # 90%
                "data_consistency_score": 95,  # 95%
                "ai_director_coordination_score": 85  # 85%
            },
            "resilience_standards": {
                "fault_recovery_rate": 80,  # 80%
                "system_stability_score": 75,  # 75%
                "graceful_degradation_score": 70  # 70%
            }
        }
    
    def validate_test_results(self, test_results: Dict[str, Any]) -> List[TestValidationResult]:
        """验证测试结果"""
        validation_results = []
        
        for test_suite, results in test_results.items():
            validation_result = self._validate_single_test_suite(test_suite, results)
            validation_results.append(validation_result)
        
        return validation_results
    
    def _validate_single_test_suite(self, test_suite: str, results: Dict[str, Any]) -> TestValidationResult:
        """验证单个测试套件"""
        issues = []
        recommendations = []
        score = 100.0
        
        # 基础成功率验证
        success_rate = results.get("success_rate", 0)
        if success_rate < self.validation_rules["performance_thresholds"]["success_rate_min"]:
            issues.append(f"成功率过低: {success_rate:.1f}% (要求: {self.validation_rules['performance_thresholds']['success_rate_min']}%)")
            score -= (self.validation_rules["performance_thresholds"]["success_rate_min"] - success_rate)
            recommendations.append("检查失败的测试用例，修复相关问题")
        
        # 性能指标验证
        execution_time = results.get("execution_time", 0)
        if execution_time > self.validation_rules["performance_thresholds"]["response_time_max"]:
            issues.append(f"执行时间过长: {execution_time:.2f}ms (要求: <{self.validation_rules['performance_thresholds']['response_time_max']}ms)")
            score -= 10
            recommendations.append("优化测试性能，考虑并行执行或减少测试数据量")
        
        # 错误率验证
        total_tests = results.get("total_tests", 1)
        failures = results.get("failures", 0)
        errors = results.get("errors", 0)
        error_rate = ((failures + errors) / total_tests * 100) if total_tests > 0 else 0
        
        if error_rate > self.validation_rules["performance_thresholds"]["error_rate_max"]:
            issues.append(f"错误率过高: {error_rate:.1f}% (要求: <{self.validation_rules['performance_thresholds']['error_rate_max']}%)")
            score -= error_rate
            recommendations.append("分析错误原因，修复系统缺陷")
        
        # 特定测试套件验证
        if "micro_integration" in test_suite:
            score = self._validate_micro_integration(results, score, issues, recommendations)
        elif "macro_integration" in test_suite:
            score = self._validate_macro_integration(results, score, issues, recommendations)
        elif "end_to_end" in test_suite:
            score = self._validate_end_to_end(results, score, issues, recommendations)
        elif "ai_director" in test_suite:
            score = self._validate_ai_director(results, score, issues, recommendations)
        elif "performance_stress" in test_suite:
            score = self._validate_performance_stress(results, score, issues, recommendations)
        
        # 性能指标提取
        performance_metrics = {
            "success_rate": success_rate,
            "execution_time": execution_time,
            "error_rate": error_rate,
            "total_tests": total_tests
        }
        
        return TestValidationResult(
            test_name=test_suite,
            passed=len(issues) == 0,
            score=max(0, score),
            issues=issues,
            recommendations=recommendations,
            performance_metrics=performance_metrics
        )
    
    def _validate_micro_integration(self, results: Dict, score: float, issues: List, recommendations: List) -> float:
        """验证微集成测试"""
        # 检查系统间通信成功率
        communication_success = results.get("cross_system_communication_success", 0)
        required_success = self.validation_rules["integration_requirements"]["cross_system_communication_success"]
        
        if communication_success < required_success:
            issues.append(f"系统间通信成功率不足: {communication_success:.1f}% (要求: {required_success}%)")
            score -= (required_success - communication_success) * 0.5
            recommendations.append("检查事件总线配置和系统间接口")
        
        return score
    
    def _validate_macro_integration(self, results: Dict, score: float, issues: List, recommendations: List) -> float:
        """验证宏集成测试"""
        # 检查事件链完成率
        event_chain_completion = results.get("event_chain_completion_rate", 0)
        required_completion = self.validation_rules["integration_requirements"]["event_chain_completion_rate"]
        
        if event_chain_completion < required_completion:
            issues.append(f"事件链完成率不足: {event_chain_completion:.1f}% (要求: {required_completion}%)")
            score -= (required_completion - event_chain_completion) * 0.3
            recommendations.append("检查事件链路配置和超时设置")
        
        return score
    
    def _validate_end_to_end(self, results: Dict, score: float, issues: List, recommendations: List) -> float:
        """验证端到端测试"""
        # 检查数据一致性
        data_consistency = results.get("data_consistency_score", 0)
        required_consistency = self.validation_rules["integration_requirements"]["data_consistency_score"]
        
        if data_consistency < required_consistency:
            issues.append(f"数据一致性分数不足: {data_consistency:.1f}% (要求: {required_consistency}%)")
            score -= (required_consistency - data_consistency) * 0.4
            recommendations.append("检查数据同步机制和事务处理")
        
        return score
    
    def _validate_ai_director(self, results: Dict, score: float, issues: List, recommendations: List) -> float:
        """验证AI导演测试"""
        # 检查AI导演协调分数
        coordination_score = results.get("ai_director_coordination_score", 0)
        required_score = self.validation_rules["integration_requirements"]["ai_director_coordination_score"]
        
        if coordination_score < required_score:
            issues.append(f"AI导演协调分数不足: {coordination_score:.1f}% (要求: {required_score}%)")
            score -= (required_score - coordination_score) * 0.6
            recommendations.append("优化AI导演决策算法和协调机制")
        
        return score
    
    def _validate_performance_stress(self, results: Dict, score: float, issues: List, recommendations: List) -> float:
        """验证性能压力测试"""
        # 检查系统稳定性
        stability_score = results.get("system_stability_score", 0)
        required_stability = self.validation_rules["resilience_standards"]["system_stability_score"]
        
        if stability_score < required_stability:
            issues.append(f"系统稳定性分数不足: {stability_score:.1f}% (要求: {required_stability}%)")
            score -= (required_stability - stability_score) * 0.5
            recommendations.append("优化系统资源管理和负载均衡")
        
        return score


class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.health_checkers = {
            "tag_property_system": self._check_tag_property_health,
            "event_system": self._check_event_system_health,
            "ai_directors": self._check_ai_directors_health,
            "world_system": self._check_world_system_health,
            "combat_system": self._check_combat_system_health,
            "social_system": self._check_social_system_health,
            "npc_system": self._check_npc_system_health,
            "novel_generation": self._check_novel_generation_health
        }
    
    def check_system_health(self) -> SystemHealthStatus:
        """检查系统健康状态"""
        log_info("开始系统健康检查")
        
        component_health = {}
        critical_issues = []
        warnings = []
        
        # 检查各个组件
        for component, checker in self.health_checkers.items():
            try:
                health_score, issues, warns = checker()
                component_health[component] = health_score
                
                if health_score < 50:
                    critical_issues.extend([f"{component}: {issue}" for issue in issues])
                elif health_score < 80:
                    warnings.extend([f"{component}: {warn}" for warn in warns])
                    
            except Exception as e:
                log_err(f"健康检查失败 {component}: {e}")
                component_health[component] = 0
                critical_issues.append(f"{component}: 健康检查失败 - {e}")
        
        # 计算总体健康分数
        if component_health:
            overall_health = sum(component_health.values()) / len(component_health)
        else:
            overall_health = 0
        
        # 性能摘要
        performance_summary = self._generate_performance_summary()
        
        return SystemHealthStatus(
            overall_health=overall_health,
            component_health=component_health,
            critical_issues=critical_issues,
            warnings=warnings,
            performance_summary=performance_summary
        )
    
    def _check_tag_property_health(self) -> Tuple[float, List[str], List[str]]:
        """检查TagProperty系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查TagProperty查询性能
            from ..systems.tag_property_system import TagPropertyManager
            
            # 模拟性能测试
            start_time = time.time()
            # 这里应该执行实际的TagProperty查询
            query_time = (time.time() - start_time) * 1000
            
            if query_time > 100:  # 100ms
                warnings.append(f"TagProperty查询较慢: {query_time:.2f}ms")
                health_score -= 10
            
            if query_time > 500:  # 500ms
                issues.append(f"TagProperty查询过慢: {query_time:.2f}ms")
                health_score -= 30
                
        except Exception as e:
            issues.append(f"TagProperty系统异常: {e}")
            health_score -= 50
        
        return health_score, issues, warnings
    
    def _check_event_system_health(self) -> Tuple[float, List[str], List[str]]:
        """检查事件系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查事件总线状态
            from ..systems.event_system import XianxiaEventBus
            
            event_bus = XianxiaEventBus()
            
            # 检查事件队列大小
            queue_size = len(event_bus.event_queue) if hasattr(event_bus, 'event_queue') else 0
            
            if queue_size > 1000:
                warnings.append(f"事件队列较大: {queue_size}")
                health_score -= 10
            
            if queue_size > 5000:
                issues.append(f"事件队列过大: {queue_size}")
                health_score -= 30
                
        except Exception as e:
            issues.append(f"事件系统异常: {e}")
            health_score -= 50
        
        return health_score, issues, warnings
    
    def _check_ai_directors_health(self) -> Tuple[float, List[str], List[str]]:
        """检查AI导演健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查AI导演脚本状态
            ai_directors = [
                "TiandaoDirector",
                "DilingDirector", 
                "QilingDirector"
            ]
            
            active_directors = 0
            for director_name in ai_directors:
                scripts = search_script(director_name)
                if scripts:
                    active_directors += 1
                else:
                    warnings.append(f"AI导演未运行: {director_name}")
                    health_score -= 15
            
            if active_directors == 0:
                issues.append("所有AI导演都未运行")
                health_score -= 60
                
        except Exception as e:
            issues.append(f"AI导演系统异常: {e}")
            health_score -= 50
        
        return health_score, issues, warnings
    
    def _check_world_system_health(self) -> Tuple[float, List[str], List[str]]:
        """检查世界系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查世界系统组件
            from ..systems.world_location_manager import WorldLocationManager
            
            # 检查位置管理器状态
            # 这里应该检查实际的世界系统状态
            
        except Exception as e:
            issues.append(f"世界系统异常: {e}")
            health_score -= 30
        
        return health_score, issues, warnings
    
    def _check_combat_system_health(self) -> Tuple[float, List[str], List[str]]:
        """检查战斗系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查战斗系统状态
            # 这里应该检查战斗系统的实际状态
            pass
            
        except Exception as e:
            issues.append(f"战斗系统异常: {e}")
            health_score -= 30
        
        return health_score, issues, warnings
    
    def _check_social_system_health(self) -> Tuple[float, List[str], List[str]]:
        """检查社交系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查社交系统状态
            # 这里应该检查社交系统的实际状态
            pass
            
        except Exception as e:
            issues.append(f"社交系统异常: {e}")
            health_score -= 30
        
        return health_score, issues, warnings
    
    def _check_npc_system_health(self) -> Tuple[float, List[str], List[str]]:
        """检查NPC系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查NPC系统状态
            # 这里应该检查NPC系统的实际状态
            pass
            
        except Exception as e:
            issues.append(f"NPC系统异常: {e}")
            health_score -= 30
        
        return health_score, issues, warnings
    
    def _check_novel_generation_health(self) -> Tuple[float, List[str], List[str]]:
        """检查小说生成系统健康"""
        issues = []
        warnings = []
        health_score = 100
        
        try:
            # 检查小说生成系统状态
            # 这里应该检查小说生成系统的实际状态
            pass
            
        except Exception as e:
            issues.append(f"小说生成系统异常: {e}")
            health_score -= 30
        
        return health_score, issues, warnings
    
    def _generate_performance_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu_usage": "N/A",  # 这里应该获取实际的CPU使用率
            "memory_usage": "N/A",  # 这里应该获取实际的内存使用率
            "disk_usage": "N/A",  # 这里应该获取实际的磁盘使用率
            "network_status": "N/A"  # 这里应该获取实际的网络状态
        }


class IntegrationTestReportGenerator:
    """集成测试报告生成器"""
    
    def __init__(self):
        self.validator = TestResultValidator()
        self.health_checker = SystemHealthChecker()
    
    def generate_comprehensive_report(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合测试报告"""
        log_info("生成综合集成测试报告")
        
        # 验证测试结果
        validation_results = self.validator.validate_test_results(test_results)
        
        # 检查系统健康
        health_status = self.health_checker.check_system_health()
        
        # 生成报告
        report = {
            "report_metadata": {
                "generation_time": datetime.now().isoformat(),
                "report_type": "comprehensive_integration_test",
                "version": "1.0"
            },
            "executive_summary": self._generate_executive_summary(validation_results, health_status),
            "test_validation_results": [
                {
                    "test_name": result.test_name,
                    "passed": result.passed,
                    "score": result.score,
                    "issues": result.issues,
                    "recommendations": result.recommendations,
                    "performance_metrics": result.performance_metrics
                }
                for result in validation_results
            ],
            "system_health_status": {
                "overall_health": health_status.overall_health,
                "component_health": health_status.component_health,
                "critical_issues": health_status.critical_issues,
                "warnings": health_status.warnings,
                "performance_summary": health_status.performance_summary
            },
            "recommendations": self._generate_overall_recommendations(validation_results, health_status),
            "next_steps": self._generate_next_steps(validation_results, health_status)
        }
        
        return report
    
    def _generate_executive_summary(self, validation_results: List[TestValidationResult], 
                                   health_status: SystemHealthStatus) -> Dict[str, Any]:
        """生成执行摘要"""
        total_tests = len(validation_results)
        passed_tests = sum(1 for result in validation_results if result.passed)
        average_score = sum(result.score for result in validation_results) / total_tests if total_tests > 0 else 0
        
        return {
            "overall_status": "PASSED" if passed_tests == total_tests and health_status.overall_health > 80 else "FAILED",
            "test_pass_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "average_test_score": average_score,
            "system_health_score": health_status.overall_health,
            "critical_issues_count": len(health_status.critical_issues),
            "warnings_count": len(health_status.warnings),
            "readiness_assessment": self._assess_system_readiness(validation_results, health_status)
        }
    
    def _assess_system_readiness(self, validation_results: List[TestValidationResult], 
                                health_status: SystemHealthStatus) -> str:
        """评估系统就绪状态"""
        if health_status.overall_health > 90 and all(result.passed for result in validation_results):
            return "PRODUCTION_READY"
        elif health_status.overall_health > 80 and sum(1 for result in validation_results if result.passed) / len(validation_results) > 0.9:
            return "STAGING_READY"
        elif health_status.overall_health > 70:
            return "DEVELOPMENT_READY"
        else:
            return "NOT_READY"
    
    def _generate_overall_recommendations(self, validation_results: List[TestValidationResult], 
                                        health_status: SystemHealthStatus) -> List[str]:
        """生成总体建议"""
        recommendations = []
        
        # 基于测试结果的建议
        failed_tests = [result for result in validation_results if not result.passed]
        if failed_tests:
            recommendations.append(f"优先修复 {len(failed_tests)} 个失败的测试套件")
        
        # 基于健康状态的建议
        if health_status.critical_issues:
            recommendations.append("立即处理系统关键问题")
        
        if health_status.overall_health < 80:
            recommendations.append("提升系统整体健康分数至80分以上")
        
        # 性能优化建议
        low_score_tests = [result for result in validation_results if result.score < 80]
        if low_score_tests:
            recommendations.append("优化低分测试套件的性能表现")
        
        return recommendations
    
    def _generate_next_steps(self, validation_results: List[TestValidationResult], 
                           health_status: SystemHealthStatus) -> List[str]:
        """生成下一步行动计划"""
        next_steps = []
        
        # 基于系统就绪状态的下一步
        readiness = self._assess_system_readiness(validation_results, health_status)
        
        if readiness == "PRODUCTION_READY":
            next_steps.append("系统已准备好进入生产环境")
            next_steps.append("执行最终的生产环境部署检查")
        elif readiness == "STAGING_READY":
            next_steps.append("部署到预生产环境进行最终测试")
            next_steps.append("修复剩余的非关键问题")
        elif readiness == "DEVELOPMENT_READY":
            next_steps.append("继续开发和测试周期")
            next_steps.append("重点关注系统稳定性改进")
        else:
            next_steps.append("暂停部署，专注于修复关键问题")
            next_steps.append("重新运行集成测试验证修复效果")
        
        return next_steps
    
    def export_report(self, report: Dict[str, Any], format: str = "json", filename: str = None) -> str:
        """导出报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"integration_test_validation_report_{timestamp}"
        
        if format == "json":
            filepath = f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的报告格式: {format}")
        
        log_info(f"集成测试验证报告已导出: {filepath}")
        return filepath


# 全局验证器实例
test_validator = TestResultValidator()
health_checker = SystemHealthChecker()
report_generator = IntegrationTestReportGenerator()


def validate_integration_tests():
    """验证集成测试结果"""
    log_info("开始验证集成测试结果")
    
    # 获取测试结果
    test_results = integration_console.suite_manager.test_results
    
    if not test_results:
        log_err("没有找到测试结果，请先运行集成测试")
        return None
    
    # 生成综合报告
    comprehensive_report = report_generator.generate_comprehensive_report(test_results)
    
    # 导出报告
    report_file = report_generator.export_report(comprehensive_report)
    
    log_info(f"集成测试验证完成，报告已保存到: {report_file}")
    
    return comprehensive_report


if __name__ == '__main__':
    # 运行集成测试验证
    validate_integration_tests()
