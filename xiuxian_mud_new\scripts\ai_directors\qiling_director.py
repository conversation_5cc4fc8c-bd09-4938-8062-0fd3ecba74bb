"""
器灵导演 - 个体交互和体验优化

负责修仙界的微观层面管理：
- 个性化AI交互（NPC对话、任务推荐、智能引导）
- 实时游戏体验优化（难度调节、奖励平衡、进度跟踪）
- 社交系统管理（师徒关系、道侣系统、好友互动）
- 个人成长引导（修炼建议、突破指导、技能推荐）
"""

import time
import random
from typing import Dict, Any, List, Optional

from evennia.utils import logger
from evennia.utils.search import search_object

from .base_director import BaseDirector, DirectorEventTypes
from systems.ai_decision_engine import AIDecisionEngine, DecisionContext, DirectorType
from systems.query_interfaces import AIDirectorQueryInterface
from systems.event_system import EventPriority


class QilingDirector(BaseDirector):
    """
    器灵导演 - 负责个体交互和体验优化
    
    决策周期：10秒
    职责范围：个性化交互、体验优化、社交管理、成长引导
    """
    
    def at_script_creation(self):
        """器灵导演初始化"""
        self.director_type = "qiling"
        self.decision_interval = 10  # 10秒
        
        # 初始化个体交互状态
        self.db.interaction_state = {
            "active_conversations": {},
            "pending_recommendations": {},
            "player_preferences": {},
            "interaction_history": [],
            "ai_npc_status": {}
        }
        
        # 初始化体验优化状态
        self.db.experience_optimization = {
            "difficulty_adjustments": {},
            "reward_multipliers": {},
            "progress_tracking": {},
            "engagement_metrics": {},
            "optimization_history": []
        }
        
        # 初始化社交系统状态
        self.db.social_system = {
            "mentor_relationships": {},
            "companion_relationships": {},
            "friend_networks": {},
            "social_events": [],
            "relationship_recommendations": {}
        }
        
        # 初始化成长引导状态
        self.db.growth_guidance = {
            "cultivation_advice": {},
            "breakthrough_guidance": {},
            "skill_recommendations": {},
            "personalized_paths": {},
            "guidance_history": []
        }
        
        super().at_script_creation()
        logger.log_info("器灵导演已创建 - 专注个体体验与成长引导")
    
    def collect_context_data(self) -> Dict[str, Any]:
        """收集个体级决策所需的上下文数据"""
        try:
            # 获取实时事件
            realtime_events = AIDirectorQueryInterface.get_realtime_events()
            
            # 获取在线玩家详细信息
            online_players = self._get_detailed_player_info()
            
            # 分析玩家行为模式
            behavior_analysis = self._analyze_player_behavior()
            
            # 获取社交网络状态
            social_analysis = self._analyze_social_networks()
            
            # 分析个体需求
            individual_needs = self._analyze_individual_needs()
            
            # 获取AI NPC状态
            npc_status = self._get_ai_npc_status()
            
            context_data = {
                "realtime_events": realtime_events,
                "online_players": online_players,
                "behavior_analysis": behavior_analysis,
                "social_analysis": social_analysis,
                "individual_needs": individual_needs,
                "npc_status": npc_status,
                "interaction_state": self.db.interaction_state,
                "experience_optimization": self.db.experience_optimization,
                "social_system": self.db.social_system,
                "growth_guidance": self.db.growth_guidance,
                "current_time": time.time()
            }
            
            return context_data
            
        except Exception as e:
            logger.log_err(f"器灵导演收集上下文数据失败: {e}")
            return {}
    
    def make_ai_decision(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基于个体状态进行器灵级AI决策"""
        try:
            # 创建决策上下文
            decision_context = DecisionContext(
                director_type=DirectorType.QILING,
                timestamp=time.time(),
                world_state={
                    "online_players": context.get("online_players", []),
                    "interaction_state": context.get("interaction_state", {}),
                    "social_system": context.get("social_system", {})
                },
                recent_events=context.get("realtime_events", []),
                performance_metrics=self.get_performance_stats(),
                custom_data={
                    "behavior_analysis": context.get("behavior_analysis", {}),
                    "social_analysis": context.get("social_analysis", {}),
                    "individual_needs": context.get("individual_needs", {}),
                    "npc_status": context.get("npc_status", {})
                }
            )
            
            # 获取AI决策引擎
            ai_engine = self.get_ai_decision_engine()
            if not ai_engine:
                return self._fallback_decision(context)
            
            # 进行AI决策
            decision_result = ai_engine.make_decision(decision_context)
            
            if decision_result:
                return {
                    "decision_type": decision_result.decision_type,
                    "priority": decision_result.priority,
                    "actions": decision_result.actions,
                    "effects": decision_result.effects,
                    "duration": decision_result.duration,
                    "conditions": decision_result.conditions,
                    "confidence": decision_result.confidence,
                    "reasoning": decision_result.reasoning
                }
            
            return None
            
        except Exception as e:
            logger.log_err(f"器灵导演AI决策失败: {e}")
            return self._fallback_decision(context)
    
    def execute_decision(self, decision: Dict[str, Any]):
        """执行器灵级决策"""
        try:
            decision_type = decision.get("decision_type", "unknown")
            actions = decision.get("actions", [])
            effects = decision.get("effects", {})
            
            logger.log_info(f"器灵导演执行决策: {decision_type}")
            logger.log_info(f"决策理由: {decision.get('reasoning', '未知')}")
            
            # 执行具体行动
            for action in actions:
                self._execute_action(action)
            
            # 应用个体效果
            self._apply_individual_effects(effects)
            
            # 记录交互事件
            self._record_interaction_event(decision)
            
            # 发布器灵事件到事件总线
            event_type = self._determine_event_type(decision_type)
            self.publish_director_event(
                event_type,
                {
                    "decision": decision,
                    "timestamp": time.time(),
                    "affected_players": self._get_affected_players(decision)
                },
                EventPriority.LOW
            )
            
        except Exception as e:
            logger.log_err(f"器灵导演执行决策失败: {e}")
    
    def _execute_action(self, action: Dict[str, Any]):
        """执行具体的器灵行动"""
        action_type = action.get("type", "unknown")
        details = action.get("details", {})
        
        if action_type == "provide_cultivation_advice":
            self._provide_cultivation_advice(details)
        elif action_type == "adjust_difficulty":
            self._adjust_difficulty(details)
        elif action_type == "recommend_social_interaction":
            self._recommend_social_interaction(details)
        elif action_type == "trigger_npc_interaction":
            self._trigger_npc_interaction(details)
        elif action_type == "optimize_reward":
            self._optimize_reward(details)
        elif action_type == "guide_breakthrough":
            self._guide_breakthrough(details)
        elif action_type == "suggest_skill_development":
            self._suggest_skill_development(details)
        elif action_type == "facilitate_social_event":
            self._facilitate_social_event(details)
        else:
            logger.log_warn(f"未知的器灵行动类型: {action_type}")
    
    def _provide_cultivation_advice(self, details: Dict[str, Any]):
        """提供修炼建议"""
        target = details.get("target", "active_players")
        advice_type = details.get("advice_type", "general_guidance")
        
        logger.log_info(f"器灵提供修炼建议: {advice_type} 给 {target}")
        
        if target == "active_players":
            # 为所有活跃玩家提供建议
            for player_name in self._get_active_player_names():
                self._add_cultivation_advice(player_name, advice_type)
        else:
            # 为特定玩家提供建议
            self._add_cultivation_advice(target, advice_type)
    
    def _adjust_difficulty(self, details: Dict[str, Any]):
        """调整游戏难度"""
        player_name = details.get("player_name", "")
        adjustment_type = details.get("adjustment_type", "moderate")
        factor = details.get("factor", 1.0)
        
        if player_name:
            logger.log_info(f"器灵为{player_name}调整难度: {adjustment_type} (倍率: {factor})")
            
            self.db.experience_optimization["difficulty_adjustments"][player_name] = {
                "type": adjustment_type,
                "factor": factor,
                "timestamp": time.time()
            }
    
    def _recommend_social_interaction(self, details: Dict[str, Any]):
        """推荐社交互动"""
        player_name = details.get("player_name", "")
        interaction_type = details.get("interaction_type", "friendship")
        target_player = details.get("target_player", "")
        
        logger.log_info(f"器灵推荐社交互动: {player_name} -> {target_player} ({interaction_type})")
        
        if player_name and target_player:
            recommendation = {
                "type": interaction_type,
                "target": target_player,
                "timestamp": time.time(),
                "status": "pending"
            }
            
            if player_name not in self.db.social_system["relationship_recommendations"]:
                self.db.social_system["relationship_recommendations"][player_name] = []
            
            self.db.social_system["relationship_recommendations"][player_name].append(recommendation)
    
    def _trigger_npc_interaction(self, details: Dict[str, Any]):
        """触发NPC交互"""
        npc_name = details.get("npc_name", "智能NPC")
        player_name = details.get("player_name", "")
        interaction_type = details.get("interaction_type", "dialogue")
        
        logger.log_info(f"器灵触发NPC交互: {npc_name} 与 {player_name} ({interaction_type})")
        
        interaction_record = {
            "npc_name": npc_name,
            "player_name": player_name,
            "type": interaction_type,
            "timestamp": time.time(),
            "status": "initiated"
        }
        
        self.db.interaction_state["interaction_history"].append(interaction_record)
    
    def _optimize_reward(self, details: Dict[str, Any]):
        """优化奖励"""
        player_name = details.get("player_name", "")
        reward_type = details.get("reward_type", "experience")
        multiplier = details.get("multiplier", 1.0)
        
        logger.log_info(f"器灵优化奖励: {player_name} {reward_type} 倍率{multiplier}")
        
        if player_name:
            if player_name not in self.db.experience_optimization["reward_multipliers"]:
                self.db.experience_optimization["reward_multipliers"][player_name] = {}
            
            self.db.experience_optimization["reward_multipliers"][player_name][reward_type] = {
                "multiplier": multiplier,
                "timestamp": time.time()
            }
    
    def _guide_breakthrough(self, details: Dict[str, Any]):
        """指导突破"""
        player_name = details.get("player_name", "")
        breakthrough_type = details.get("breakthrough_type", "realm_advancement")
        guidance_level = details.get("guidance_level", "detailed")
        
        logger.log_info(f"器灵指导突破: {player_name} {breakthrough_type} ({guidance_level})")
        
        if player_name:
            guidance = {
                "type": breakthrough_type,
                "level": guidance_level,
                "timestamp": time.time(),
                "status": "active"
            }
            
            self.db.growth_guidance["breakthrough_guidance"][player_name] = guidance
    
    def _suggest_skill_development(self, details: Dict[str, Any]):
        """建议技能发展"""
        player_name = details.get("player_name", "")
        skill_category = details.get("skill_category", "combat")
        priority = details.get("priority", "normal")
        
        logger.log_info(f"器灵建议技能发展: {player_name} {skill_category} (优先级: {priority})")
        
        if player_name:
            suggestion = {
                "category": skill_category,
                "priority": priority,
                "timestamp": time.time()
            }
            
            if player_name not in self.db.growth_guidance["skill_recommendations"]:
                self.db.growth_guidance["skill_recommendations"][player_name] = []
            
            self.db.growth_guidance["skill_recommendations"][player_name].append(suggestion)
    
    def _facilitate_social_event(self, details: Dict[str, Any]):
        """促进社交事件"""
        event_type = details.get("event_type", "gathering")
        participants = details.get("participants", [])
        location = details.get("location", "聚会场所")
        
        logger.log_info(f"器灵促进社交事件: {event_type} 于 {location}，参与者: {len(participants)}人")
        
        social_event = {
            "type": event_type,
            "participants": participants,
            "location": location,
            "timestamp": time.time(),
            "status": "planned"
        }
        
        self.db.social_system["social_events"].append(social_event)
    
    def _apply_individual_effects(self, effects: Dict[str, Any]):
        """应用个体效果"""
        for effect_type, value in effects.items():
            if effect_type == "player_engagement":
                # 提升玩家参与度
                for player_name in self._get_active_player_names():
                    if player_name not in self.db.experience_optimization["engagement_metrics"]:
                        self.db.experience_optimization["engagement_metrics"][player_name] = 1.0
                    self.db.experience_optimization["engagement_metrics"][player_name] *= value
            elif effect_type == "social_activity":
                # 提升社交活跃度
                for event in self.db.social_system["social_events"]:
                    if event["status"] == "planned":
                        event["status"] = "active"
    
    def _record_interaction_event(self, decision: Dict[str, Any]):
        """记录交互事件"""
        event_record = {
            "timestamp": time.time(),
            "decision_type": decision.get("decision_type"),
            "reasoning": decision.get("reasoning"),
            "effects": decision.get("effects"),
            "confidence": decision.get("confidence"),
            "affected_players": self._get_affected_players(decision)
        }
        
        self.db.interaction_state["interaction_history"].append(event_record)
        
        # 保留最近1000个交互事件
        if len(self.db.interaction_state["interaction_history"]) > 1000:
            self.db.interaction_state["interaction_history"] = self.db.interaction_state["interaction_history"][-1000:]
    
    def _get_detailed_player_info(self) -> List[Dict[str, Any]]:
        """获取在线玩家详细信息"""
        try:
            players = search_object(typeclass="typeclasses.characters.Character")
            detailed_info = []
            
            for player in players:
                if player.has_account and player.sessions.all():
                    player_info = {
                        "name": player.key,
                        "location": player.location.key if player.location else "未知",
                        "realm": getattr(player, "修为境界", "练气"),
                        "sect": getattr(player, "门派归属", "无门派"),
                        "online_time": self._calculate_online_time(player),
                        "last_activity": self._get_last_activity(player),
                        "engagement_level": self._calculate_engagement_level(player),
                        "social_connections": self._get_social_connections(player),
                        "current_needs": self._assess_current_needs(player)
                    }
                    detailed_info.append(player_info)
            
            return detailed_info
            
        except Exception as e:
            logger.log_err(f"获取玩家详细信息失败: {e}")
            return []
    
    def _analyze_player_behavior(self) -> Dict[str, Any]:
        """分析玩家行为模式"""
        behavior_patterns = {}
        
        for player_name in self._get_active_player_names():
            # 分析玩家的行为模式
            patterns = {
                "activity_frequency": self._calculate_activity_frequency(player_name),
                "preferred_activities": self._get_preferred_activities(player_name),
                "social_tendency": self._calculate_social_tendency(player_name),
                "learning_pace": self._calculate_learning_pace(player_name),
                "challenge_preference": self._assess_challenge_preference(player_name)
            }
            behavior_patterns[player_name] = patterns
        
        return behavior_patterns
    
    def _analyze_social_networks(self) -> Dict[str, Any]:
        """分析社交网络状态"""
        social_analysis = {
            "active_relationships": len(self.db.social_system["mentor_relationships"]) + 
                                  len(self.db.social_system["companion_relationships"]),
            "network_density": self._calculate_network_density(),
            "social_events_count": len([e for e in self.db.social_system["social_events"] if e["status"] == "active"]),
            "relationship_satisfaction": self._calculate_relationship_satisfaction(),
            "social_opportunities": self._identify_social_opportunities()
        }
        
        return social_analysis
    
    def _analyze_individual_needs(self) -> Dict[str, Any]:
        """分析个体需求"""
        individual_needs = {}
        
        for player_name in self._get_active_player_names():
            needs = {
                "guidance_need": self._assess_guidance_need(player_name),
                "social_need": self._assess_social_need(player_name),
                "challenge_need": self._assess_challenge_need(player_name),
                "reward_need": self._assess_reward_need(player_name),
                "progression_need": self._assess_progression_need(player_name)
            }
            individual_needs[player_name] = needs
        
        return individual_needs
    
    def _get_ai_npc_status(self) -> Dict[str, Any]:
        """获取AI NPC状态"""
        # 简化的NPC状态
        return {
            "active_npcs": ["智能师父", "修炼导师", "社交助手"],
            "interaction_capacity": 0.8,
            "response_quality": 0.9,
            "availability": True
        }
    
    def _get_active_player_names(self) -> List[str]:
        """获取活跃玩家名称列表"""
        try:
            players = search_object(typeclass="typeclasses.characters.Character")
            active_names = []
            
            for player in players:
                if player.has_account and player.sessions.all():
                    active_names.append(player.key)
            
            return active_names
            
        except Exception as e:
            logger.log_err(f"获取活跃玩家名称失败: {e}")
            return []
    
    def _add_cultivation_advice(self, player_name: str, advice_type: str):
        """为玩家添加修炼建议"""
        advice = {
            "type": advice_type,
            "timestamp": time.time(),
            "status": "pending"
        }
        
        if player_name not in self.db.growth_guidance["cultivation_advice"]:
            self.db.growth_guidance["cultivation_advice"][player_name] = []
        
        self.db.growth_guidance["cultivation_advice"][player_name].append(advice)
    
    def _determine_event_type(self, decision_type: str) -> str:
        """根据决策类型确定事件类型"""
        if "interaction" in decision_type or "npc" in decision_type:
            return DirectorEventTypes.INDIVIDUAL_INTERACTION
        elif "optimization" in decision_type or "experience" in decision_type:
            return DirectorEventTypes.EXPERIENCE_OPTIMIZATION
        elif "social" in decision_type or "relationship" in decision_type:
            return DirectorEventTypes.SOCIAL_RELATIONSHIP_UPDATE
        else:
            return DirectorEventTypes.GROWTH_GUIDANCE
    
    def _get_affected_players(self, decision: Dict[str, Any]) -> List[str]:
        """获取决策影响的玩家"""
        # 简化实现，返回所有在线玩家
        return self._get_active_player_names()
    
    # 简化的辅助方法实现
    def _calculate_online_time(self, player) -> float:
        return random.uniform(0.5, 3.0)  # 模拟在线时间
    
    def _get_last_activity(self, player) -> str:
        activities = ["修炼", "战斗", "社交", "探索", "任务"]
        return random.choice(activities)
    
    def _calculate_engagement_level(self, player) -> float:
        return random.uniform(0.3, 1.0)
    
    def _get_social_connections(self, player) -> int:
        return random.randint(0, 5)
    
    def _assess_current_needs(self, player) -> List[str]:
        needs = ["修炼指导", "社交互动", "挑战任务", "技能提升"]
        return random.sample(needs, random.randint(1, 3))
    
    def _calculate_activity_frequency(self, player_name: str) -> float:
        return random.uniform(0.5, 1.0)
    
    def _get_preferred_activities(self, player_name: str) -> List[str]:
        activities = ["修炼", "战斗", "社交", "探索"]
        return random.sample(activities, random.randint(1, 3))
    
    def _calculate_social_tendency(self, player_name: str) -> float:
        return random.uniform(0.2, 0.9)
    
    def _calculate_learning_pace(self, player_name: str) -> str:
        paces = ["缓慢", "正常", "快速"]
        return random.choice(paces)
    
    def _assess_challenge_preference(self, player_name: str) -> str:
        preferences = ["简单", "适中", "困难"]
        return random.choice(preferences)
    
    def _calculate_network_density(self) -> float:
        return random.uniform(0.3, 0.8)
    
    def _calculate_relationship_satisfaction(self) -> float:
        return random.uniform(0.6, 0.9)
    
    def _identify_social_opportunities(self) -> List[str]:
        opportunities = ["师徒配对", "道侣推荐", "团队组建", "聚会活动"]
        return random.sample(opportunities, random.randint(1, 3))
    
    def _assess_guidance_need(self, player_name: str) -> float:
        return random.uniform(0.2, 0.8)
    
    def _assess_social_need(self, player_name: str) -> float:
        return random.uniform(0.1, 0.7)
    
    def _assess_challenge_need(self, player_name: str) -> float:
        return random.uniform(0.3, 0.9)
    
    def _assess_reward_need(self, player_name: str) -> float:
        return random.uniform(0.4, 0.8)
    
    def _assess_progression_need(self, player_name: str) -> float:
        return random.uniform(0.5, 1.0)
    
    def _fallback_decision(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """AI不可用时的备用决策"""
        online_players = context.get("online_players", [])
        
        if len(online_players) > 0:
            # 有玩家在线，提供基础服务
            return {
                "decision_type": "basic_service",
                "priority": "low",
                "actions": [
                    {
                        "type": "provide_cultivation_advice",
                        "details": {
                            "target": "active_players",
                            "advice_type": "general_guidance"
                        }
                    }
                ],
                "effects": {"player_engagement": 1.02},
                "duration": 300,
                "conditions": ["有玩家在线"],
                "confidence": 0.6,
                "reasoning": "为在线玩家提供基础的修炼指导服务"
            }
        
        # 无玩家在线，维持待机状态
        return {
            "decision_type": "standby",
            "priority": "low",
            "actions": [],
            "effects": {},
            "duration": 10,
            "conditions": [],
            "confidence": 0.5,
            "reasoning": "无玩家在线，保持待机状态"
        }
