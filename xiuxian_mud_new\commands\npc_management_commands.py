"""
NPC管理命令系统

提供创建、配置和管理智能NPC的命令：
- 创建不同类型的NPC
- 配置NPC属性和个性
- 查看NPC状态和对话历史
- 监控NPC性能
- 调试NPC系统
"""

import time
from evennia.commands.command import Command
from evennia.commands.cmdset import CmdSet
from evennia.utils import logger, search, evtable
from evennia.utils.utils import class_from_module

try:
    from typeclasses.npcs import NPCFactory, IntelligentNPC
    from systems.npc_personality_engine import NPCPersonalityEngine
    from systems.npc_context_manager import NPCContextManager
    NPC_SYSTEM_AVAILABLE = True
except ImportError as e:
    logger.log_warn(f"NPC系统导入失败: {e}")
    NPC_SYSTEM_AVAILABLE = False


class CmdCreateNPC(Command):
    """
    创建智能NPC
    
    用法:
        create_npc <类型> <名称> [属性=值 ...]
        
    类型:
        elder - 长老NPC
        senior_brother - 师兄NPC
        senior_sister - 师姐NPC
        junior_brother - 师弟NPC
        junior_sister - 师妹NPC
        disciple - 普通弟子NPC
        
    属性:
        门派归属=<门派名> - 设置NPC所属门派
        修为境界=<境界> - 设置NPC修为境界
        五行属性=<属性> - 设置NPC五行属性
        
    示例:
        create_npc elder 青云长老 门派归属=青云门 修为境界=元婴
        create_npc senior_brother 李师兄 门派归属=青云门
    """
    
    key = "create_npc"
    aliases = ["创建npc", "npc_create"]
    locks = "cmd:perm(Builder)"
    help_category = "NPC管理"
    
    def func(self):
        """执行命令"""
        if not NPC_SYSTEM_AVAILABLE:
            self.caller.msg("NPC系统不可用")
            return
        
        if not self.args:
            self.caller.msg("用法: create_npc <类型> <名称> [属性=值 ...]")
            self.caller.msg(f"可用类型: {', '.join(NPCFactory.get_available_types())}")
            return
        
        args = self.args.split()
        if len(args) < 2:
            self.caller.msg("请指定NPC类型和名称")
            return
        
        npc_type = args[0]
        npc_name = args[1]
        
        # 解析属性
        attributes = {}
        for arg in args[2:]:
            if "=" in arg:
                key, value = arg.split("=", 1)
                attributes[key] = value
        
        try:
            # 创建NPC
            npc = NPCFactory.create_npc(
                npc_type=npc_type,
                name=npc_name,
                location=self.caller.location,
                **attributes
            )
            
            self.caller.msg(f"成功创建{npc_type}类型的NPC: {npc_name}")
            self.caller.msg(f"NPC已放置在: {self.caller.location.key}")
            
            # 显示NPC信息
            self._show_npc_info(npc)
            
        except ValueError as e:
            self.caller.msg(f"创建失败: {e}")
        except Exception as e:
            self.caller.msg(f"创建NPC时发生错误: {e}")
            logger.log_err(f"创建NPC失败: {e}")
    
    def _show_npc_info(self, npc):
        """显示NPC信息"""
        info = f"""
NPC信息:
- 名称: {npc.key}
- 类型: {getattr(npc, '角色类型', '未知')}
- 修为: {getattr(npc, '修为境界', '未知')}
- 门派: {getattr(npc, '门派归属', '未知')}
- 五行: {getattr(npc, '五行属性', '未知')}
- 位置: {npc.location.key if npc.location else '未知'}
"""
        self.caller.msg(info)


class CmdNPCStatus(Command):
    """
    查看NPC状态
    
    用法:
        npc_status [NPC名称]
        
    如果不指定NPC名称，显示当前位置所有NPC的状态
    
    示例:
        npc_status
        npc_status 青云长老
    """
    
    key = "npc_status"
    aliases = ["npc状态", "npc_info"]
    locks = "cmd:perm(Builder)"
    help_category = "NPC管理"
    
    def func(self):
        """执行命令"""
        if not NPC_SYSTEM_AVAILABLE:
            self.caller.msg("NPC系统不可用")
            return
        
        if self.args:
            # 查看指定NPC
            npc = search.search_object(self.args.strip(), candidates=self.caller.location.contents)
            if not npc:
                self.caller.msg(f"未找到NPC: {self.args}")
                return
            
            npc = npc[0]
            if not isinstance(npc, IntelligentNPC):
                self.caller.msg(f"{npc.key} 不是智能NPC")
                return
            
            self._show_detailed_npc_status(npc)
        else:
            # 显示当前位置所有NPC
            npcs = [obj for obj in self.caller.location.contents 
                   if isinstance(obj, IntelligentNPC)]
            
            if not npcs:
                self.caller.msg("当前位置没有智能NPC")
                return
            
            self._show_npcs_overview(npcs)
    
    def _show_detailed_npc_status(self, npc):
        """显示详细NPC状态"""
        npc_state = npc.db.npc_state or {}
        
        # 基本信息
        table = evtable.EvTable("属性", "值", border="cells")
        table.add_row("名称", npc.key)
        table.add_row("类型", getattr(npc, "角色类型", "未知"))
        table.add_row("修为", getattr(npc, "修为境界", "未知"))
        table.add_row("门派", getattr(npc, "门派归属", "未知"))
        table.add_row("五行", getattr(npc, "五行属性", "未知"))
        table.add_row("位置", npc.location.key if npc.location else "未知")
        
        self.caller.msg("=== NPC基本信息 ===")
        self.caller.msg(table)
        
        # 状态信息
        creation_time = npc_state.get("creation_time", 0)
        total_conversations = npc_state.get("total_conversations", 0)
        last_interaction = npc_state.get("last_interaction_time", 0)
        
        status_table = evtable.EvTable("状态", "值", border="cells")
        status_table.add_row("创建时间", time.ctime(creation_time) if creation_time else "未知")
        status_table.add_row("对话总数", str(total_conversations))
        status_table.add_row("最后交互", time.ctime(last_interaction) if last_interaction else "从未")
        
        self.caller.msg("\n=== NPC状态信息 ===")
        self.caller.msg(status_table)
        
        # 个性特征
        traits = npc_state.get("personality_traits", [])
        if traits:
            self.caller.msg(f"\n=== 个性特征 ===")
            self.caller.msg(f"特征: {', '.join(traits[:10])}")
        
        # 对话历史统计
        conversation_history = npc_state.get("conversation_history", {})
        if conversation_history:
            self.caller.msg(f"\n=== 对话历史统计 ===")
            for player_name, history in conversation_history.items():
                self.caller.msg(f"{player_name}: {len(history)}次对话")
    
    def _show_npcs_overview(self, npcs):
        """显示NPC概览"""
        table = evtable.EvTable("名称", "类型", "修为", "门派", "对话数", border="cells")
        
        for npc in npcs:
            npc_state = npc.db.npc_state or {}
            total_conversations = npc_state.get("total_conversations", 0)
            
            table.add_row(
                npc.key,
                getattr(npc, "角色类型", "未知"),
                getattr(npc, "修为境界", "未知"),
                getattr(npc, "门派归属", "未知"),
                str(total_conversations)
            )
        
        self.caller.msg("=== 当前位置NPC概览 ===")
        self.caller.msg(table)


class CmdNPCConfig(Command):
    """
    配置NPC属性
    
    用法:
        npc_config <NPC名称> <属性> <值>
        
    可配置属性:
        修为境界 - 修为境界
        门派归属 - 所属门派
        五行属性 - 五行属性
        角色类型 - 角色类型
        
    示例:
        npc_config 青云长老 修为境界 化神
        npc_config 李师兄 门派归属 青云门
    """
    
    key = "npc_config"
    aliases = ["npc配置", "config_npc"]
    locks = "cmd:perm(Builder)"
    help_category = "NPC管理"
    
    def func(self):
        """执行命令"""
        if not NPC_SYSTEM_AVAILABLE:
            self.caller.msg("NPC系统不可用")
            return
        
        if not self.args:
            self.caller.msg("用法: npc_config <NPC名称> <属性> <值>")
            return
        
        args = self.args.split()
        if len(args) < 3:
            self.caller.msg("请指定NPC名称、属性和值")
            return
        
        npc_name = args[0]
        attribute = args[1]
        value = " ".join(args[2:])
        
        # 查找NPC
        npc = search.search_object(npc_name, candidates=self.caller.location.contents)
        if not npc:
            self.caller.msg(f"未找到NPC: {npc_name}")
            return
        
        npc = npc[0]
        if not isinstance(npc, IntelligentNPC):
            self.caller.msg(f"{npc.key} 不是智能NPC")
            return
        
        # 配置属性
        try:
            if hasattr(npc, attribute):
                setattr(npc, attribute, value)
                self.caller.msg(f"成功设置 {npc.key} 的 {attribute} 为 {value}")
                
                # 重新生成个性特征
                npc._generate_personality_traits()
                self.caller.msg("已重新生成个性特征")
            else:
                self.caller.msg(f"NPC没有属性: {attribute}")
        except Exception as e:
            self.caller.msg(f"配置失败: {e}")
            logger.log_err(f"NPC配置失败: {e}")


class CmdNPCHistory(Command):
    """
    查看NPC对话历史
    
    用法:
        npc_history <NPC名称> [玩家名称] [数量]
        
    参数:
        NPC名称 - 要查看的NPC
        玩家名称 - 可选，指定玩家的对话历史
        数量 - 可选，显示的对话数量（默认10）
        
    示例:
        npc_history 青云长老
        npc_history 青云长老 张三 5
    """
    
    key = "npc_history"
    aliases = ["npc历史", "npc_log"]
    locks = "cmd:perm(Builder)"
    help_category = "NPC管理"
    
    def func(self):
        """执行命令"""
        if not NPC_SYSTEM_AVAILABLE:
            self.caller.msg("NPC系统不可用")
            return
        
        if not self.args:
            self.caller.msg("用法: npc_history <NPC名称> [玩家名称] [数量]")
            return
        
        args = self.args.split()
        npc_name = args[0]
        player_name = args[1] if len(args) > 1 else None
        count = int(args[2]) if len(args) > 2 and args[2].isdigit() else 10
        
        # 查找NPC
        npc = search.search_object(npc_name)
        if not npc:
            self.caller.msg(f"未找到NPC: {npc_name}")
            return
        
        npc = npc[0]
        if not isinstance(npc, IntelligentNPC):
            self.caller.msg(f"{npc.key} 不是智能NPC")
            return
        
        # 获取对话历史
        conversation_history = npc.db.npc_state.get("conversation_history", {})
        
        if player_name:
            # 显示特定玩家的对话历史
            if player_name not in conversation_history:
                self.caller.msg(f"{npc.key} 与 {player_name} 没有对话历史")
                return
            
            history = conversation_history[player_name][-count:]
            self._show_player_conversation_history(npc.key, player_name, history)
        else:
            # 显示所有对话历史概览
            self._show_all_conversation_overview(npc.key, conversation_history, count)
    
    def _show_player_conversation_history(self, npc_name, player_name, history):
        """显示特定玩家的对话历史"""
        import time
        
        self.caller.msg(f"=== {npc_name} 与 {player_name} 的对话历史 ===")
        
        for i, entry in enumerate(history, 1):
            timestamp = entry.get("timestamp", 0)
            message = entry.get("message", "")
            location = entry.get("location", "未知")
            
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
            self.caller.msg(f"{i}. [{time_str}] 在{location}: {message}")
    
    def _show_all_conversation_overview(self, npc_name, conversation_history, count):
        """显示所有对话历史概览"""
        self.caller.msg(f"=== {npc_name} 对话历史概览 ===")
        
        if not conversation_history:
            self.caller.msg("没有对话历史")
            return
        
        table = evtable.EvTable("玩家", "对话数", "最后对话时间", border="cells")
        
        for player_name, history in conversation_history.items():
            if not history:
                continue
            
            conversation_count = len(history)
            last_conversation = history[-1] if history else {}
            last_time = last_conversation.get("timestamp", 0)
            
            import time
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(last_time)) if last_time else "从未"
            
            table.add_row(player_name, str(conversation_count), time_str)
        
        self.caller.msg(table)


class CmdCreateSectNPCs(Command):
    """
    为门派创建标准NPC套装
    
    用法:
        create_sect_npcs <门派名称>
        
    会创建一套标准的门派NPC：
        - 1个长老
        - 1个师兄
        - 1个师姐
        - 1个师弟
        - 1个师妹
        
    示例:
        create_sect_npcs 青云门
    """
    
    key = "create_sect_npcs"
    aliases = ["创建门派npc", "sect_npcs"]
    locks = "cmd:perm(Builder)"
    help_category = "NPC管理"
    
    def func(self):
        """执行命令"""
        if not NPC_SYSTEM_AVAILABLE:
            self.caller.msg("NPC系统不可用")
            return
        
        if not self.args:
            self.caller.msg("用法: create_sect_npcs <门派名称>")
            return
        
        sect_name = self.args.strip()
        
        try:
            npcs = NPCFactory.create_sect_npcs(sect_name, self.caller.location)
            
            self.caller.msg(f"成功为{sect_name}创建了{len(npcs)}个NPC:")
            for npc in npcs:
                self.caller.msg(f"- {npc.key} ({getattr(npc, '角色类型', '未知')})")
            
        except Exception as e:
            self.caller.msg(f"创建门派NPC失败: {e}")
            logger.log_err(f"创建门派NPC失败: {e}")


class NPCManagementCmdSet(CmdSet):
    """NPC管理命令集"""
    
    key = "NPCManagement"
    
    def at_cmdset_creation(self):
        """创建命令集"""
        self.add(CmdCreateNPC())
        self.add(CmdNPCStatus())
        self.add(CmdNPCConfig())
        self.add(CmdNPCHistory())
        self.add(CmdCreateSectNPCs())
