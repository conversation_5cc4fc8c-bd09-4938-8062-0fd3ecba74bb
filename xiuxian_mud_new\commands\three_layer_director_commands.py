"""
三层AI导演架构管理命令

提供管理和监控三层AI导演系统的命令：
- 导演状态查看
- 导演控制（启动/停止/重启）
- 性能监控
- 消息和协调管理
- 调试工具
"""

from evennia.commands.command import Command
from evennia.commands.cmdset import CmdSet
from evennia.utils import logger

try:
    from scripts.ai_directors.director_integration import Director<PERSON>ayer, get_director_integration
    DIRECTOR_INTEGRATION_AVAILABLE = True
except ImportError:
    DIRECTOR_INTEGRATION_AVAILABLE = False
    logger.log_warn("导演集成系统未找到")


class CmdDirectorStatus(Command):
    """
    查看三层AI导演系统状态
    
    用法:
        director_status [layer]
        
    参数:
        layer - 可选，指定查看的导演层级 (tiandao/diling/qiling)
        
    示例:
        director_status          # 查看所有导演状态
        director_status tiandao  # 查看天道导演状态
    """
    
    key = "director_status"
    aliases = ["dstatus", "ds"]
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"
    
    def func(self):
        """执行命令"""
        if not DIRECTOR_INTEGRATION_AVAILABLE:
            self.caller.msg("导演集成系统不可用")
            return
        
        args = self.args.strip().lower()
        
        # 获取导演集成系统
        integration = get_director_integration()
        if not integration:
            self.caller.msg("导演集成系统未找到")
            return
        
        if args and args in ["tiandao", "diling", "qiling"]:
            # 查看特定导演状态
            layer = DirectorLayer(args)
            self._show_single_director_status(integration, layer)
        else:
            # 查看所有导演状态
            self._show_all_directors_status(integration)
    
    def _show_all_directors_status(self, integration):
        """显示所有导演状态"""
        self.caller.msg("|c=== 三层AI导演系统状态 ===|n")
        
        registered_directors = integration.db.registered_directors
        performance_metrics = integration.get_performance_metrics()
        
        # 显示各层导演状态
        for layer in DirectorLayer:
            director = registered_directors.get(layer)
            status = "运行中" if director and director.is_active else "未运行"
            
            self.caller.msg(f"|w{layer.value.upper()}导演|n: {status}")
            
            if director:
                stats = director.db.performance_stats
                self.caller.msg(f"  - 决策次数: {stats['total_decisions']}")
                self.caller.msg(f"  - 平均响应时间: {stats['average_response_time']:.3f}秒")
                self.caller.msg(f"  - 错误次数: {stats['error_count']}")
        
        # 显示集成系统性能
        self.caller.msg(f"\n|w集成系统性能|n:")
        self.caller.msg(f"  - 消息总数: {performance_metrics['message_count']}")
        self.caller.msg(f"  - 协调请求: {performance_metrics['coordination_count']}")
        self.caller.msg(f"  - 平均响应时间: {performance_metrics['average_response_time']:.3f}秒")
        self.caller.msg(f"  - 错误次数: {performance_metrics['error_count']}")
    
    def _show_single_director_status(self, integration, layer):
        """显示单个导演状态"""
        director = integration.db.registered_directors.get(layer)
        
        if not director:
            self.caller.msg(f"{layer.value.upper()}导演未注册")
            return
        
        self.caller.msg(f"|c=== {layer.value.upper()}导演详细状态 ===|n")
        
        # 基本状态
        status = "运行中" if director.is_active else "未运行"
        self.caller.msg(f"|w状态|n: {status}")
        self.caller.msg(f"|w脚本键|n: {director.key}")
        self.caller.msg(f"|w描述|n: {director.desc}")
        
        # 性能统计
        stats = director.db.performance_stats
        self.caller.msg(f"\n|w性能统计|n:")
        self.caller.msg(f"  - 总决策次数: {stats['total_decisions']}")
        self.caller.msg(f"  - 总处理时间: {stats['total_processing_time']:.3f}秒")
        self.caller.msg(f"  - 平均响应时间: {stats['average_response_time']:.3f}秒")
        self.caller.msg(f"  - 错误次数: {stats['error_count']}")
        self.caller.msg(f"  - 上次决策时间: {stats.get('last_decision_time', '无')}")
        
        # 决策历史（最近5条）
        history = director.db.decision_history or []
        if history:
            self.caller.msg(f"\n|w最近决策历史|n:")
            for i, record in enumerate(history[-5:], 1):
                success_str = "成功" if record['success'] else "失败"
                self.caller.msg(f"  {i}. {record['timestamp']:.0f} - {record['processing_time']:.3f}秒 - {success_str}")


class CmdDirectorControl(Command):
    """
    控制三层AI导演系统
    
    用法:
        director_control <action> [layer]
        
    参数:
        action - 操作类型 (start/stop/restart/force_decision)
        layer  - 可选，指定操作的导演层级 (tiandao/diling/qiling)
        
    示例:
        director_control start           # 启动所有导演
        director_control stop tiandao    # 停止天道导演
        director_control restart diling  # 重启地灵导演
        director_control force_decision qiling  # 强制器灵导演做决策
    """
    
    key = "director_control"
    aliases = ["dcontrol", "dc"]
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"
    
    def func(self):
        """执行命令"""
        if not DIRECTOR_INTEGRATION_AVAILABLE:
            self.caller.msg("导演集成系统不可用")
            return
        
        if not self.args:
            self.caller.msg("用法: director_control <action> [layer]")
            return
        
        args = self.args.split()
        action = args[0].lower()
        layer_name = args[1].lower() if len(args) > 1 else None
        
        # 获取导演集成系统
        integration = get_director_integration()
        if not integration:
            self.caller.msg("导演集成系统未找到")
            return
        
        if action == "start":
            self._start_directors(integration, layer_name)
        elif action == "stop":
            self._stop_directors(integration, layer_name)
        elif action == "restart":
            self._restart_directors(integration, layer_name)
        elif action == "force_decision":
            self._force_decision(integration, layer_name)
        else:
            self.caller.msg(f"未知操作: {action}")
    
    def _start_directors(self, integration, layer_name):
        """启动导演"""
        if layer_name:
            if layer_name not in ["tiandao", "diling", "qiling"]:
                self.caller.msg(f"未知导演层级: {layer_name}")
                return
            
            layer = DirectorLayer(layer_name)
            director = integration.db.registered_directors.get(layer)
            
            if director:
                if director.is_active:
                    self.caller.msg(f"{layer_name.upper()}导演已在运行")
                else:
                    director.start()
                    self.caller.msg(f"{layer_name.upper()}导演已启动")
            else:
                self.caller.msg(f"{layer_name.upper()}导演未注册")
        else:
            # 启动所有导演
            started_count = 0
            for layer, director in integration.db.registered_directors.items():
                if director and not director.is_active:
                    director.start()
                    started_count += 1
            
            self.caller.msg(f"已启动 {started_count} 个导演")
    
    def _stop_directors(self, integration, layer_name):
        """停止导演"""
        if layer_name:
            if layer_name not in ["tiandao", "diling", "qiling"]:
                self.caller.msg(f"未知导演层级: {layer_name}")
                return
            
            layer = DirectorLayer(layer_name)
            director = integration.db.registered_directors.get(layer)
            
            if director:
                if not director.is_active:
                    self.caller.msg(f"{layer_name.upper()}导演已停止")
                else:
                    director.stop()
                    self.caller.msg(f"{layer_name.upper()}导演已停止")
            else:
                self.caller.msg(f"{layer_name.upper()}导演未注册")
        else:
            # 停止所有导演
            stopped_count = 0
            for layer, director in integration.db.registered_directors.items():
                if director and director.is_active:
                    director.stop()
                    stopped_count += 1
            
            self.caller.msg(f"已停止 {stopped_count} 个导演")
    
    def _restart_directors(self, integration, layer_name):
        """重启导演"""
        if layer_name:
            if layer_name not in ["tiandao", "diling", "qiling"]:
                self.caller.msg(f"未知导演层级: {layer_name}")
                return
            
            layer = DirectorLayer(layer_name)
            director = integration.db.registered_directors.get(layer)
            
            if director:
                director.restart()
                self.caller.msg(f"{layer_name.upper()}导演已重启")
            else:
                self.caller.msg(f"{layer_name.upper()}导演未注册")
        else:
            # 重启所有导演
            restarted_count = 0
            for layer, director in integration.db.registered_directors.items():
                if director:
                    director.restart()
                    restarted_count += 1
            
            self.caller.msg(f"已重启 {restarted_count} 个导演")
    
    def _force_decision(self, integration, layer_name):
        """强制导演做决策"""
        if not layer_name:
            self.caller.msg("强制决策需要指定导演层级")
            return
        
        if layer_name not in ["tiandao", "diling", "qiling"]:
            self.caller.msg(f"未知导演层级: {layer_name}")
            return
        
        layer = DirectorLayer(layer_name)
        director = integration.db.registered_directors.get(layer)
        
        if director:
            if hasattr(director, 'force_decision'):
                director.force_decision()
                self.caller.msg(f"已强制{layer_name.upper()}导演做决策")
            else:
                self.caller.msg(f"{layer_name.upper()}导演不支持强制决策")
        else:
            self.caller.msg(f"{layer_name.upper()}导演未注册")


class CmdDirectorMessages(Command):
    """
    查看和管理导演间消息
    
    用法:
        director_messages [action] [args]
        
    参数:
        action - 操作类型 (list/send/clear)
        
    示例:
        director_messages                    # 查看消息队列
        director_messages list               # 查看消息队列
        director_messages clear              # 清空消息队列
        director_messages send tiandao diling test_message  # 发送测试消息
    """
    
    key = "director_messages"
    aliases = ["dmsg", "dm"]
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"
    
    def func(self):
        """执行命令"""
        if not DIRECTOR_INTEGRATION_AVAILABLE:
            self.caller.msg("导演集成系统不可用")
            return
        
        # 获取导演集成系统
        integration = get_director_integration()
        if not integration:
            self.caller.msg("导演集成系统未找到")
            return
        
        args = self.args.split() if self.args else []
        action = args[0].lower() if args else "list"
        
        if action == "list":
            self._list_messages(integration)
        elif action == "clear":
            self._clear_messages(integration)
        elif action == "send":
            if len(args) >= 4:
                from_layer = args[1]
                to_layer = args[2]
                message_type = args[3]
                self._send_test_message(integration, from_layer, to_layer, message_type)
            else:
                self.caller.msg("用法: director_messages send <from_layer> <to_layer> <message_type>")
        else:
            self.caller.msg(f"未知操作: {action}")
    
    def _list_messages(self, integration):
        """列出消息队列"""
        queue = integration.db.communication_state["message_queue"]
        
        if not queue:
            self.caller.msg("消息队列为空")
            return
        
        self.caller.msg("|c=== 导演间消息队列 ===|n")
        
        # 显示最近20条消息
        recent_messages = queue[-20:] if len(queue) > 20 else queue
        
        for i, message in enumerate(recent_messages, 1):
            status_color = "|g" if message["status"] == "delivered" else "|r" if message["status"] == "failed" else "|y"
            
            self.caller.msg(f"{i}. {status_color}{message['status']}|n - "
                          f"{message['from_layer']} -> {message['to_layer']} "
                          f"({message['message_type']}) - {message['timestamp']:.0f}")
        
        if len(queue) > 20:
            self.caller.msg(f"\n显示最近20条，总共{len(queue)}条消息")
    
    def _clear_messages(self, integration):
        """清空消息队列"""
        integration.db.communication_state["message_queue"] = []
        self.caller.msg("消息队列已清空")
    
    def _send_test_message(self, integration, from_layer, to_layer, message_type):
        """发送测试消息"""
        try:
            from_layer_enum = DirectorLayer(from_layer)
            to_layer_enum = DirectorLayer(to_layer)
            
            integration.send_message(
                from_layer_enum,
                to_layer_enum,
                message_type,
                {"test": True, "content": "测试消息"},
                "normal"
            )
            
            self.caller.msg(f"测试消息已发送: {from_layer} -> {to_layer} ({message_type})")
            
        except ValueError as e:
            self.caller.msg(f"无效的导演层级: {e}")


class CmdDirectorCoordination(Command):
    """
    查看和管理导演协调请求

    用法:
        director_coordination [action] [args]

    参数:
        action - 操作类型 (list/request/clear)

    示例:
        director_coordination                           # 查看协调请求
        director_coordination list                      # 查看协调请求
        director_coordination clear                     # 清空协调请求
        director_coordination request tiandao world_event  # 创建协调请求
    """

    key = "director_coordination"
    aliases = ["dcoord", "dcr"]
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """执行命令"""
        if not DIRECTOR_INTEGRATION_AVAILABLE:
            self.caller.msg("导演集成系统不可用")
            return

        # 获取导演集成系统
        integration = get_director_integration()
        if not integration:
            self.caller.msg("导演集成系统未找到")
            return

        args = self.args.split() if self.args else []
        action = args[0].lower() if args else "list"

        if action == "list":
            self._list_coordination_requests(integration)
        elif action == "clear":
            self._clear_coordination_requests(integration)
        elif action == "request":
            if len(args) >= 3:
                requesting_layer = args[1]
                coordination_type = args[2]
                self._create_coordination_request(integration, requesting_layer, coordination_type)
            else:
                self.caller.msg("用法: director_coordination request <requesting_layer> <coordination_type>")
        else:
            self.caller.msg(f"未知操作: {action}")

    def _list_coordination_requests(self, integration):
        """列出协调请求"""
        requests = integration.db.communication_state["coordination_requests"]

        if not requests:
            self.caller.msg("协调请求队列为空")
            return

        self.caller.msg("|c=== 导演协调请求 ===|n")

        for i, request in enumerate(requests[-10:], 1):  # 显示最近10条
            status_color = "|g" if request["status"] == "completed" else "|r" if request["status"] == "failed" else "|y"

            self.caller.msg(f"{i}. {status_color}{request['status']}|n - "
                          f"{request['requesting_layer']} ({request['coordination_type']}) - "
                          f"{request['timestamp']:.0f}")

            if request["status"] == "completed" and "result" in request:
                result = request["result"]
                self.caller.msg(f"   结果: {result.get('decision', '未知')}")

    def _clear_coordination_requests(self, integration):
        """清空协调请求"""
        integration.db.communication_state["coordination_requests"] = []
        self.caller.msg("协调请求队列已清空")

    def _create_coordination_request(self, integration, requesting_layer, coordination_type):
        """创建协调请求"""
        try:
            requesting_layer_enum = DirectorLayer(requesting_layer)

            integration.request_coordination(
                requesting_layer_enum,
                coordination_type,
                {"test": True, "description": "测试协调请求"}
            )

            self.caller.msg(f"协调请求已创建: {requesting_layer} ({coordination_type})")

        except ValueError as e:
            self.caller.msg(f"无效的导演层级: {e}")


class CmdDirectorDebug(Command):
    """
    AI导演系统调试工具

    用法:
        director_debug <action> [args]

    参数:
        action - 调试操作 (context/performance/events/reset/init)

    示例:
        director_debug context           # 查看共享上下文
        director_debug performance       # 查看性能详情
        director_debug events           # 查看事件历史
        director_debug reset tiandao    # 重置导演状态
        director_debug init             # 初始化导演系统
    """

    key = "director_debug"
    aliases = ["ddebug", "dd"]
    locks = "cmd:perm(Builder)"
    help_category = "AI Director"

    def func(self):
        """执行命令"""
        if not self.args:
            self.caller.msg("用法: director_debug <action> [args]")
            return

        args = self.args.split()
        action = args[0].lower()

        if action == "init":
            self._initialize_director_system()
            return

        if not DIRECTOR_INTEGRATION_AVAILABLE:
            self.caller.msg("导演集成系统不可用")
            return

        # 获取导演集成系统
        integration = get_director_integration()
        if not integration:
            self.caller.msg("导演集成系统未找到")
            return

        if action == "context":
            self._show_shared_context(integration)
        elif action == "performance":
            self._show_performance_details(integration)
        elif action == "events":
            self._show_event_history(integration)
        elif action == "reset":
            layer_name = args[1] if len(args) > 1 else None
            self._reset_director_state(integration, layer_name)
        else:
            self.caller.msg(f"未知调试操作: {action}")

    def _initialize_director_system(self):
        """初始化导演系统"""
        self.caller.msg("开始初始化三层AI导演系统...")

        try:
            from scripts.ai_directors.director_initialization import initialize_director_system, get_director_system_status

            # 获取当前状态
            status = get_director_system_status()

            if status["initialization_complete"]:
                self.caller.msg("导演系统已完成初始化")
                self._show_system_status(status)
                return

            # 执行初始化
            success = initialize_director_system()

            if success:
                self.caller.msg("导演系统初始化已启动，请稍后检查状态")
            else:
                self.caller.msg("导演系统初始化失败")

        except ImportError:
            self.caller.msg("导演初始化模块未找到")
        except Exception as e:
            self.caller.msg(f"初始化过程中发生错误: {e}")

    def _show_system_status(self, status):
        """显示系统状态"""
        self.caller.msg("|c=== 导演系统状态 ===|n")
        self.caller.msg(f"集成系统可用: {'是' if status['integration_available'] else '否'}")

        for layer, registered in status["directors_registered"].items():
            active = status["directors_active"].get(layer, False)
            status_str = "运行中" if active else "已注册" if registered else "未注册"
            self.caller.msg(f"{layer.upper()}导演: {status_str}")

        if status["initialization_error"]:
            self.caller.msg(f"|r初始化错误: {status['initialization_error']}|n")

    def _show_shared_context(self, integration):
        """显示共享上下文"""
        context = integration.get_shared_context()

        if not context:
            self.caller.msg("共享上下文为空")
            return

        self.caller.msg("|c=== 导演共享上下文 ===|n")

        for layer_key, layer_context in context.items():
            self.caller.msg(f"\n|w{layer_key.upper()}导演上下文|n:")

            for context_key, context_data in layer_context.items():
                timestamp = context_data.get("timestamp", 0)
                value = context_data.get("value", "无")

                self.caller.msg(f"  - {context_key}: {value} (时间: {timestamp:.0f})")

    def _show_performance_details(self, integration):
        """显示性能详情"""
        metrics = integration.get_performance_metrics()

        self.caller.msg("|c=== 导演系统性能详情 ===|n")

        for key, value in metrics.items():
            if isinstance(value, float):
                self.caller.msg(f"{key}: {value:.3f}")
            else:
                self.caller.msg(f"{key}: {value}")

    def _show_event_history(self, integration):
        """显示事件历史"""
        # 这里可以扩展显示事件总线的历史记录
        # integration参数用于未来扩展
        self.caller.msg("事件历史功能待实现")

    def _reset_director_state(self, integration, layer_name):
        """重置导演状态"""
        if not layer_name:
            self.caller.msg("重置操作需要指定导演层级")
            return

        if layer_name not in ["tiandao", "diling", "qiling"]:
            self.caller.msg(f"未知导演层级: {layer_name}")
            return

        layer = DirectorLayer(layer_name)
        director = integration.db.registered_directors.get(layer)

        if director:
            # 重置性能统计
            director.db.performance_stats = {
                "total_decisions": 0,
                "total_processing_time": 0.0,
                "average_response_time": 0.0,
                "error_count": 0,
                "last_decision_time": None
            }

            # 清空决策历史
            director.db.decision_history = []

            self.caller.msg(f"{layer_name.upper()}导演状态已重置")
        else:
            self.caller.msg(f"{layer_name.upper()}导演未注册")


class ThreeLayerDirectorCmdSet(CmdSet):
    """三层AI导演命令集"""

    key = "Three Layer Director CmdSet"

    def at_cmdset_creation(self):
        """创建命令集"""
        self.add(CmdDirectorStatus())
        self.add(CmdDirectorControl())
        self.add(CmdDirectorMessages())
        self.add(CmdDirectorCoordination())
        self.add(CmdDirectorDebug())
