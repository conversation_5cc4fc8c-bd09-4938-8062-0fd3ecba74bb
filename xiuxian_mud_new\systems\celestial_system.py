"""
天象灵气系统

为地灵导演提供天象和灵气管理功能：
- 灵气潮汐管理
- 五行平衡调节
- 季节性变化
- 天象现象控制
"""

import time
import math
import random
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from evennia.utils import logger
from evennia.scripts.scripts import DefaultScript

from .event_system import BaseEvent, EventPriority, publish_event


class Element(Enum):
    """五行元素"""
    METAL = "金"  # 金
    WOOD = "木"   # 木
    WATER = "水"  # 水
    FIRE = "火"   # 火
    EARTH = "土"  # 土


class Season(Enum):
    """季节"""
    SPRING = "春"  # 春
    SUMMER = "夏"  # 夏
    AUTUMN = "秋"  # 秋
    WINTER = "冬"  # 冬


class TidePhase(Enum):
    """灵气潮汐阶段"""
    LOW = "低潮"      # 低潮
    RISING = "涨潮"   # 涨潮
    HIGH = "高潮"     # 高潮
    FALLING = "落潮"  # 落潮


@dataclass
class SpiritualTide:
    """灵气潮汐数据结构"""
    phase: TidePhase
    intensity: float
    duration: int
    affected_elements: List[Element]
    regional_effects: Dict[str, float]


@dataclass
class CelestialPhenomenon:
    """天象现象数据结构"""
    phenomenon_id: str
    name: str
    description: str
    start_time: float
    duration: int
    effects: Dict[str, Any]
    visibility_regions: List[str]
    spiritual_impact: float
    status: str  # active, completed, cancelled


class CelestialSystem(DefaultScript):
    """
    天象灵气系统
    
    管理修仙界的天象和灵气变化
    """
    
    def at_script_creation(self):
        """系统初始化"""
        self.key = "celestial_system"
        self.desc = "天象灵气系统"
        self.persistent = True
        self.interval = 60  # 1分钟更新一次
        
        # 初始化五行平衡
        self.db.five_elements_balance = {
            Element.METAL: 1.0,
            Element.WOOD: 1.0,
            Element.WATER: 1.0,
            Element.FIRE: 1.0,
            Element.EARTH: 1.0
        }
        
        # 初始化灵气潮汐
        self.db.spiritual_tides = {
            "current_tide": SpiritualTide(
                phase=TidePhase.LOW,
                intensity=1.0,
                duration=7200,  # 2小时
                affected_elements=[],
                regional_effects={}
            ),
            "tide_cycle_start": time.time(),
            "tide_history": []
        }
        
        # 初始化季节系统
        self.db.seasonal_system = {
            "current_season": self._calculate_current_season(),
            "season_start": time.time(),
            "seasonal_effects": self._get_seasonal_effects(),
            "transition_progress": 0.0
        }
        
        # 初始化天象现象
        self.db.celestial_phenomena = {
            "active_phenomena": {},
            "phenomenon_history": [],
            "generation_config": {
                "base_chance": 0.05,  # 基础生成概率
                "max_concurrent": 3   # 最大同时现象数
            }
        }
        
        # 区域配置
        self.db.regional_config = {
            "青云山": {
                "dominant_element": Element.WOOD,
                "spiritual_density": 1.2,
                "seasonal_sensitivity": 1.1
            },
            "天音山": {
                "dominant_element": Element.METAL,
                "spiritual_density": 1.1,
                "seasonal_sensitivity": 0.9
            },
            "焚香谷": {
                "dominant_element": Element.FIRE,
                "spiritual_density": 1.0,
                "seasonal_sensitivity": 1.0
            },
            "万毒门": {
                "dominant_element": Element.WATER,
                "spiritual_density": 0.9,
                "seasonal_sensitivity": 1.2
            }
        }
        
        logger.log_info("天象灵气系统已初始化")
    
    def at_repeat(self):
        """定期更新天象灵气状态"""
        try:
            # 更新灵气潮汐
            self._update_spiritual_tides()
            
            # 更新五行平衡
            self._update_five_elements_balance()
            
            # 更新季节系统
            self._update_seasonal_system()
            
            # 更新天象现象
            self._update_celestial_phenomena()
            
            # 检查天象现象生成
            self._check_phenomenon_generation()
            
        except Exception as e:
            logger.log_err(f"天象灵气系统更新失败: {e}")
    
    def adjust_element_balance(self, element: Element, adjustment: float, 
                             duration: int = 3600, reason: str = ""):
        """
        调整五行平衡
        
        Args:
            element: 要调整的元素
            adjustment: 调整倍率
            duration: 持续时间（秒）
            reason: 调整原因
        """
        try:
            old_value = self.db.five_elements_balance[element]
            self.db.five_elements_balance[element] *= adjustment
            
            logger.log_info(f"调整五行平衡: {element.value} {old_value:.2f} -> {self.db.five_elements_balance[element]:.2f} ({reason})")
            
            # 发布五行变化事件
            self._publish_element_change_event(element, old_value, self.db.five_elements_balance[element], reason)
            
            # 如果有持续时间，设置恢复任务
            if duration > 0:
                self._schedule_element_restoration(element, old_value, duration)
                
        except Exception as e:
            logger.log_err(f"调整五行平衡失败: {e}")
    
    def trigger_spiritual_tide(self, phase: TidePhase, intensity: float = 1.0,
                             duration: int = 7200, affected_elements: List[Element] = None):
        """
        触发灵气潮汐
        
        Args:
            phase: 潮汐阶段
            intensity: 强度倍率
            duration: 持续时间
            affected_elements: 受影响的元素
        """
        try:
            # 创建新的灵气潮汐
            new_tide = SpiritualTide(
                phase=phase,
                intensity=intensity,
                duration=duration,
                affected_elements=affected_elements or [],
                regional_effects=self._calculate_regional_effects(intensity, affected_elements or [])
            )
            
            # 更新当前潮汐
            self.db.spiritual_tides["current_tide"] = new_tide
            self.db.spiritual_tides["tide_cycle_start"] = time.time()
            
            logger.log_info(f"触发灵气潮汐: {phase.value} 强度{intensity} 持续{duration}秒")
            
            # 发布潮汐变化事件
            self._publish_tide_change_event(new_tide)
            
        except Exception as e:
            logger.log_err(f"触发灵气潮汐失败: {e}")
    
    def create_celestial_phenomenon(self, name: str, description: str,
                                  duration: int, effects: Dict[str, Any],
                                  visibility_regions: List[str] = None) -> Optional[str]:
        """
        创建天象现象
        
        Args:
            name: 现象名称
            description: 现象描述
            duration: 持续时间
            effects: 效果
            visibility_regions: 可见区域
            
        Returns:
            现象ID，失败返回None
        """
        try:
            # 检查并发限制
            active_count = len(self.db.celestial_phenomena["active_phenomena"])
            max_concurrent = self.db.celestial_phenomena["generation_config"]["max_concurrent"]
            
            if active_count >= max_concurrent:
                logger.log_warn(f"天象现象数量已达上限: {active_count}/{max_concurrent}")
                return None
            
            # 生成现象ID
            phenomenon_id = f"celestial_{int(time.time())}_{random.randint(1000, 9999)}"
            
            # 创建天象现象
            phenomenon = CelestialPhenomenon(
                phenomenon_id=phenomenon_id,
                name=name,
                description=description,
                start_time=time.time(),
                duration=duration,
                effects=effects,
                visibility_regions=visibility_regions or list(self.db.regional_config.keys()),
                spiritual_impact=self._calculate_spiritual_impact(effects),
                status="active"
            )
            
            # 注册现象
            self.db.celestial_phenomena["active_phenomena"][phenomenon_id] = phenomenon
            
            logger.log_info(f"创建天象现象: {name} (ID: {phenomenon_id})")
            
            # 发布天象事件
            self._publish_celestial_event(phenomenon)
            
            return phenomenon_id
            
        except Exception as e:
            logger.log_err(f"创建天象现象失败: {e}")
            return None
    
    def get_current_spiritual_state(self) -> Dict[str, Any]:
        """获取当前灵气状态"""
        current_tide = self.db.spiritual_tides["current_tide"]
        
        return {
            "five_elements_balance": {elem.value: balance for elem, balance in self.db.five_elements_balance.items()},
            "spiritual_tide": {
                "phase": current_tide.phase.value,
                "intensity": current_tide.intensity,
                "affected_elements": [elem.value for elem in current_tide.affected_elements]
            },
            "current_season": self.db.seasonal_system["current_season"].value,
            "seasonal_effects": self.db.seasonal_system["seasonal_effects"],
            "active_phenomena": len(self.db.celestial_phenomena["active_phenomena"]),
            "overall_spiritual_density": self._calculate_overall_spiritual_density()
        }
    
    def get_regional_spiritual_state(self, region: str) -> Dict[str, Any]:
        """获取区域灵气状态"""
        if region not in self.db.regional_config:
            return {}
        
        region_config = self.db.regional_config[region]
        current_tide = self.db.spiritual_tides["current_tide"]
        
        # 计算区域灵气密度
        base_density = region_config["spiritual_density"]
        tide_effect = current_tide.regional_effects.get(region, 1.0)
        seasonal_effect = self._get_regional_seasonal_effect(region)
        element_effect = self._get_regional_element_effect(region)
        
        total_density = base_density * tide_effect * seasonal_effect * element_effect
        
        return {
            "region": region,
            "dominant_element": region_config["dominant_element"].value,
            "base_spiritual_density": base_density,
            "current_spiritual_density": total_density,
            "tide_effect": tide_effect,
            "seasonal_effect": seasonal_effect,
            "element_effect": element_effect,
            "visible_phenomena": self._get_visible_phenomena(region)
        }
    
    def _update_spiritual_tides(self):
        """更新灵气潮汐"""
        current_time = time.time()
        tide_data = self.db.spiritual_tides
        current_tide = tide_data["current_tide"]
        
        # 检查潮汐是否应该转换
        elapsed_time = current_time - tide_data["tide_cycle_start"]
        
        if elapsed_time >= current_tide.duration:
            # 转换到下一个潮汐阶段
            next_phase = self._get_next_tide_phase(current_tide.phase)
            self._transition_to_next_tide(next_phase)
    
    def _update_five_elements_balance(self):
        """更新五行平衡"""
        # 自然恢复机制
        for element in Element:
            current_balance = self.db.five_elements_balance[element]
            
            # 向1.0缓慢恢复
            if current_balance != 1.0:
                recovery_rate = 0.001  # 每分钟恢复0.1%
                if current_balance > 1.0:
                    self.db.five_elements_balance[element] = max(1.0, current_balance - recovery_rate)
                else:
                    self.db.five_elements_balance[element] = min(1.0, current_balance + recovery_rate)
    
    def _update_seasonal_system(self):
        """更新季节系统"""
        current_season = self._calculate_current_season()
        
        if current_season != self.db.seasonal_system["current_season"]:
            # 季节转换
            self.db.seasonal_system["current_season"] = current_season
            self.db.seasonal_system["season_start"] = time.time()
            self.db.seasonal_system["seasonal_effects"] = self._get_seasonal_effects()
            
            logger.log_info(f"季节转换: {current_season.value}")
            
            # 发布季节变化事件
            self._publish_seasonal_change_event(current_season)
    
    def _update_celestial_phenomena(self):
        """更新天象现象"""
        current_time = time.time()
        active_phenomena = self.db.celestial_phenomena["active_phenomena"]
        
        # 检查过期现象
        expired_phenomena = []
        for phenomenon_id, phenomenon in active_phenomena.items():
            if current_time >= phenomenon.start_time + phenomenon.duration:
                phenomenon.status = "completed"
                expired_phenomena.append(phenomenon_id)
        
        # 移除过期现象
        for phenomenon_id in expired_phenomena:
            phenomenon = active_phenomena.pop(phenomenon_id)
            self._record_phenomenon_completion(phenomenon)
    
    def _check_phenomenon_generation(self):
        """检查天象现象生成"""
        config = self.db.celestial_phenomena["generation_config"]
        active_count = len(self.db.celestial_phenomena["active_phenomena"])
        
        if active_count < config["max_concurrent"] and random.random() < config["base_chance"]:
            self._generate_random_phenomenon()
    
    def _calculate_current_season(self) -> Season:
        """计算当前季节"""
        # 基于实际时间的简化季节系统
        day_of_year = (int(time.time() / 86400) % 365)
        
        if day_of_year < 91:
            return Season.SPRING
        elif day_of_year < 182:
            return Season.SUMMER
        elif day_of_year < 273:
            return Season.AUTUMN
        else:
            return Season.WINTER
    
    def _get_seasonal_effects(self) -> Dict[str, float]:
        """获取季节效果"""
        season = self.db.seasonal_system["current_season"]
        
        seasonal_effects = {
            Season.SPRING: {Element.WOOD.value: 1.2, Element.EARTH.value: 0.9},
            Season.SUMMER: {Element.FIRE.value: 1.2, Element.WATER.value: 0.9},
            Season.AUTUMN: {Element.METAL.value: 1.2, Element.WOOD.value: 0.9},
            Season.WINTER: {Element.WATER.value: 1.2, Element.FIRE.value: 0.9}
        }
        
        return seasonal_effects.get(season, {})
    
    def _calculate_regional_effects(self, intensity: float, 
                                  affected_elements: List[Element]) -> Dict[str, float]:
        """计算区域效果"""
        regional_effects = {}
        
        for region, config in self.db.regional_config.items():
            effect = intensity
            
            # 如果区域主导元素受影响，效果增强
            if config["dominant_element"] in affected_elements:
                effect *= 1.2
            
            regional_effects[region] = effect
        
        return regional_effects
    
    def _calculate_overall_spiritual_density(self) -> float:
        """计算整体灵气密度"""
        # 基于五行平衡和当前潮汐计算
        element_average = sum(self.db.five_elements_balance.values()) / len(self.db.five_elements_balance)
        tide_intensity = self.db.spiritual_tides["current_tide"].intensity
        
        return element_average * tide_intensity
    
    def _get_regional_seasonal_effect(self, region: str) -> float:
        """获取区域季节效果"""
        if region not in self.db.regional_config:
            return 1.0
        
        sensitivity = self.db.regional_config[region]["seasonal_sensitivity"]
        seasonal_effects = self.db.seasonal_system["seasonal_effects"]
        dominant_element = self.db.regional_config[region]["dominant_element"]
        
        element_effect = seasonal_effects.get(dominant_element.value, 1.0)
        
        return 1.0 + (element_effect - 1.0) * sensitivity
    
    def _get_regional_element_effect(self, region: str) -> float:
        """获取区域元素效果"""
        if region not in self.db.regional_config:
            return 1.0
        
        dominant_element = self.db.regional_config[region]["dominant_element"]
        return self.db.five_elements_balance[dominant_element]
    
    def _get_visible_phenomena(self, region: str) -> List[str]:
        """获取区域可见的天象现象"""
        visible = []
        
        for phenomenon in self.db.celestial_phenomena["active_phenomena"].values():
            if region in phenomenon.visibility_regions:
                visible.append(phenomenon.name)
        
        return visible
    
    def _get_next_tide_phase(self, current_phase: TidePhase) -> TidePhase:
        """获取下一个潮汐阶段"""
        phase_cycle = [TidePhase.LOW, TidePhase.RISING, TidePhase.HIGH, TidePhase.FALLING]
        current_index = phase_cycle.index(current_phase)
        next_index = (current_index + 1) % len(phase_cycle)
        return phase_cycle[next_index]
    
    def _transition_to_next_tide(self, next_phase: TidePhase):
        """转换到下一个潮汐阶段"""
        # 计算新的强度
        phase_intensities = {
            TidePhase.LOW: 0.8,
            TidePhase.RISING: 1.1,
            TidePhase.HIGH: 1.3,
            TidePhase.FALLING: 1.0
        }
        
        new_intensity = phase_intensities[next_phase]
        
        # 随机选择受影响的元素
        affected_elements = random.sample(list(Element), random.randint(1, 3))
        
        # 触发新潮汐
        self.trigger_spiritual_tide(next_phase, new_intensity, 7200, affected_elements)
    
    def _generate_random_phenomenon(self):
        """生成随机天象现象"""
        phenomena_templates = [
            {
                "name": "流星雨",
                "description": "天空中划过无数流星，灵气波动异常",
                "duration": 1800,
                "effects": {"spiritual_enhancement": 1.1}
            },
            {
                "name": "五彩祥云",
                "description": "天空出现五彩斑斓的祥云，五行之气和谐",
                "duration": 3600,
                "effects": {"element_harmony": 1.05}
            },
            {
                "name": "雷劫征兆",
                "description": "天空雷云密布，似有大能者渡劫",
                "duration": 2400,
                "effects": {"lightning_energy": 1.2}
            }
        ]
        
        template = random.choice(phenomena_templates)
        self.create_celestial_phenomenon(**template)
    
    def _calculate_spiritual_impact(self, effects: Dict[str, Any]) -> float:
        """计算灵气影响度"""
        # 简化的影响度计算
        total_impact = 0.0
        for effect_value in effects.values():
            if isinstance(effect_value, (int, float)):
                total_impact += abs(effect_value - 1.0)
        
        return total_impact
    
    def _publish_element_change_event(self, element: Element, old_value: float, 
                                    new_value: float, reason: str):
        """发布五行变化事件"""
        event = BaseEvent(
            event_type="element_balance_changed",
            source_id=self.key,
            data={
                "element": element.value,
                "old_value": old_value,
                "new_value": new_value,
                "reason": reason
            },
            priority=EventPriority.NORMAL
        )
        publish_event(event)
    
    def _publish_tide_change_event(self, tide: SpiritualTide):
        """发布潮汐变化事件"""
        event = BaseEvent(
            event_type="spiritual_tide_changed",
            source_id=self.key,
            data={
                "phase": tide.phase.value,
                "intensity": tide.intensity,
                "affected_elements": [elem.value for elem in tide.affected_elements],
                "duration": tide.duration
            },
            priority=EventPriority.NORMAL
        )
        publish_event(event)
    
    def _publish_seasonal_change_event(self, season: Season):
        """发布季节变化事件"""
        event = BaseEvent(
            event_type="season_changed",
            source_id=self.key,
            data={
                "new_season": season.value,
                "seasonal_effects": self.db.seasonal_system["seasonal_effects"]
            },
            priority=EventPriority.NORMAL
        )
        publish_event(event)
    
    def _publish_celestial_event(self, phenomenon: CelestialPhenomenon):
        """发布天象事件"""
        event = BaseEvent(
            event_type="celestial_phenomenon_created",
            source_id=self.key,
            data={
                "phenomenon_id": phenomenon.phenomenon_id,
                "name": phenomenon.name,
                "description": phenomenon.description,
                "duration": phenomenon.duration,
                "visibility_regions": phenomenon.visibility_regions
            },
            priority=EventPriority.NORMAL
        )
        publish_event(event)
    
    def _schedule_element_restoration(self, element: Element, original_value: float, duration: int):
        """安排元素恢复"""
        # 这里可以实现延时恢复机制
        # 简化实现：记录恢复时间
        pass
    
    def _record_phenomenon_completion(self, phenomenon: CelestialPhenomenon):
        """记录现象完成"""
        completion_record = {
            "phenomenon_id": phenomenon.phenomenon_id,
            "name": phenomenon.name,
            "start_time": phenomenon.start_time,
            "duration": phenomenon.duration,
            "completion_time": time.time(),
            "spiritual_impact": phenomenon.spiritual_impact
        }
        
        self.db.celestial_phenomena["phenomenon_history"].append(completion_record)
        
        # 保留最近100个记录
        if len(self.db.celestial_phenomena["phenomenon_history"]) > 100:
            self.db.celestial_phenomena["phenomenon_history"] = self.db.celestial_phenomena["phenomenon_history"][-100:]
        
        logger.log_info(f"天象现象已完成: {phenomenon.name}")


def get_celestial_system() -> Optional[CelestialSystem]:
    """获取天象灵气系统实例"""
    from evennia import search_script
    
    scripts = search_script("celestial_system")
    return scripts[0] if scripts else None
