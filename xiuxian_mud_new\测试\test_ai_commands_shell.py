#!/usr/bin/env python3
"""
通过Evennia shell测试AI导演命令
"""

print("=== 测试AI导演命令系统 ===")

# 导入必要的模块
from evennia import AccountDB, ObjectDB
from commands.ai_director_commands import CmdAIDirectorStatus, CmdAIDirectorHelp

# 获取admin账户和角色
try:
    admin_account = AccountDB.objects.get(username="admin")
    character = admin_account.db._last_puppet
    if not character:
        characters = ObjectDB.objects.filter(db_account=admin_account)
        if characters:
            character = characters[0]
    
    if character:
        print(f"找到角色: {character.key}")
        
        # 测试ai帮助命令
        help_cmd = CmdAIDirectorHelp()
        help_cmd.caller = character
        help_cmd.args = ""
        
        print("\n执行 'ai帮助' 命令:")
        help_cmd.func()
        
        # 测试ai状态命令
        status_cmd = CmdAIDirectorStatus()
        status_cmd.caller = character
        status_cmd.args = ""
        
        print("\n执行 'ai状态' 命令:")
        status_cmd.func()
        
        print("\n✓ AI导演命令测试完成！")
        
    else:
        print("✗ 未找到角色")
        
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()