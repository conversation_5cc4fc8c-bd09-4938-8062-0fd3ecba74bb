"""
Characters

Characters are (by default) Objects setup to be puppeted by Accounts.
They are what you "see" in game. The Character class in this module
is setup to be the "default" character type created by the default
creation commands.

"""
from typing import Dict, Any, List
from evennia.objects.objects import DefaultCharacter # type: ignore
from evennia.contrib.base_systems.components import ComponentHolderMixin  # type: ignore
from evennia.utils.utils import lazy_property # type: ignore
from evennia.contrib.rpg.traits import TraitHandler  # type: ignore
from evennia import TagProperty, TagCategoryProperty
from evennia.utils.logger import log_info

from .objects import ObjectParent
from components.cultivation_component import CultivationComponent
from ..systems.tag_property_system import <PERSON>anxiaTagProperty, TagPropertyQueryManager


class Character(ObjectParent, DefaultCharacter, ComponentHolderMixin):
    """
    Modernized MUD Character Class

    This class represents the player character, built upon Evennia's
    recommended best practices using Components and Traits.

    - `ComponentHolderMixin`: Allows attaching modular `Components`.
    - `TraitHandler`: Manages all numerical stats and attributes.
    """

    def at_object_creation(self):
        """Called only when the object is first created."""
        super().at_object_creation()

        # Initialize all numerical traits
        self.initialize_traits()

        # Add components to the character
        self.components.add(CultivationComponent)

        # Set initial db flags
        self.db.is_cultivating = False
        self.db.cultivation_technique = None
        self.db.cultivation_start_time = 0

    @lazy_property
    def traits(self) -> TraitHandler:
        """Adds the TraitHandler to the character."""
        return TraitHandler(self)

    def initialize_traits(self):
        """
        Initializes all the numerical traits for the character.
        This is called only once, during character creation.
        """
        # Core Cultivation Traits
        self.traits.add("realm", "境界", "static", "练气期") # type: ignore
        self.traits.add("realm_level", "境界等级", "static", 1) # type: ignore
        self.traits.add("realm_tier", "境界阶位", "static", 1) # type: ignore
        self.traits.add("cultivation", "修为", "gauge", 0) # type: ignore

        # Core Combat/Vital Traits
        self.traits.add("hp", "生命值", "gauge", 100) # type: ignore
        self.traits.add("sp", "灵力", "gauge", 100) # type: ignore

        # Elemental Affinities (as static traits)
        self.traits.add("fire_affinity", "火系亲和", "static", 0.1) # type: ignore
        self.traits.add("water_affinity", "水系亲和", "static", 0.1) # type: ignore
        self.traits.add("earth_affinity", "土系亲和", "static", 0.1) # type: ignore
        self.traits.add("wood_affinity", "木系亲和", "static", 0.1) # type: ignore
        self.traits.add("metal_affinity", "金系亲和", "static", 0.1) # type: ignore
        
    def get_cultivation_info(self):
        """Convenience method to get cultivation info from the component."""
        if self.components.has("cultivation"):
            return self.components.cultivation.get_info()
        return {"Error": "Cultivation component not found."}

    def get_full_status(self):
        """
        Gathers and returns a complete status of the character.
        """
        return {
            "basic_info": {
                "name": self.name,
                "location": str(self.location) if self.location else "未知"
            },
            "cultivation": self.get_cultivation_info(),
            "vitals": {
                "HP": self.traits.hp.current, # type: ignore
                "SP": self.traits.sp.current # type: ignore
            }
        }


class XianxiaCharacter(Character):
    """
    仙侠世界角色类

    在现有Character基础上添加TagProperty支持，实现高性能的语义化属性查询：
    - 修为境界：练气到仙人的境界系统
    - 门派归属：角色所属门派
    - 五行属性：角色的五行倾向
    - 职业类型：剑修、法修、体修等
    - 特殊状态：闭关、历练、受伤等

    与现有Traits系统协同工作，TagProperty用于分类查询，Traits用于数值计算。
    """

    # 核心修炼属性（用于高性能查询）
    修为境界 = XianxiaTagProperty(
        category="境界等级",
        default="练气",
        xianxia_type="cultivation",
        valid_values=TagPropertyQueryManager.REALM_LEVELS
    )

    门派归属 = XianxiaTagProperty(
        category="sect_territory",
        default="无门派",
        xianxia_type="politics",
        valid_values=TagPropertyQueryManager.SECT_TERRITORIES
    )

    五行属性 = XianxiaTagProperty(
        category="elemental_type",
        default="土",
        xianxia_type="elemental",
        valid_values=TagPropertyQueryManager.ELEMENTAL_TYPES
    )

    职业类型 = XianxiaTagProperty(
        category="profession_type",
        default="散修",
        xianxia_type="profession",
        valid_values=["散修", "剑修", "法修", "体修", "丹修", "器修", "阵修", "符修"]
    )

    # 特殊状态标记
    特殊状态 = TagCategoryProperty("正常", "闭关", "历练", "受伤", "中毒", "突破", "渡劫")

    def at_object_creation(self):
        """角色创建时的初始化"""
        super().at_object_creation()

        # 设置默认TagProperty属性
        if not self.tags.get(category="境界等级"):
            self.修为境界 = "练气"

        if not self.tags.get(category="sect_territory"):
            self.门派归属 = "无门派"

        if not self.tags.get(category="elemental_type"):
            self.五行属性 = "土"

        if not self.tags.get(category="profession_type"):
            self.职业类型 = "散修"

        # 设置默认状态
        if "正常" not in self.特殊状态:
            self.特殊状态 = ["正常"]

        log_info(f"XianxiaCharacter created: {self.key} ({self.修为境界}, {self.门派归属})")

    def sync_realm_with_traits(self):
        """
        同步TagProperty境界与Traits境界

        确保TagProperty的修为境界与Traits系统的realm保持一致
        """
        if hasattr(self.traits, 'realm'):
            # 从Traits更新TagProperty
            self.修为境界 = self.traits.realm.value
        else:
            # 从TagProperty更新Traits
            if hasattr(self.traits, 'realm'):
                self.traits.realm.value = self.修为境界

    def get_realm_level(self) -> int:
        """
        获取境界等级数值

        Returns:
            int: 境界等级（0-9）
        """
        realm_levels = {
            "练气": 0, "筑基": 1, "金丹": 2, "元婴": 3, "化神": 4,
            "炼虚": 5, "合体": 6, "大乘": 7, "渡劫": 8, "仙人": 9
        }
        return realm_levels.get(self.修为境界, 0)

    def is_sect_member(self, sect_name: str) -> bool:
        """
        检查是否为指定门派成员

        Args:
            sect_name: 门派名称

        Returns:
            bool: 是否为该门派成员
        """
        return self.门派归属 == sect_name

    def get_elemental_affinity(self, element: str) -> float:
        """
        获取五行亲和度

        Args:
            element: 五行属性

        Returns:
            float: 亲和度倍数
        """
        if self.五行属性 == element:
            return 1.5  # 同属性高亲和

        # 相生关系
        generation_map = {
            "金": "水", "水": "木", "木": "火", "火": "土", "土": "金"
        }

        if generation_map.get(self.五行属性) == element:
            return 1.2  # 相生中等亲和

        # 相克关系
        restraint_map = {
            "金": "木", "木": "土", "土": "水", "水": "火", "火": "金"
        }

        if restraint_map.get(self.五行属性) == element:
            return 0.8  # 相克低亲和

        return 1.0  # 普通亲和

    def get_enhanced_status(self) -> Dict[str, Any]:
        """
        获取增强的角色状态信息

        结合原有Traits系统和新的TagProperty系统

        Returns:
            Dict: 完整的角色状态信息
        """
        base_status = self.get_full_status()

        # 添加TagProperty信息
        enhanced_status = {
            **base_status,
            "tag_realm": self.修为境界,
            "sect": self.门派归属,
            "element": self.五行属性,
            "profession": self.职业类型,
            "special_states": list(self.特殊状态),
            "realm_level": self.get_realm_level(),
        }

        return enhanced_status
